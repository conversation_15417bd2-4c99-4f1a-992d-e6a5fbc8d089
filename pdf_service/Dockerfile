FROM python:3.12-slim

# 安装 uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/
ENV UV_PYPI_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple


WORKDIR /app

# 复制项目文件
COPY pyproject.toml README.md ./
COPY src ./src
# 用 uv 安装依赖
RUN uv sync

# 复制本地 Hugging Face 模型缓存到容器
COPY hf_cache/models--ds4sd--docling-models /root/.cache/huggingface/hub/models--ds4sd--docling-models

# 暴露端口
EXPOSE 8002

# 启动应用
CMD ["uv", "run", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8002"]