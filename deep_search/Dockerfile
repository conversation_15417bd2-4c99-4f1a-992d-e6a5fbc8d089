FROM python:3.12-slim

# 安装 uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/
ENV UV_PYPI_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple


# 使用 --upgrade 选项
RUN pip install --upgrade pip setuptools wheel -i https://pypi.tuna.tsinghua.edu.cn/simple


WORKDIR /app

# 复制项目文件
COPY pyproject.toml langgraph.json langgraph_no_auth.json ./
COPY src ./src

# 复制 Docker 环境变量文件
COPY .env.docker .env

# 用 uv 安装依赖
RUN uv sync

# 暴露端口 (LangGraph 默认端口)
EXPOSE 2024

# 启动应用
CMD ["uv", "run", "langgraph", "dev", "--config", "langgraph_no_auth.json", "--host", "0.0.0.0", "--port", "2024"]