"""Dynamic model configuration service that fetches models from backend API."""

import asyncio
import logging
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta

import httpx
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class AIModelInfo(BaseModel):
    """AI Model information from backend API."""
    id: str
    name: str
    model_type: str
    model_name: str
    provider: str
    credential: str
    base_url: Optional[str] = None


class ModelConfigService:
    """Service for managing dynamic model configurations."""
    
    def __init__(self):
        self.backend_api_base = os.getenv("BACKEND_API_BASE", "http://localhost:8000/v1/api")
        self.refresh_interval = int(os.getenv("MODEL_CONFIG_REFRESH_INTERVAL", "300"))  # 5分钟
        self._models_cache: Dict[str, AIModelInfo] = {}
        self._last_refresh: Optional[datetime] = None
        self._refresh_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """Start the model configuration service."""
        logger.info("Starting model configuration service")
        await self._refresh_models()
        self._refresh_task = asyncio.create_task(self._periodic_refresh())
        
    async def stop(self):
        """Stop the model configuration service."""
        if self._refresh_task:
            self._refresh_task.cancel()
            try:
                await self._refresh_task
            except asyncio.CancelledError:
                pass
        logger.info("Model configuration service stopped")
        
    async def _periodic_refresh(self):
        """Periodically refresh model configurations."""
        while True:
            try:
                await asyncio.sleep(self.refresh_interval)
                await self._refresh_models()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic model refresh: {e}")
                
    async def _refresh_models(self):
        """Refresh model configurations from backend API."""
        try:
            async with httpx.AsyncClient() as client:
                # 获取默认provider的模型
                response = await client.get(
                    f"{self.backend_api_base}/models",
                    timeout=10.0
                )
                response.raise_for_status()
                
                models_data = response.json()
                new_cache = {}
                
                for model_data in models_data:
                    if model_data.get("provider") == "default":
                        model_info = AIModelInfo(**model_data)
                        new_cache[model_info.model_type] = model_info
                        
                self._models_cache = new_cache
                self._last_refresh = datetime.now()
                logger.info(f"Refreshed {len(new_cache)} default models from backend")
                
        except Exception as e:
            logger.error(f"Failed to refresh models from backend: {e}")
            # 如果刷新失败，保持现有缓存
            
    def get_model_by_type(self, model_type: str) -> Optional[AIModelInfo]:
        """Get model configuration by type."""
        return self._models_cache.get(model_type)
        
    def get_all_models(self) -> Dict[str, AIModelInfo]:
        """Get all cached model configurations."""
        return self._models_cache.copy()
        
    def is_cache_valid(self) -> bool:
        """Check if the cache is still valid."""
        if not self._last_refresh:
            return False
        return datetime.now() - self._last_refresh < timedelta(seconds=self.refresh_interval * 2)


# 全局单例实例
model_config_service = ModelConfigService()