"""Graph state definitions and data structures for the Deep Research agent.

This module provides backward compatibility by re-exporting the new modular state definitions.
For new code, prefer importing directly from the state module.
"""

# Import from the new modular structure
from .state import (
    AgentInputState,
    AgentState,
    ClarifyWithUser,
    ConductResearch,
    ResearchComplete,
    ResearcherOutputState,
    ResearcherState,
    ResearchQuestion,
    Summary,
    SupervisorState,
)

# Re-export for backward compatibility
__all__ = [
    "AgentInputState",
    "AgentState",
    "ClarifyWithUser",
    "ConductResearch",
    "ResearchComplete",
    "ResearcherOutputState",
    "ResearcherState",
    "ResearchQuestion",
    "Summary",
    "SupervisorState",
]