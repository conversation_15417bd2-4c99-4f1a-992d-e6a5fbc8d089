"""Configuration management for the Open Deep Research system.

This module provides backward compatibility by re-exporting the new modular configuration.
For new code, prefer importing directly from the config module.
"""

# Import from the new modular structure
from .config import Configuration, MCPConfig, SearchAPI
from .config.factory import from_runnable_config

# Add the factory method to Configuration class for backward compatibility
def _from_runnable_config(cls, config=None):
    """Wrapper to make from_runnable_config work as a classmethod."""
    return from_runnable_config(config)

Configuration.from_runnable_config = classmethod(_from_runnable_config)

# Re-export for backward compatibility
__all__ = ["Configuration", "MCPConfig", "SearchAPI"]