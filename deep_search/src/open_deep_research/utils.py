"""Utility functions and helpers for the Deep Research agent.

This module provides backward compatibility by re-exporting the new modular utilities.
For new code, prefer importing directly from the utils module.
"""

# Import from the new modular structure
from .utils import (
    anthropic_websearch_called,
    arxiv_search,
    arxiv_search_async,
    get_all_tools,
    get_api_key_for_model,
    get_base_url_for_model,
    get_config_value,
    get_model_token_limit,
    get_notes_from_tool_calls,
    get_search_tool,
    get_tavily_api_key,
    get_today_str,
    is_token_limit_exceeded,
    load_mcp_tools,
    openai_websearch_called,
    remove_up_to_last_ai_message,
    summarize_webpage,
    tavily_search,
    tavily_search_async,
    think_tool,
)

# Re-export for backward compatibility
__all__ = [
    "anthropic_websearch_called",
    "arxiv_search",
    "arxiv_search_async",
    "get_all_tools",
    "get_api_key_for_model",
    "get_base_url_for_model",
    "get_config_value",
    "get_model_token_limit",
    "get_notes_from_tool_calls",
    "get_search_tool",
    "get_tavily_api_key",
    "get_today_str",
    "is_token_limit_exceeded",
    "load_mcp_tools",
    "openai_websearch_called",
    "remove_up_to_last_ai_message",
    "summarize_webpage",
    "tavily_search",
    "tavily_search_async",
    "think_tool",
]