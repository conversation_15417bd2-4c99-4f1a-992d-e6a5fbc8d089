"""Configuration helper utilities for the Open Deep Research system."""

import os
from langchain_core.runnables import RunnableConfig


def get_config_value(value):
    """Extract value from configuration, handling enums and None values."""
    if value is None:
        return None
    if isinstance(value, str):
        return value
    elif isinstance(value, dict):
        return value
    else:
        return value.value


def get_api_key_for_model(model_name: str, config: RunnableConfig):
    """Get API key for a specific model from environment or config."""
    should_get_from_config = os.getenv("GET_API_KEYS_FROM_CONFIG", "false")
    model_name = model_name.lower()
    if should_get_from_config.lower() == "true":
        api_keys = config.get("configurable", {}).get("apiKeys", {})
        if not api_keys:
            return None
        if model_name.startswith("openai:"):
            return api_keys.get("OPENAI_API_KEY")
        elif model_name.startswith("anthropic:"):
            return api_keys.get("ANTHROPIC_API_KEY")
        elif model_name.startswith("google"):
            return api_keys.get("GOOGLE_API_KEY")
        else:
            # Default to OpenAI for custom models without prefix
            return api_keys.get("OPENAI_API_KEY")
    else:
        if model_name.startswith("openai:"): 
            return os.getenv("OPENAI_API_KEY")
        elif model_name.startswith("anthropic:"):
            return os.getenv("ANTHROPIC_API_KEY")
        elif model_name.startswith("google"):
            return os.getenv("GOOGLE_API_KEY")
        else:
            # Default to OpenAI for custom models without prefix
            return os.getenv("OPENAI_API_KEY")


def get_base_url_for_model(model_name: str, config: RunnableConfig):
    """Get base URL for a specific model from environment or config."""
    model_name = model_name.lower()
    # For OpenAI-compatible models (including custom models), use OPENAI_API_BASE
    if model_name.startswith("openai:") or not any(model_name.startswith(prefix) for prefix in ["anthropic:", "google"]):
        return os.getenv("OPENAI_API_BASE")
    # For other providers, return None to use default
    return None


def get_tavily_api_key(config: RunnableConfig):
    """Get Tavily API key from environment or config."""
    should_get_from_config = os.getenv("GET_API_KEYS_FROM_CONFIG", "false")
    if should_get_from_config.lower() == "true":
        api_keys = config.get("configurable", {}).get("apiKeys", {})
        if not api_keys:
            return None
        return api_keys.get("TAVILY_API_KEY")
    else:
        return os.getenv("TAVILY_API_KEY")