"""arXiv search functionality for the Open Deep Research system."""

import asyncio
from typing import Annotated, Dict, List, Literal

import arxiv
from langchain_community.utilities import Arxiv<PERSON><PERSON><PERSON>rapper
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import InjectedToolArg, tool

ARXIV_SEARCH_DESCRIPTION = (
    "A search engine for academic papers from arXiv.org. "
    "Useful for finding research papers, preprints, and academic literature on various topics."
)


@tool(description=ARXIV_SEARCH_DESCRIPTION)
async def arxiv_search(
    queries: List[str],
    max_results: Annotated[int, InjectedToolArg] = 5,
    sort_by: Annotated[Literal["relevance", "lastUpdatedDate", "submittedDate"], InjectedToolArg] = "relevance",
    config: RunnableConfig = None
) -> str:
    """Fetches results from arXiv search API.

    Args:
        queries (List[str]): List of search queries for academic papers
        max_results (int): Maximum number of results to return per query
        sort_by (Literal['relevance', 'lastUpdatedDate', 'submittedDate']): Sort order for results

    Returns:
        str: A formatted string of search results with paper details
    """
    search_results = await arxiv_search_async(
        queries,
        max_results=max_results,
        sort_by=sort_by,
        config=config
    )
    
    # Format the search results
    formatted_output = f"arXiv search results:\n\n"
    for query_idx, (query, results) in enumerate(search_results.items()):
        formatted_output += f"=== QUERY {query_idx + 1}: {query} ===\n\n"
        
        if not results:
            formatted_output += "No results found for this query.\n\n"
            continue
            
        for i, paper in enumerate(results):
            formatted_output += f"--- PAPER {i+1} ---\n"
            formatted_output += f"Title: {paper['title']}\n"
            formatted_output += f"Authors: <AUTHORS>
            formatted_output += f"Published: {paper['published']}\n"
            formatted_output += f"Updated: {paper['updated']}\n"
            formatted_output += f"URL: {paper['url']}\n"
            formatted_output += f"Categories: {', '.join(paper['categories'])}\n\n"
            formatted_output += f"ABSTRACT:\n{paper['summary']}\n\n"
            formatted_output += "-" * 80 + "\n\n"
    
    return formatted_output if search_results else "No valid search results found. Please try different search queries."


def _optimize_arxiv_query(query: str) -> List[str]:
    """Optimize a complex query by creating multiple search strategies.
    
    Returns a list of optimized queries to try in order of preference.
    """
    # Common stop words to remove
    stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 
                  'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 
                  'among', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
                  'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'case', 'study', 
                  'using', 'based', 'approach', 'method', 'system', 'systems'}
    
    # Extract meaningful keywords
    words = query.lower().split()
    
    # Remove years and common academic terms
    filtered_words = []
    for word in words:
        # Skip years (2020-2030 range)
        if word.isdigit() and 2020 <= int(word) <= 2030:
            continue
        # Skip year ranges like "2023-2025"
        if '-' in word and any(c.isdigit() for c in word):
            continue
        # Skip stop words
        if word not in stop_words and len(word) > 2:
            filtered_words.append(word)
    
    # Create different query strategies
    strategies = []
    if len(filtered_words) >= 3:
        # Strategy 1: Top 3 most important terms with AND
        key_terms = filtered_words[:3]
        strategies.append(' AND '.join(key_terms))
        
        # Strategy 2: Top 2 terms with AND
        strategies.append(' AND '.join(key_terms[:2]))
        
        # Strategy 3: All terms as phrase (less strict)
        strategies.append(' '.join(filtered_words[:4]))
        
        # Strategy 4: Individual key terms
        strategies.append(filtered_words[0])
    elif len(filtered_words) >= 2:
        # For shorter queries
        strategies.append(' AND '.join(filtered_words))
        strategies.append(' '.join(filtered_words))
        strategies.append(filtered_words[0])
    else:
        # Single term or very short query
        strategies.append(query)
    
    return strategies


def _search_arxiv_langchain(query: str, max_results: int) -> List[Dict]:
    """Use LangChain ArxivAPIWrapper for search."""
    try:
        arxiv_wrapper = ArxivAPIWrapper(
            top_k_results=max_results,
            load_max_docs=max_results,
            load_all_available_meta=True
        )
        
        # LangChain wrapper returns a string, we need to parse it
        result_text = arxiv_wrapper.run(query)
        
        # For now, return a simple format - this could be enhanced to parse the text
        papers = [{
            'title': f"LangChain Search Results for: {query}",
            'authors': ['Various'],
            'summary': result_text[:500] + "..." if len(result_text) > 500 else result_text,
            'published': '2024-01-01',
            'updated': '2024-01-01',
            'url': 'https://arxiv.org',
            'pdf_url': 'https://arxiv.org',
            'categories': ['cs.AI'],
            'primary_category': 'cs.AI',
            'search_method': 'langchain'
        }]
        return papers
    except Exception as e:
        print(f"Error with LangChain arXiv search for '{query}': {str(e)}")
        return []


def _search_arxiv_direct(query: str, max_results: int, sort_criterion) -> List[Dict]:
    """Direct arXiv search without query optimization."""
    try:
        client = arxiv.Client()
        search = arxiv.Search(
            query=query,
            max_results=max_results,
            sort_by=sort_criterion,
            sort_order=arxiv.SortOrder.Descending
        )
        
        papers = []
        for paper in client.results(search):
            paper_data = {
                'title': paper.title.strip(),
                'authors': [author.name for author in paper.authors],
                'summary': paper.summary.strip().replace('\n', ' '),
                'published': paper.published.strftime('%Y-%m-%d'),
                'updated': paper.updated.strftime('%Y-%m-%d') if paper.updated else paper.published.strftime('%Y-%m-%d'),
                'url': paper.entry_id,
                'pdf_url': paper.pdf_url,
                'categories': paper.categories,
                'primary_category': paper.primary_category,
                'search_method': 'direct'
            }
            papers.append(paper_data)
        
        return papers
    except Exception as e:
        print(f"Error with direct arXiv search for '{query}': {str(e)}")
        return []


def _search_arxiv_optimized(query: str, max_results: int, sort_criterion) -> List[Dict]:
    """Synchronous helper function to search arXiv with query optimization.
    
    This will be run in a separate thread to avoid blocking the event loop.
    """
    try:
        # Get optimized query strategies
        query_strategies = _optimize_arxiv_query(query)
        client = arxiv.Client()
        all_papers = []
        papers_seen = set()  # To avoid duplicates
        
        # Try each strategy until we get enough results
        for strategy in query_strategies:
            if len(all_papers) >= max_results:
                break
                
            try:
                print(f"Trying arXiv search strategy: '{strategy}'")
                search = arxiv.Search(
                    query=strategy,
                    max_results=max_results * 2,  # Get more to filter duplicates
                    sort_by=sort_criterion,
                    sort_order=arxiv.SortOrder.Descending
                )
                
                # Execute search and collect results
                for paper in client.results(search):
                    # Skip duplicates
                    if paper.entry_id in papers_seen:
                        continue
                    papers_seen.add(paper.entry_id)
                    
                    paper_data = {
                        'title': paper.title.strip(),
                        'authors': [author.name for author in paper.authors],
                        'summary': paper.summary.strip().replace('\n', ' '),
                        'published': paper.published.strftime('%Y-%m-%d'),
                        'updated': paper.updated.strftime('%Y-%m-%d') if paper.updated else paper.published.strftime('%Y-%m-%d'),
                        'url': paper.entry_id,
                        'pdf_url': paper.pdf_url,
                        'categories': paper.categories,
                        'primary_category': paper.primary_category,
                        'search_strategy': strategy  # Track which strategy found this
                    }
                    all_papers.append(paper_data)
                    
                    if len(all_papers) >= max_results:
                        break
                
                # If we found results with this strategy, we can stop or continue for diversity
                if all_papers:
                    print(f"Found {len(all_papers)} papers with strategy: '{strategy}'")
                    
            except Exception as e:
                print(f"Error with arXiv search strategy '{strategy}': {str(e)}")
                continue
        
        # Return up to max_results papers
        return all_papers[:max_results]
        
    except Exception as e:
        print(f"Error in optimized arXiv search for query '{query}': {str(e)}")
        return []


def _search_arxiv_sync(query: str, max_results: int, sort_criterion, method: str = "optimized") -> List[Dict]:
    """Synchronous helper function to search arXiv using different methods.
    
    This will be run in a separate thread to avoid blocking the event loop.
    """
    if method == "langchain":
        return _search_arxiv_langchain(query, max_results)
    elif method == "direct":
        return _search_arxiv_direct(query, max_results, sort_criterion)
    else:  # optimized (default)
        return _search_arxiv_optimized(query, max_results, sort_criterion)


async def arxiv_search_async(
    search_queries: List[str], 
    max_results: int = 5, 
    sort_by: str = "relevance", 
    config: RunnableConfig = None
) -> Dict[str, List[Dict]]:
    """Asynchronously search arXiv for academic papers.

    Args:
        search_queries: List of search terms
        max_results: Maximum results per query
        sort_by: Sort criteria for results
        config: Runtime configuration

    Returns:
        Dictionary mapping queries to their results
    """
    # Get search method from configuration
    from open_deep_research.configuration import Configuration
    config_obj = Configuration.from_runnable_config(config)
    search_method = getattr(config_obj, 'arxiv_search_method', 'optimized')
    
    # Map sort_by parameter to arxiv.SortCriterion
    sort_criteria = {
        "relevance": arxiv.SortCriterion.Relevance,
        "lastUpdatedDate": arxiv.SortCriterion.LastUpdatedDate,
        "submittedDate": arxiv.SortCriterion.SubmittedDate
    }
    sort_criterion = sort_criteria.get(sort_by, arxiv.SortCriterion.Relevance)
    
    results = {}
    print(f"Using arXiv search method: {search_method}")
    
    # Run searches concurrently using asyncio.to_thread to avoid blocking
    search_tasks = []
    for query in search_queries:
        task = asyncio.to_thread(_search_arxiv_sync, query, max_results, sort_criterion, search_method)
        search_tasks.append((query, task))
    
    # Wait for all searches to complete
    for query, task in search_tasks:
        papers = await task
        results[query] = papers
    
    return results