"""Search tool configuration utilities for the Open Deep Research system."""

from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool

from open_deep_research.configuration import Configuration, SearchAPI
from open_deep_research.state import ResearchComplete
from open_deep_research.utils.arxiv_search import arxiv_search
from open_deep_research.utils.mcp_tools import load_mcp_tools
from open_deep_research.utils.reflection_tools import think_tool
from open_deep_research.utils.config_helpers import get_config_value


async def get_search_tool(search_api: SearchAPI):
    """Configure and return search tools based on the specified API provider.
    
    Args:
        search_api: The search API provider to use (arXiv, Mock, or None)
        
    Returns:
        List of configured search tool objects for the specified provider
    """
    if search_api == SearchAPI.ARXIV:
        search_tool = arxiv_search
        search_tool.metadata = {**(search_tool.metadata or {}), "type": "search", "name": "arxiv_search"}
        return [search_tool]
    elif search_api == SearchAPI.MOCK:
        from open_deep_research.mock_search import mock_arxiv_search
        search_tool = mock_arxiv_search
        # 修改工具名称以匹配期望的arxiv_search
        search_tool.name = "arxiv_search"
        search_tool.metadata = {**(search_tool.metadata or {}), "type": "search", "name": "arxiv_search"}
        return [search_tool]
    elif search_api == SearchAPI.NONE:
        return []
        
    # Default fallback for unknown search API types
    return []


async def get_all_tools(config: RunnableConfig):
    """Assemble complete toolkit including research, search, and MCP tools.
    
    Args:
        config: Runtime configuration specifying search API and MCP settings
        
    Returns:
        List of all configured and available tools for research operations
    """
    # Start with core research tools
    tools = [tool(ResearchComplete), think_tool]
    
    # Add configured search tools
    configurable = Configuration.from_runnable_config(config)
    search_api = SearchAPI(get_config_value(configurable.search_config.search_api))
    search_tools = await get_search_tool(search_api)
    tools.extend(search_tools)
    
    # Track existing tool names to prevent conflicts
    existing_tool_names = {
        tool.name if hasattr(tool, "name") else tool.get("name", "web_search") 
        for tool in tools
    }
    
    # Add MCP tools if configured
    mcp_tools = await load_mcp_tools(config, existing_tool_names)
    tools.extend(mcp_tools)
    
    return tools