"""Base configuration class for the Open Deep Research system."""

from typing import Optional
from pydantic import BaseModel, Field

from .mcp import MCPConfig
from .models import ModelConfig
from .search import SearchConfig


class Configuration(BaseModel):
    """Main configuration class for the Deep Research agent."""
    
    # General Configuration
    max_structured_output_retries: int = Field(
        default=3,
        metadata={
            "x_oap_ui_config": {
                "type": "number",
                "default": 3,
                "min": 1,
                "max": 10,
                "description": "Maximum number of retries for structured output calls from models"
            }
        }
    )
    
    allow_clarification: bool = Field(
        default=True,
        metadata={
            "x_oap_ui_config": {
                "type": "boolean",
                "default": True,
                "description": "Whether to allow the researcher to ask the user clarifying questions before starting research"
            }
        }
    )
    
    max_concurrent_research_units: int = Field(
        default=5,
        metadata={
            "x_oap_ui_config": {
                "type": "slider",
                "default": 5,
                "min": 1,
                "max": 20,
                "step": 1,
                "description": "Maximum number of research units to run concurrently. This will allow the researcher to use multiple sub-agents to conduct research. Note: with more concurrency, you may run into rate limits."
            }
        }
    )

    # Research Configuration
    max_researcher_iterations: int = Field(
        default=3,
        metadata={
            "x_oap_ui_config": {
                "type": "slider",
                "default": 3,
                "min": 1,
                "max": 10,
                "step": 1,
                "description": "Maximum number of research iterations for the Research Supervisor. This is the number of times the Research Supervisor will reflect on the research and ask follow-up questions."
            }
        }
    )
    
    max_react_tool_calls: int = Field(
        default=5,
        metadata={
            "x_oap_ui_config": {
                "type": "slider",
                "default": 5,
                "min": 1,
                "max": 30,
                "step": 1,
                "description": "Maximum number of tool calling iterations to make in a single researcher step."
            }
        }
    )

    # Embedded configurations
    search_config: SearchConfig = Field(default_factory=SearchConfig)
    models_config: ModelConfig = Field(default_factory=ModelConfig)

    # MCP server configuration
    mcp_config: Optional[MCPConfig] = Field(
        default=None,
        optional=True,
        metadata={
            "x_oap_ui_config": {
                "type": "mcp",
                "description": "MCP server configuration"
            }
        }
    )
    
    mcp_prompt: Optional[str] = Field(
        default=None,
        optional=True,
        metadata={
            "x_oap_ui_config": {
                "type": "text",
                "description": "Any additional instructions to pass along to the Agent regarding the MCP tools that are available to it."
            }
        }
    )

    # Backward compatibility properties
    @property
    def search_api(self):
        """Backward compatibility for search_api access."""
        return self.search_config.search_api
    
    @property
    def arxiv_search_method(self):
        """Backward compatibility for arxiv_search_method access."""
        return self.search_config.arxiv_search_method
    
    @property
    def summarization_model(self):
        """Backward compatibility for summarization_model access."""
        return self.models_config.summarization_model
    
    @property
    def summarization_model_max_tokens(self):
        """Backward compatibility for summarization_model_max_tokens access."""
        return self.models_config.summarization_model_max_tokens
    
    @property
    def research_model(self):
        """Backward compatibility for research_model access."""
        return self.models_config.research_model
    
    @property
    def research_model_max_tokens(self):
        """Backward compatibility for research_model_max_tokens access."""
        return self.models_config.research_model_max_tokens
    
    @property
    def compression_model(self):
        """Backward compatibility for compression_model access."""
        return self.models_config.compression_model
    
    @property
    def compression_model_max_tokens(self):
        """Backward compatibility for compression_model_max_tokens access."""
        return self.models_config.compression_model_max_tokens
    
    @property
    def final_report_model(self):
        """Backward compatibility for final_report_model access."""
        return self.models_config.final_report_model
    
    @property
    def final_report_model_max_tokens(self):
        """Backward compatibility for final_report_model_max_tokens access."""
        return self.models_config.final_report_model_max_tokens

    model_config = {"arbitrary_types_allowed": True}