"""Configuration factory methods for the Open Deep Research system."""

import os
from typing import Any, Optional

from langchain_core.runnables import RunnableConfig

from .base import Configuration


def from_runnable_config(config: Optional[RunnableConfig] = None) -> Configuration:
    """Create a Configuration instance from a RunnableConfig."""
    configurable = config.get("configurable", {}) if config else {}
    field_names = list(Configuration.model_fields.keys())
    values: dict[str, Any] = {
        field_name: os.environ.get(field_name.upper(), configurable.get(field_name))
        for field_name in field_names
    }
    return Configuration(**{k: v for k, v in values.items() if v is not None})