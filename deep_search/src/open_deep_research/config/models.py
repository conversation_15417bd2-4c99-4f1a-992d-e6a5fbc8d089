"""Model configuration for the Open Deep Research system."""

import os
from typing import Optional
from pydantic import BaseModel, Field

from ..services.model_config_service import model_config_service


class ModelConfig(BaseModel):
    """Configuration for AI models used in the research system."""
    
    # Fallback configurations (used when dynamic config is not available)
    _fallback_summarization_model: str = "qwen3-30b-a3b-thinking-2507-mlx"
    _fallback_research_model: str = "qwen3-30b-a3b-instruct-2507"
    _fallback_compression_model: str = "qwen3-30b-a3b-instruct-2507"
    _fallback_final_report_model: str = "qwen3-30b-a3b-thinking-2507-mlx"
    
    # Token limits
    summarization_model_max_tokens: int = Field(
        default=8192,
        metadata={
            "x_oap_ui_config": {
                "type": "number",
                "default": 8192,
                "description": "Maximum output tokens for summarization model"
            }
        }
    )
    
    research_model_max_tokens: int = Field(
        default=10000,
        metadata={
            "x_oap_ui_config": {
                "type": "number",
                "default": 10000,
                "description": "Maximum output tokens for research model"
            }
        }
    )
    
    compression_model_max_tokens: int = Field(
        default=8192,
        metadata={
            "x_oap_ui_config": {
                "type": "number",
                "default": 8192,
                "description": "Maximum output tokens for compression model"
            }
        }
    )
    
    final_report_model_max_tokens: int = Field(
        default=10000,
        metadata={
            "x_oap_ui_config": {
                "type": "number",
                "default": 10000,
                "description": "Maximum output tokens for final report model"
            }
        }
    )
    
    def _get_dynamic_model(self, model_type: str, fallback: str) -> str:
        """Get model configuration dynamically from service or fallback."""
        try:
            model_info = model_config_service.get_model_by_type(model_type)
            if model_info:
                # 构建模型标识符，格式: provider:model_name
                if model_info.base_url:
                    # 如果有自定义base_url，使用openai兼容格式
                    os.environ["OPENAI_API_BASE"] = model_info.base_url
                    os.environ["OPENAI_API_KEY"] = model_info.credential
                    return f"openai:{model_info.model_name}"
                else:
                    # 使用provider:model_name格式
                    return f"{model_info.provider}:{model_info.model_name}"
        except Exception as e:
            # 记录错误但不中断服务
            import logging
            logging.getLogger(__name__).warning(f"Failed to get dynamic model for {model_type}: {e}")
        
        return fallback
    
    @property
    def summarization_model(self) -> str:
        """Get summarization model configuration."""
        return self._get_dynamic_model("summarization", self._fallback_summarization_model)
    
    @property
    def research_model(self) -> str:
        """Get research model configuration."""
        return self._get_dynamic_model("research", self._fallback_research_model)
    
    @property
    def compression_model(self) -> str:
        """Get compression model configuration."""
        return self._get_dynamic_model("compression", self._fallback_compression_model)
    
    @property
    def final_report_model(self) -> str:
        """Get final report model configuration."""
        return self._get_dynamic_model("final_report", self._fallback_final_report_model)