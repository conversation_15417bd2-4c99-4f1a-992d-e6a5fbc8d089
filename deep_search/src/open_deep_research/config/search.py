"""Search API configuration for the Open Deep Research system."""

from enum import Enum
from pydantic import BaseModel, Field, field_validator


class SearchAPI(Enum):
    """Enumeration of available search API providers."""
    
    ARXIV = "arxiv"
    MOCK = "mock"  # For testing when network is unstable
    NONE = "none"


class SearchConfig(BaseModel):
    """Configuration for search functionality."""
    
    search_api: SearchAPI = Field(
        default=SearchAPI.ARXIV,
        metadata={
            "x_oap_ui_config": {
                "type": "select",
                "default": "arxiv",
                "description": "Search API to use for research. Using arXiv for academic paper search capabilities.",
                "options": [
                    {"label": "arXiv", "value": SearchAPI.ARXIV.value},
                    {"label": "Mock (Testing)", "value": SearchAPI.MOCK.value},
                    {"label": "None", "value": SearchAPI.NONE.value}
                ]
            }
        }
    )
    
    arxiv_search_method: str = Field(
        default="optimized",
        metadata={
            "x_oap_ui_config": {
                "type": "select",
                "default": "optimized",
                "description": "Method for arXiv search: 'optimized' uses query optimization strategies, 'langchain' uses Lang<PERSON>hain wrapper, 'direct' uses direct queries",
                "options": [
                    {"label": "Optimized (Recommended)", "value": "optimized"},
                    {"label": "LangChain Wrapper", "value": "langchain"},
                    {"label": "Direct Query", "value": "direct"}
                ]
            }
        }
    )

    @field_validator('search_api', mode='before')
    @classmethod
    def validate_search_api(cls, v):
        """Validate search_api field and handle deprecated values."""
        if isinstance(v, str):
            # Handle deprecated search API values
            deprecated_apis = {'tavily', 'anthropic', 'openai'}
            if v.lower() in deprecated_apis:
                print(f"Warning: Search API '{v}' is no longer supported. Falling back to 'arxiv'.")
                return SearchAPI.ARXIV.value
            
            # Try to find the enum value
            for api in SearchAPI:
                if api.value == v.lower():
                    return v
            
            # If not found, default to arxiv
            print(f"Warning: Unknown search API '{v}'. Falling back to 'arxiv'.")
            return SearchAPI.ARXIV.value
        return v