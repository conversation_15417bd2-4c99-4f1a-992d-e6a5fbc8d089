"""System prompts and prompt templates for the Deep Research agent.

This module provides backward compatibility by re-exporting the new modular prompts.
For new code, prefer importing directly from the prompts module.
"""

# Import from the new modular structure
from .prompts import (
    clarify_with_user_instructions,
    compress_research_simple_human_message,
    compress_research_system_prompt,
    final_report_generation_prompt,
    lead_researcher_prompt,
    research_system_prompt,
    summarize_webpage_prompt,
    transform_messages_into_research_topic_prompt,
)

# Re-export for backward compatibility
__all__ = [
    "clarify_with_user_instructions",
    "compress_research_simple_human_message",
    "compress_research_system_prompt",
    "final_report_generation_prompt",
    "lead_researcher_prompt",
    "research_system_prompt",
    "summarize_webpage_prompt",
    "transform_messages_into_research_topic_prompt",
]