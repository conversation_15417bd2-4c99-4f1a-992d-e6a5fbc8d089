"""Mock search tools for testing when network is unstable."""

from typing import List, Dict, Annotated
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig


# Mock data for testing
MOCK_PAPERS = {
    "agentic digital twin": [
        {
            'title': 'Multi-Agent Digital Twinning for Industrial Systems',
            'authors': ['<PERSON>', '<PERSON>', '<PERSON>'],
            'summary': 'This paper presents a novel approach to digital twin technology using multi-agent systems for industrial automation. The proposed framework enables real-time monitoring and control of complex manufacturing processes through intelligent agent coordination.',
            'published': '2024-12-15',
            'updated': '2024-12-15',
            'url': 'http://arxiv.org/abs/2412.15001',
            'pdf_url': 'http://arxiv.org/pdf/2412.15001.pdf',
            'categories': ['cs.AI', 'cs.MA', 'cs.SY'],
            'primary_category': 'cs.AI',
            'search_method': 'mock'
        },
        {
            'title': 'Agentic Frameworks for Autonomous Digital Twin Operations',
            'authors': ['<PERSON>', '<PERSON>', '<PERSON>'],
            'summary': 'We introduce an agentic framework that enables autonomous operation of digital twins in smart manufacturing environments. The system uses reinforcement learning agents to optimize production parameters and predict maintenance needs.',
            'published': '2024-11-20',
            'updated': '2024-11-20',
            'url': 'http://arxiv.org/abs/2411.20002',
            'pdf_url': 'http://arxiv.org/pdf/2411.20002.pdf',
            'categories': ['cs.AI', 'cs.RO', 'cs.LG'],
            'primary_category': 'cs.AI',
            'search_method': 'mock'
        }
    ],
    "transformer architecture": [
        {
            'title': 'Efficient Transformer Architectures for Long Sequences',
            'authors': ['Smith John', 'Brown Alice', 'Davis Bob'],
            'summary': 'This work proposes new efficient transformer architectures that can handle very long sequences with linear complexity. We introduce sparse attention mechanisms and hierarchical processing to achieve state-of-the-art performance on long-range tasks.',
            'published': '2024-10-10',
            'updated': '2024-10-10',
            'url': 'http://arxiv.org/abs/2410.10001',
            'pdf_url': 'http://arxiv.org/pdf/2410.10001.pdf',
            'categories': ['cs.LG', 'cs.CL', 'cs.AI'],
            'primary_category': 'cs.LG',
            'search_method': 'mock'
        },
        {
            'title': 'Dynamic Attention Mechanisms in Modern Transformers',
            'authors': ['Johnson Mary', 'Wilson Tom', 'Garcia Ana'],
            'summary': 'We present dynamic attention mechanisms that adapt their computation based on input complexity. This approach reduces computational overhead while maintaining or improving model performance across various natural language processing tasks.',
            'published': '2024-09-25',
            'updated': '2024-09-25',
            'url': 'http://arxiv.org/abs/2409.25003',
            'pdf_url': 'http://arxiv.org/pdf/2409.25003.pdf',
            'categories': ['cs.CL', 'cs.LG', 'cs.AI'],
            'primary_category': 'cs.CL',
            'search_method': 'mock'
        }
    ]
}

def find_relevant_papers(query: str, max_results: int = 5) -> List[Dict]:
    """Find mock papers relevant to the query."""
    query_lower = query.lower()
    
    # Simple keyword matching
    relevant_papers = []
    
    for topic, papers in MOCK_PAPERS.items():
        topic_words = topic.split()
        query_words = query_lower.split()
        
        # Check if any topic words are in the query
        if any(word in query_lower for word in topic_words) or any(word in topic for word in query_words):
            relevant_papers.extend(papers[:max_results])
    
    # If no specific matches, return some general papers
    if not relevant_papers:
        all_papers = []
        for papers in MOCK_PAPERS.values():
            all_papers.extend(papers)
        relevant_papers = all_papers[:max_results]
    
    return relevant_papers[:max_results]

@tool(description="Mock arXiv search for testing when network is unstable")
async def mock_arxiv_search(
    queries: List[str],
    max_results: Annotated[int, "Maximum results per query"] = 5,
    sort_by: Annotated[str, "Sort order"] = "relevance",
    config: RunnableConfig = None
) -> str:
    """Mock arXiv search that returns predefined results for testing.

    Args:
        queries (List[str]): List of search queries for academic papers
        max_results (int): Maximum number of results to return per query
        sort_by (str): Sort order for results

    Returns:
        str: A formatted string of mock search results
    """
    print(f"🔧 Using mock arXiv search (network testing mode)")
    
    formatted_output = f"arXiv search results (MOCK DATA):\n\n"
    
    for query_idx, query in enumerate(queries):
        formatted_output += f"=== QUERY {query_idx + 1}: {query} ===\n\n"
        
        # Find relevant mock papers
        papers = find_relevant_papers(query, max_results)
        
        if not papers:
            formatted_output += "No results found for this query.\n\n"
            continue
            
        for i, paper in enumerate(papers):
            formatted_output += f"--- PAPER {i+1} ---\n"
            formatted_output += f"Title: {paper['title']}\n"
            formatted_output += f"Authors: <AUTHORS>
            formatted_output += f"Published: {paper['published']}\n"
            formatted_output += f"Updated: {paper['updated']}\n"
            formatted_output += f"URL: {paper['url']}\n"
            formatted_output += f"Categories: {', '.join(paper['categories'])}\n\n"
            formatted_output += f"ABSTRACT:\n{paper['summary']}\n\n"
            formatted_output += "-" * 80 + "\n\n"
    
    return formatted_output