"""Prompt templates for the Open Deep Research system."""

from .clarification import clarify_with_user_instructions
from .compression import (
    compress_research_simple_human_message,
    compress_research_system_prompt,
)
from .final_report import final_report_generation_prompt
from .research_brief import transform_messages_into_research_topic_prompt
from .researcher import research_system_prompt
from .summarization import summarize_webpage_prompt
from .supervisor import lead_researcher_prompt

__all__ = [
    "clarify_with_user_instructions",
    "compress_research_simple_human_message",
    "compress_research_system_prompt",
    "final_report_generation_prompt",
    "lead_researcher_prompt",
    "research_system_prompt",
    "summarize_webpage_prompt",
    "transform_messages_into_research_topic_prompt",
]