"""Application lifecycle management."""

import asyncio
import logging
from contextlib import asynccontextmanager

from .services.model_config_service import model_config_service

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan_manager():
    """Manage application lifecycle events."""
    # 启动时初始化服务
    logger.info("Starting Open Deep Research services")
    try:
        await model_config_service.start()
        logger.info("All services started successfully")
        yield
    finally:
        # 关闭时清理服务
        logger.info("Shutting down Open Deep Research services")
        await model_config_service.stop()
        logger.info("All services stopped")


async def initialize_services():
    """Initialize all services for non-async contexts."""
    await model_config_service.start()