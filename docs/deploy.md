# Docker 与部署说明

## 开发环境部署


```bash
# start supabase
supabase start --ignore-health-check
```


```bash
./docker-start-prod.sh --dev-build
# 或
./docker-start-prod.sh -db
```

## 生产环境部署

```bash
./docker-start-prod.sh --build
# 或
./docker-start-prod.sh -b
```

## 服务端口
- 前端: 5173 (开发) / 8080 (生产)
- 后端: 8000 (开发) / 8080/api (生产)
- PDF: 8002 (开发) / 8080/pdf-service (生产)
- Agent: 8011 (开发) / 8080/agent (生产)

## 常见问题
- 端口冲突：检查并修改 docker-compose.yml
- 数据库连接：确认 Supabase 服务已启动
- 镜像拉取慢：提前 pull 或用代理

## 环境变量
- 详见各服务 .env.example 文件

## 其它说明
- 详细部署脚本与配置请查阅 docker/ 目录。 