# Bug

## 已修复
[x] 1. 微博"加载更多"出错(lcf issued （2025-0715 09:28）)
    > fixed by <PERSON><PERSON><PERSON> (2025-0715 10:39), 提供导航控制栏，更新了微博页面样式，右边栏提供折叠，搜索效率提升。

[x] 2. Reddit Style
    [x] 2.1 跟贴功能（无限嵌套）
    [x] 2.2 跟帖删除
    [x] 2.3 Full Reddit风格

[x] 3. Context的微博搜索没有完成
[x] 4. Setting中板块管理的版主没有显示，selected没有实现

[ ] Chat History并入Academic页
[ ] Acadmic页加入Markdown Editor/Web Presentation（参考Grok？）
[ ] Search Agent改造（打造一个Agentic Search？）
    - [ ] 搜索按钮的Switch太繁琐，使用一个按钮toggle就可以
    - [ ] 搜索引擎pool直接在后台，前端不需要人工选择，简洁操作

[ ] 模型设置页，不能修改模型名称

[x] What's new?[tobe publish]
    [ ] RSS 作为上下文的重要窗口
        -[x] TL;DR tech前端实现功能（目前disabled，在home组件中注释）
        -[ ] https://github.com/cooderl/wewe-rss/blob/main/README.md
        -[ ] weibo: https://github.com/dataabc/weibo-crawler?tab=readme-ov-file
    [ ] Context collector

[ ] Reddit功能完善
    - [x] 跟帖没有及时刷新
    - [ ] 粘贴图片到对话框
    - [x] Channel Bar图标使用emoji,Background color存储在数据库中