# 项目结构与简介

本项目为 3stooges-portal-v2，致力于打造一个集学术研究、AI对话、知识管理于一体的多服务平台。
以HIL（Human-in-Loop）为核心,将研究自由权还给用户, 并通过AI技术赋能用户实现更高效的研究。

## 主要目录结构

- backend/         # 后端服务（FastAPI, Alembic, SQLModel）
- pdf_service/     # PDF 文档解析与转换服务
- agent/           # Agent Memory 服务
- frontend/        # 前端应用（React, Vite, Tailwind）
- frontend-memory/ # Memory 前端
- docker/          # 生产环境 Docker 配置
- supabase/        # Supabase 相关配置

## 主要功能

- 用户认证与团队管理
- AI 对话与知识库
- PDF/文档解析与摘要
- 学术微博与灵感管理
- 图表与思维链可视化
- 多模型与多搜索引擎集成

详细功能请见各子文档。 

## 代码行数统计报告(2025.07.16)

总体统计
- 总代码文件数: 310 个
- 总代码行数: 59,421 行
按文件类型分类
- Python 文件 (.py): 7,820 行
- TypeScript/JavaScript 文件 (.ts, .tsx, .js, .jsx): 36,613 行
- CSS/样式文件 (.css, .scss, .sass, .less): 1,345 行
- 配置文件 (.json, .yml, .yaml, .toml): 13,421 行
- HTML 文件 (.html): 391 行
- Shell 脚本 (.sh): 1,125 行