FROM python:3.12-slim

COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/
ENV UV_PYPI_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple

WORKDIR /app

# 复制项目文件
COPY pyproject.toml alembic.ini ./
COPY alembic ./alembic
# 在开发环境下，app目录通过volume挂载，所以这里不复制
# COPY app ./app

# 复制 Docker 环境变量文件
COPY .env.docker .env


# 安装基础依赖
# 使用 --upgrade 选项
RUN pip install --upgrade pip setuptools wheel -i https://pypi.tuna.tsinghua.edu.cn/simple

# 安装项目依赖
# 用 uv 安装依赖
RUN uv sync


COPY start.sh /start.sh
RUN chmod +x /start.sh

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["/start.sh"]
