from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING
from uuid import UUID, uuid4
from sqlmodel import SQLModel, Field, Relationship
from enum import Enum

if TYPE_CHECKING:
    from .user import User
    from .post import Post
    from .channel import Channel

class NotificationType(str, Enum):
    MENTION = "mention"  # @ 提醒
    LIKE = "like"        # 点赞通知
    COMMENT = "comment"  # 评论通知
    FOLLOW = "follow"    # 关注通知
    CHANNEL_POST = "channel_post"  # 关注的板块有新帖子

class Notification(SQLModel, table=True):
    __tablename__ = "notifications"

    id: UUID = Field(default_factory=uuid4, primary_key=True, index=True)
    type: NotificationType = Field(default=NotificationType.MENTION)
    content: Optional[str] = Field(default=None)  # @ 提醒的具体内容
    
    # 用户关系
    user_id: UUID = Field(foreign_key="user.id")  # 被通知的用户
    sender_id: UUID = Field(foreign_key="user.id")  # 发送通知的用户
    
    # 帖子关系
    target_post_id: Optional[UUID] = Field(default=None, foreign_key="posts.id")  # 目标帖子
    source_post_id: Optional[UUID] = Field(default=None, foreign_key="posts.id")  # 来源帖子
    source_comment_id: Optional[UUID] = Field(default=None, foreign_key="posts.id")  # 来源评论
    
    # 板块关系
    channel_id: Optional[UUID] = Field(default=None, foreign_key="channels.id")  # 相关板块
    
    # 状态
    is_read: bool = Field(default=False, index=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: Optional[datetime] = Field(default=None)

    # 关系（使用字符串引用避免循环导入）
    user: Optional["User"] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Notification.user_id]"}
    )
    sender: Optional["User"] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Notification.sender_id]"}
    )
    target_post: Optional["Post"] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Notification.target_post_id]"}
    )
    source_post: Optional["Post"] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Notification.source_post_id]"}
    )
    source_comment: Optional["Post"] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Notification.source_comment_id]"}
    )
    channel: Optional["Channel"] = Relationship()