from datetime import datetime
from typing import Optional, TYPE_CHECKING
from uuid import UUID, uuid4
from sqlmodel import Field, Relationship, SQLModel

if TYPE_CHECKING:
    from .user import User

class OpenSourceProject(SQLModel, table=True):
    __tablename__ = "open_source_projects"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    name: str
    description: Optional[str] = None
    github_url: str
    image_url: Optional[str] = None
    rate: Optional[float] = 0
    is_private: bool = False
    creator_id: UUID = Field(foreign_key="user.id")
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = Field(default=None, sa_column_kwargs={"onupdate": datetime.utcnow})

    creator: Optional["User"] = Relationship(back_populates="open_source_projects")
    discussions: list["OpenSourceDiscussion"] = Relationship(back_populates="project")

class OpenSourceDiscussion(SQLModel, table=True):
    __tablename__ = "open_source_discussions"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    project_id: UUID = Field(foreign_key="open_source_projects.id")
    user_id: UUID = Field(foreign_key="user.id")
    content: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    project: Optional[OpenSourceProject] = Relationship(back_populates="discussions")
    user: Optional["User"] = Relationship() 