from datetime import datetime, timezone
from typing import List, Optional, TYPE_CHECKING
from uuid import UUID, uuid4
from sqlmodel import SQLModel, Field, Relationship
from enum import Enum

if TYPE_CHECKING:
    from .channel import Channel

class PostType(str, Enum):
    NORMAL = "NORMAL"  # 普通帖子
    COMMENT = "COMMENT"  # 评论帖子

class Post(SQLModel, table=True):
    __tablename__ = "posts"

    id: UUID = Field(default_factory=uuid4, primary_key=True, index=True)
    content: str = Field(max_length=2000, nullable=False)
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    owner_id: UUID = Field(foreign_key="user.id", nullable=False)
    # 添加类型字段，默认为普通帖子
    type: PostType = Field(default=PostType.NORMAL, nullable=False)
    # 可以添加一个字段存储原帖ID（对于评论类型）
    parent_id: Optional[UUID] = Field(default=None, foreign_key="posts.id", nullable=True)
    # 添加板块关系
    channel_id: Optional[UUID] = Field(default=None, foreign_key="channels.id", nullable=True)

    # 关系
    channel: Optional["Channel"] = Relationship(back_populates="posts")
    attachments: List["PostAttached"] = Relationship(back_populates="post")
    likes: List["PostLike"] = Relationship(back_populates="post")
    retweets: List["PostRetweet"] = Relationship(
        back_populates="post",
        sa_relationship_kwargs={"foreign_keys": "PostRetweet.post_id"}  # 使用sa_relationship_kwargs
    )
    # 添加一个新的关系来获取以此帖子为原始帖子的所有跟帖
    original_retweets: List["PostRetweet"] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "PostRetweet.original_post_id"}
    )

    # 添加父子关系
    parent: Optional["Post"] = Relationship(
        back_populates="comments",
        sa_relationship_kwargs={"remote_side": "Post.id"}
    )
    comments: List["Post"] = Relationship(back_populates="parent")

class PostAttached(SQLModel, table=True):
    __tablename__ = "post_attached"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    post_id: UUID = Field(foreign_key="posts.id")
    attached_id: UUID = Field(nullable=False)  # 存储diagram、bookmark、document等的ID
    attached_type: str = Field(max_length=50, nullable=False)  # 附件类型：diagram, document, bookmark等
    post: Optional[Post] = Relationship(back_populates="attachments")

class PostLike(SQLModel, table=True):
    __tablename__ = "post_likes"

    user_id: UUID = Field(foreign_key="user.id", primary_key=True)
    post_id: UUID = Field(foreign_key="posts.id", primary_key=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    post: Optional[Post] = Relationship(back_populates="likes")

class PostRetweet(SQLModel, table=True):
    __tablename__ = "post_retweets"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    user_id: UUID = Field(foreign_key="user.id")
    post_id: UUID = Field(foreign_key="posts.id")
    original_post_id: UUID = Field(foreign_key="posts.id")  # 添加原始帖子ID
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    post: Optional[Post] = Relationship(
        back_populates="retweets",
        sa_relationship_kwargs={"foreign_keys": "[PostRetweet.post_id]"}  # 使用sa_relationship_kwargs
    )
    original_post: Optional[Post] = Relationship(
        back_populates="original_retweets",
        sa_relationship_kwargs={"foreign_keys": "[PostRetweet.original_post_id]"}  # 使用sa_relationship_kwargs
    )