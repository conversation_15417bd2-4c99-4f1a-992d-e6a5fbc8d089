from datetime import datetime, timezone
from typing import Optional, List, TYPE_CHECKING
from uuid import UUID, uuid4
from sqlmodel import SQLModel, Field, Relationship

if TYPE_CHECKING:
    from .user import User
    from .post import Post

class Channel(SQLModel, table=True):
    __tablename__ = "channels"

    id: UUID = Field(default_factory=uuid4, primary_key=True, index=True)
    name: str = Field(max_length=100, unique=True, index=True)  # 英文名，用于URL
    display_name: str = Field(max_length=200)  # 显示名称
    description: Optional[str] = Field(default=None)
    icon: Optional[str] = Field(default=None)  # 图标URL或名称
    color: Optional[str] = Field(default=None, max_length=7)  # 主题色
    is_active: bool = Field(default=True, index=True)
    is_default: bool = Field(default=False)  # 是否为默认板块
    post_count: int = Field(default=0)  # 帖子数量
    member_count: int = Field(default=0)  # 成员数量
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: Optional[datetime] = Field(default=None)
    created_by: UUID = Field(foreign_key="user.id")
    moderator_id: Optional[UUID] = Field(default=None, foreign_key="user.id", index=True)  # 版主

    # 关系
    creator: Optional["User"] = Relationship(sa_relationship_kwargs={"foreign_keys": "[Channel.created_by]"})
    moderator: Optional["User"] = Relationship(sa_relationship_kwargs={"foreign_keys": "[Channel.moderator_id]"})
    posts: List["Post"] = Relationship(back_populates="channel")
    members: List["ChannelMember"] = Relationship(back_populates="channel")

class ChannelMember(SQLModel, table=True):
    __tablename__ = "channel_members"

    id: UUID = Field(default_factory=uuid4, primary_key=True)
    channel_id: UUID = Field(foreign_key="channels.id")
    user_id: UUID = Field(foreign_key="user.id")
    role: str = Field(default="member")  # member, moderator, admin
    joined_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    is_active: bool = Field(default=True)

    # 关系
    channel: Optional[Channel] = Relationship(back_populates="members")
    user: Optional["User"] = Relationship()

    class Config:
        # 确保同一用户不能重复加入同一板块
        table_args = (
            {"sqlite_autoincrement": True},
        )

class UserChannelSubscription(SQLModel, table=True):
    __tablename__ = "user_channel_subscriptions"

    id: UUID = Field(default_factory=uuid4, primary_key=True, index=True)
    user_id: UUID = Field(foreign_key="user.id", index=True)
    channel_id: UUID = Field(foreign_key="channels.id", index=True)
    joined_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    notification_enabled: bool = Field(default=True)  # 是否启用通知
    is_active: bool = Field(default=True)

    # 关系
    user: Optional["User"] = Relationship()
    channel: Optional[Channel] = Relationship()

    class Config:
        # 确保同一用户不能重复订阅同一板块
        table_args = (
            {"sqlite_autoincrement": True},
        )