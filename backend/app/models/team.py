from datetime import datetime
from typing import Optional, List
from uuid import UUID, uuid1
from sqlmodel import SQLModel, Field, Relationship
from sqlalchemy import Column
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy import String

class Team(SQLModel, table=True):
    __tablename__ = "team"
    
    id: UUID = Field(default_factory=uuid1, primary_key=True)
    name: str = Field(max_length=128)
    user_id: UUID = Field(foreign_key="user.id")
    create_time: datetime = Field(default_factory=datetime.utcnow)
    update_time: datetime = Field(default_factory=datetime.utcnow, sa_column_kwargs={"onupdate": datetime.utcnow})

class TeamMember(SQLModel, table=True):
    __tablename__ = "team_member"
    
    id: UUID = Field(default_factory=uuid1, primary_key=True)
    team_id: UUID = Field(foreign_key="team.id")
    user_id: UUID = Field(foreign_key="user.id")
    create_time: datetime = Field(default_factory=datetime.utcnow)
    update_time: datetime = Field(default_factory=datetime.utcnow, sa_column_kwargs={"onupdate": datetime.utcnow})

class TeamMemberPermission(SQLModel, table=True):
    __tablename__ = "team_member_permission"
    
    id: UUID = Field(default_factory=uuid1, primary_key=True)
    member_id: UUID = Field(foreign_key="team_member.id")
    auth_target_type: str = Field(max_length=128, default="DATASET")
    target: UUID
    operate: List[str] = Field(sa_column=Column(ARRAY(String)))
    create_time: datetime = Field(default_factory=datetime.utcnow)
    update_time: datetime = Field(default_factory=datetime.utcnow, sa_column_kwargs={"onupdate": datetime.utcnow})