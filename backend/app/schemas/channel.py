from datetime import datetime
from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel

class ChannelBase(BaseModel):
    name: str
    display_name: str
    description: Optional[str] = None
    icon: Optional[str] = None
    color: Optional[str] = None
    is_active: bool = True

class ChannelCreate(ChannelBase):
    moderator_id: Optional[UUID] = None

class ChannelUpdate(BaseModel):
    display_name: Optional[str] = None
    description: Optional[str] = None
    icon: Optional[str] = None
    color: Optional[str] = None
    is_active: Optional[bool] = None
    moderator_id: Optional[UUID] = None

class ChannelResponse(ChannelBase):
    id: UUID
    post_count: int
    member_count: int
    created_at: datetime
    created_by: UUID
    is_default: bool
    is_member: bool = False
    moderator_id: Optional[UUID] = None
    moderator_name: Optional[str] = None  # 版主显示名称
    moderator_username: Optional[str] = None  # 版主用户名

    class Config:
        from_attributes = True

class ChannelListResponse(BaseModel):
    id: UUID
    name: str
    display_name: str
    description: Optional[str]
    icon: Optional[str]
    color: Optional[str]
    post_count: int
    member_count: int
    is_default: bool

class ChannelMemberResponse(BaseModel):
    id: UUID
    user_id: UUID
    username: str
    display_name: Optional[str]
    avatar: Optional[str]
    role: str
    joined_at: datetime

class JoinChannelRequest(BaseModel):
    pass  # 暂时不需要额外参数