from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from uuid import UUID

class OpenSourceProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    github_url: str
    image_url: Optional[str] = None
    rate: Optional[float] = 0
    is_private: Optional[bool] = False

class OpenSourceProjectCreate(OpenSourceProjectBase):
    pass

class OpenSourceProjectUpdate(OpenSourceProjectBase):
    pass

class OpenSourceProjectOut(OpenSourceProjectBase):
    id: UUID
    creator_id: UUID
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        orm_mode = True

class OpenSourceDiscussionBase(BaseModel):
    content: str

class OpenSourceDiscussionCreate(OpenSourceDiscussionBase):
    pass

class OpenSourceDiscussionOut(OpenSourceDiscussionBase):
    id: UUID
    user_id: UUID
    created_at: datetime

    class Config:
        orm_mode = True

class OpenSourceProjectListResponse(BaseModel):
    items: List[OpenSourceProjectOut]
    total: int 