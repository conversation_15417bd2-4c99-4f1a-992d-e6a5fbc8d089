from datetime import datetime
from typing import Optional
from uuid import UUID
from pydantic import BaseModel
from ..models.notification import NotificationType

class NotificationBase(BaseModel):
    type: NotificationType
    content: Optional[str] = None
    target_post_id: Optional[UUID] = None
    source_post_id: Optional[UUID] = None
    source_comment_id: Optional[UUID] = None

class NotificationCreate(NotificationBase):
    user_id: UUID  # 被通知的用户
    sender_id: UUID  # 发送通知的用户

class NotificationResponse(NotificationBase):
    id: UUID
    user_id: UUID
    sender_id: UUID
    is_read: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # 发送者信息
    sender_name: Optional[str] = None
    sender_avatar: Optional[str] = None
    sender_username: Optional[str] = None

class NotificationUpdate(BaseModel):
    is_read: Optional[bool] = None

class MentionUser(BaseModel):
    username: str

class UserSearchResult(BaseModel):
    id: UUID
    username: Optional[str] = None
    display_name: Optional[str] = None
    thumbnail: Optional[str] = None