from typing import List, Optional
from uuid import UUID
from sqlmodel import Session, select, and_, func
from ..models.channel import Channel, ChannelMember
from ..models.user import User
from ..schemas.channel import ChannelCreate, ChannelUpdate, ChannelResponse, ChannelListResponse, ChannelMemberResponse

def create_channel(
    db: Session,
    channel_data: ChannelCreate,
    creator_id: UUID
) -> Channel:
    """创建新板块"""
    channel_dict = channel_data.model_dump()
    # 如果没有指定版主，则设置创建者为版主
    if not channel_dict.get('moderator_id'):
        channel_dict['moderator_id'] = creator_id
    
    channel = Channel(**channel_dict, created_by=creator_id)
    db.add(channel)
    db.commit()
    db.refresh(channel)
    
    # 创建者自动成为管理员
    member = ChannelMember(
        channel_id=channel.id,
        user_id=creator_id,
        role="admin"
    )
    db.add(member)
    channel.member_count = 1
    db.add(channel)
    db.commit()
    
    return channel

def get_channel_by_id(db: Session, channel_id: UUID) -> Optional[Channel]:
    """根据ID获取板块"""
    return db.exec(select(Channel).where(Channel.id == channel_id)).first()

def get_channel_by_name(db: Session, name: str) -> Optional[Channel]:
    """根据名称获取板块"""
    return db.exec(select(Channel).where(Channel.name == name)).first()

def get_channels(
    db: Session,
    skip: int = 0,
    limit: int = 20,
    active_only: bool = True
) -> List[ChannelListResponse]:
    """获取板块列表"""
    query = select(Channel)
    
    if active_only:
        query = query.where(Channel.is_active == True)
    
    query = query.order_by(Channel.is_default.desc(), Channel.post_count.desc())
    query = query.offset(skip).limit(limit)
    
    channels = db.exec(query).all()
    
    return [
        ChannelListResponse(
            id=channel.id,
            name=channel.name,
            display_name=channel.display_name,
            description=channel.description,
            icon=channel.icon,
            color=channel.color,
            post_count=channel.post_count,
            member_count=channel.member_count,
            is_default=channel.is_default
        )
        for channel in channels
    ]

def update_channel(
    db: Session,
    channel_id: UUID,
    channel_data: ChannelUpdate
) -> Optional[Channel]:
    """更新板块信息"""
    channel = db.exec(select(Channel).where(Channel.id == channel_id)).first()
    if not channel:
        return None
    
    update_data = channel_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(channel, field, value)
    
    db.add(channel)
    db.commit()
    db.refresh(channel)
    return channel

def join_channel(db: Session, channel_id: UUID, user_id: UUID) -> bool:
    """用户加入板块"""
    # 检查是否已经是成员
    existing = db.exec(
        select(ChannelMember).where(
            and_(
                ChannelMember.channel_id == channel_id,
                ChannelMember.user_id == user_id
            )
        )
    ).first()
    
    if existing:
        if not existing.is_active:
            existing.is_active = True
            db.add(existing)
            db.commit()
            return True
        return False  # 已经是成员
    
    # 创建新成员关系
    member = ChannelMember(
        channel_id=channel_id,
        user_id=user_id,
        role="member"
    )
    db.add(member)
    
    # 更新板块成员数量
    channel = db.exec(select(Channel).where(Channel.id == channel_id)).first()
    if channel:
        channel.member_count += 1
        db.add(channel)
    
    db.commit()
    return True

def leave_channel(db: Session, channel_id: UUID, user_id: UUID) -> bool:
    """用户离开板块"""
    member = db.exec(
        select(ChannelMember).where(
            and_(
                ChannelMember.channel_id == channel_id,
                ChannelMember.user_id == user_id,
                ChannelMember.is_active == True
            )
        )
    ).first()
    
    if not member:
        return False
    
    member.is_active = False
    db.add(member)
    
    # 更新板块成员数量
    channel = db.exec(select(Channel).where(Channel.id == channel_id)).first()
    if channel and channel.member_count > 0:
        channel.member_count -= 1
        db.add(channel)
    
    db.commit()
    return True

def get_channel_members(
    db: Session,
    channel_id: UUID,
    skip: int = 0,
    limit: int = 20
) -> List[ChannelMemberResponse]:
    """获取板块成员列表"""
    query = select(ChannelMember, User).join(
        User, ChannelMember.user_id == User.id
    ).where(
        and_(
            ChannelMember.channel_id == channel_id,
            ChannelMember.is_active == True
        )
    ).order_by(ChannelMember.joined_at.desc()).offset(skip).limit(limit)
    
    results = db.exec(query).all()
    
    return [
        ChannelMemberResponse(
            id=member.id,
            user_id=member.user_id,
            username=user.username,
            display_name=user.display_name,
            avatar=user.thumbnail,
            role=member.role,
            joined_at=member.joined_at
        )
        for member, user in results
    ]

def is_user_member(db: Session, channel_id: UUID, user_id: UUID) -> bool:
    """检查用户是否为板块成员"""
    member = db.exec(
        select(ChannelMember).where(
            and_(
                ChannelMember.channel_id == channel_id,
                ChannelMember.user_id == user_id,
                ChannelMember.is_active == True
            )
        )
    ).first()
    
    return member is not None

def get_user_channels(db: Session, user_id: UUID) -> List[ChannelListResponse]:
    """获取用户加入的板块列表"""
    query = select(Channel, ChannelMember).join(
        ChannelMember, Channel.id == ChannelMember.channel_id
    ).where(
        and_(
            ChannelMember.user_id == user_id,
            ChannelMember.is_active == True,
            Channel.is_active == True
        )
    ).order_by(Channel.is_default.desc(), Channel.display_name)
    
    results = db.exec(query).all()
    
    return [
        ChannelListResponse(
            id=channel.id,
            name=channel.name,
            display_name=channel.display_name,
            description=channel.description,
            icon=channel.icon,
            color=channel.color,
            post_count=channel.post_count,
            member_count=channel.member_count,
            is_default=channel.is_default
        )
        for channel, _ in results
    ]