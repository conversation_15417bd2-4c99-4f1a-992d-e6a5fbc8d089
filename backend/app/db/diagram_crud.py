from typing import List
from uuid import UUID
from sqlmodel import Session
from ..models import Diagram
from ..schemas.diagram import DiagramCreate, DiagramUpdate
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def get_diagrams(
    db: Session, 
    user_id: UUID,
    skip: int = 0,
    limit: int = 100,
    sort_by: str = "created_at",
    sort_desc: bool = True
) -> List[Diagram]:
    """获取用户的所有图片"""
    logger.info(f"查询用户 {user_id} 的图片，跳过 {skip}，限制 {limit}，排序字段 {sort_by}，降序 {sort_desc}")
    
    query = db.query(Diagram).filter(Diagram.user_id == user_id)
    
    # 添加排序
    if sort_by in ["created_at", "name", "file_size"]:
        sort_column = getattr(Diagram, sort_by)
        if sort_desc:
            query = query.order_by(sort_column.desc())
        else:
            query = query.order_by(sort_column)
    
    # 添加分页
    results = query.offset(skip).limit(limit).all()
    logger.info(f"查询结果：找到 {len(results)} 条记录")
    return results


def get_diagram_by_id(db, diagram_id: UUID, user_id: UUID):
    """Get a diagram by id."""
    return db.query(Diagram).filter(Diagram.id == diagram_id, Diagram.user_id == user_id).first()


def create_diagram(db: Session, diagram_data: dict, user_id: UUID) -> Diagram:
    """创建新图表"""
    try:
        # 移除不存在的 folder_id 字段，如果存在的话
        if 'folder_id' in diagram_data:
            del diagram_data['folder_id']
        
        # 创建新的图表记录
        db_diagram = Diagram(
            **diagram_data,
            user_id=user_id
        )
        db.add(db_diagram)
        db.commit()
        db.refresh(db_diagram)
        return db_diagram
    except Exception as e:
        logger.error(f"创建图表失败: {str(e)}")
        db.rollback()
        raise


def update_diagram(db, diagram_id: UUID, diagram_update: DiagramUpdate, user_id: UUID):
    """Update a diagram."""
    db_diagram = get_diagram_by_id(db, diagram_id, user_id)
    if not db_diagram:
        return None
    
    update_data = diagram_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_diagram, key, value)
    
    db_diagram.updated_at = datetime.now()
    db.commit()
    db.refresh(db_diagram)
    return db_diagram


def delete_diagram(db, diagram_id: UUID, user_id: UUID):
    """Delete a diagram."""
    db_diagram = get_diagram_by_id(db, diagram_id, user_id)
    if not db_diagram:
        return False
    
    db.delete(db_diagram)
    db.commit()
    return True