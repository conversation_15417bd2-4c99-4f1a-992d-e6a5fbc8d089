from typing import List, Optional
from uuid import UUID
from sqlmodel import Session, select, and_
from ..models.notification import Notification, NotificationType
from ..models.user import User
from ..schemas.notification import NotificationCreate, NotificationResponse

def create_notification(
    db: Session,
    notification_data: NotificationCreate
) -> Notification:
    """创建通知"""
    notification = Notification(**notification_data.model_dump())
    db.add(notification)
    db.commit()
    db.refresh(notification)
    return notification

def get_user_notifications(
    db: Session,
    user_id: UUID,
    skip: int = 0,
    limit: int = 20,
    unread_only: bool = False
) -> List[NotificationResponse]:
    """获取用户的通知列表"""
    query = select(Notification, User).join(
        User, Notification.sender_id == User.id
    ).where(Notification.user_id == user_id)
    
    if unread_only:
        query = query.where(Notification.is_read == False)
    
    query = query.order_by(Notification.created_at.desc()).offset(skip).limit(limit)
    
    results = db.exec(query).all()
    
    notifications = []
    for notification, sender in results:
        notifications.append(NotificationResponse(
            id=notification.id,
            type=notification.type,
            content=notification.content,
            user_id=notification.user_id,
            sender_id=notification.sender_id,
            target_post_id=notification.target_post_id,
            source_post_id=notification.source_post_id,
            source_comment_id=notification.source_comment_id,
            is_read=notification.is_read,
            created_at=notification.created_at,
            updated_at=notification.updated_at,
            sender_name=sender.display_name or sender.username,
            sender_avatar=sender.thumbnail,
            sender_username=sender.username
        ))
    
    return notifications

def mark_notification_as_read(
    db: Session,
    notification_id: UUID,
    user_id: UUID
) -> Optional[Notification]:
    """标记通知为已读"""
    notification = db.exec(
        select(Notification).where(
            and_(
                Notification.id == notification_id,
                Notification.user_id == user_id
            )
        )
    ).first()
    
    if notification:
        notification.is_read = True
        db.add(notification)
        db.commit()
        db.refresh(notification)
    
    return notification

def mark_all_notifications_as_read(
    db: Session,
    user_id: UUID
) -> int:
    """标记用户所有通知为已读"""
    notifications = db.exec(
        select(Notification).where(
            and_(
                Notification.user_id == user_id,
                Notification.is_read == False
            )
        )
    ).all()
    
    count = 0
    for notification in notifications:
        notification.is_read = True
        db.add(notification)
        count += 1
    
    db.commit()
    return count

def get_unread_count(db: Session, user_id: UUID) -> int:
    """获取用户未读通知数量"""
    count = db.exec(
        select(Notification).where(
            and_(
                Notification.user_id == user_id,
                Notification.is_read == False
            )
        )
    ).all()
    return len(count)

def search_users_by_username(
    db: Session,
    query: str,
    limit: int = 10
) -> List[User]:
    """根据用户名搜索用户"""
    print(f"🔍 [DB DEBUG] 搜索用户 - 查询: '{query}', 限制: {limit}")
    
    # 同时搜索用户名和显示名称
    users = db.exec(
        select(User).where(
            (User.username.ilike(f"%{query}%")) | 
            (User.display_name.ilike(f"%{query}%"))
        ).where(
            User.is_active == True  # 只返回活跃用户
        ).limit(limit)
    ).all()
    
    print(f"📊 [DB DEBUG] 数据库查询完成 - 找到 {len(users)} 个用户")
    for user in users:
        print(f"   - 用户: {user.username} (显示名: {user.display_name})")
    
    return users