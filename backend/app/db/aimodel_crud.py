from typing import List, Optional
from uuid import UUID
from sqlmodel import Session, select, or_
from ..models import AIModel
from ..schemas.aimodel import AIModelCreate, AIModelUpdate

def create_ai_model(db: Session, model_data: AIModelCreate, user_id: UUID) -> AIModel:
    """创建新的AI模型"""
    db_model = AIModel(**model_data.model_dump(), user_id=user_id)
    db.add(db_model)
    db.commit()
    db.refresh(db_model)
    return db_model

def get_ai_models(db: Session, user_id: UUID) -> List[AIModel]:
    """获取用户的所有AI模型，包括系统默认模型"""
    # 查询用户自己的模型 + 系统默认模型
    return db.exec(
        select(AIModel).where(
            or_(
                AIModel.user_id == user_id,  # 用户自己的模型
                AIModel.provider == 'default'  # 系统默认模型
            )
        )
    ).all()

def get_ai_model_by_id(db: Session, model_id: UUID, user_id: UUID) -> Optional[AIModel]:
    """根据ID获取AI模型，包括系统默认模型"""
    return db.exec(
        select(AIModel).where(
            AIModel.id == model_id,
            or_(
                AIModel.user_id == user_id,  # 用户自己的模型
                AIModel.provider == 'default'  # 系统默认模型
            )
        )
    ).first()

def update_ai_model(db: Session, model_id: UUID, model_data: AIModelUpdate, user_id: UUID) -> Optional[AIModel]:
    """更新AI模型，但不允许修改系统默认模型"""
    db_model = db.exec(
        select(AIModel).where(AIModel.id == model_id, AIModel.user_id == user_id)
    ).first()
    
    if not db_model:
        return None
    
    # 检查是否为系统默认模型，默认模型不允许修改
    if db_model.provider == 'default':
        raise ValueError("系统默认模型不允许修改")
    
    update_data = model_data.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_model, key, value)
    
    db.add(db_model)
    db.commit()
    db.refresh(db_model)
    return db_model

def delete_ai_model(db: Session, model_id: UUID, user_id: UUID) -> bool:
    """删除AI模型，但不允许删除系统默认模型"""
    db_model = db.exec(
        select(AIModel).where(AIModel.id == model_id, AIModel.user_id == user_id)
    ).first()
    
    if not db_model:
        return False
    
    # 检查是否为系统默认模型，默认模型不允许删除
    if db_model.provider == 'default':
        raise ValueError("系统默认模型不允许删除")
    
    db.delete(db_model)
    db.commit()
    return True