from sqlalchemy.orm import Session
from uuid import UUID
from app.models.open_source import OpenSourceProject, OpenSourceDiscussion
from app.schemas.open_source import (
    OpenSourceProjectCreate, OpenSourceProjectUpdate,
)

def get_projects(db: Session, skip=0, limit=20):
    from sqlalchemy import desc, case
    
    query = db.query(OpenSourceProject).filter_by(is_private=False)
    
    # 添加排序逻辑：推荐项目(rate > 4)置顶，然后按创建时间倒序
    query = query.order_by(
        desc(case((OpenSourceProject.rate > 4, 1), else_=0)),  # 推荐项目置顶
        desc(OpenSourceProject.created_at)  # 按创建时间倒序
    )
    
    total = query.count()
    items = query.offset(skip).limit(limit).all()
    return {"items": items, "total": total}

def get_project(db: Session, project_id: UUID):
    return db.query(OpenSourceProject).filter_by(id=project_id).first()

def create_project(db: Session, project: OpenSourceProjectCreate, user_id: UUID):
    db_project = OpenSourceProject(**project.dict(), creator_id=user_id)
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    return db_project

def update_project(db: Session, project_id: UUID, project: OpenSourceProjectUpdate, user_id: UUID):
    db_project = get_project(db, project_id)
    if db_project and db_project.creator_id == user_id:
        for k, v in project.dict(exclude_unset=True).items():
            setattr(db_project, k, v)
        db.commit()
        db.refresh(db_project)
    return db_project

def delete_project(db: Session, project_id: UUID, user_id: UUID):
    db_project = get_project(db, project_id)
    if db_project and db_project.creator_id == user_id:
        db.delete(db_project)
        db.commit()
    return db_project

def get_discussions(db: Session, project_id: UUID):
    return db.query(OpenSourceDiscussion).filter_by(project_id=project_id).all()

def create_discussion(db: Session, project_id: UUID, user_id: UUID, content: str):
    discussion = OpenSourceDiscussion(project_id=project_id, user_id=user_id, content=content)
    db.add(discussion)
    db.commit()
    db.refresh(discussion)
    return discussion

def update_project_rate_by_admin(db: Session, project_id: UUID, rate: float):
    """管理员更新项目评分"""
    db_project = get_project(db, project_id)
    if db_project:
        db_project.rate = rate
        db.commit()
        db.refresh(db_project)
    return db_project