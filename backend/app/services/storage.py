from fastapi import UploadFile, HTTPException, status
import os
import uuid
from ..dependencies import get_supabase_client

async def upload_file_to_storage(
    file: UploadFile, 
    bucket: str, 
    path: str
) -> str:
    """上传文件到Supabase Storage"""
    import logging
    import asyncio
    import random
    logger = logging.getLogger(__name__)
    
    try:
        # 获取Supabase客户端
        supabase = get_supabase_client()
        
        # 重置文件指针到开始位置
        await file.seek(0)
        
        # 读取文件内容
        file_content = await file.read()
        
        if not file_content:
            raise ValueError(f"File content is empty for {file.filename}")
        
        actual_size = len(file_content)
        logger.info(f"Uploading file: {path}, size: {actual_size} bytes")
        
        # 生成更安全的唯一路径，避免并发冲突
        import time
        import random
        timestamp = int(time.time() * 1000)
        random_suffix = random.randint(1000, 9999)
        path_parts = path.rsplit('.', 1)
        if len(path_parts) == 2:
            unique_path = f"{path_parts[0]}_{timestamp}_{random_suffix}.{path_parts[1]}"
        else:
            unique_path = f"{path}_{timestamp}_{random_suffix}"
        
        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 上传到Supabase Storage
                response = supabase.storage.from_(bucket).upload(
                    path=unique_path,
                    file=file_content,
                    file_options={
                        "content-type": file.content_type,
                        "cache-control": "3600",
                        "upsert": "true"
                    }
                )
                
                # 检查上传响应
                if hasattr(response, 'error') and response.error:
                    error_msg = str(response.error)
                    if attempt < max_retries - 1:
                        logger.warning(f"Upload attempt {attempt + 1} failed: {error_msg}, retrying...")
                        await asyncio.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        raise Exception(f"Supabase upload error: {error_msg}")
                
                # 获取公共URL
                file_url = supabase.storage.from_(bucket).get_public_url(unique_path)
                logger.info(f"File uploaded successfully: {unique_path} -> {file_url}")
                
                return file_url
                
            except Exception as e:
                if attempt < max_retries - 1:
                    logger.warning(f"Upload attempt {attempt + 1} failed: {str(e)}, retrying...")
                    await asyncio.sleep(1)
                    continue
                else:
                    raise e
        
    except Exception as e:
        logger.error(f"Failed to upload file {path}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file {file.filename}: {str(e)}"
        )