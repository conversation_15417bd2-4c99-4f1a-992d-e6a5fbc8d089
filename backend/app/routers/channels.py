from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from uuid import UUID
from sqlmodel import select

from ..dependencies import DBSessionDependency, UserDependency
from ..schemas.channel import (
    ChannelCreate, 
    ChannelUpdate, 
    ChannelResponse, 
    ChannelListResponse,
    ChannelMemberResponse,
    JoinChannelRequest
)
from ..db.channel_crud import (
    create_channel,
    get_channel_by_id,
    get_channel_by_name,
    get_channels,
    update_channel,
    join_channel,
    leave_channel,
    get_channel_members,
    is_user_member,
    get_user_channels
)

router = APIRouter(prefix="/api/channels", tags=["channels"])

@router.get("/", response_model=List[ChannelListResponse])
async def list_channels(
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """获取板块列表"""
    return get_channels(db, skip=skip, limit=limit)

@router.post("/", response_model=ChannelResponse)
async def create_new_channel(
    channel_data: Channel<PERSON>reate,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """创建新板块"""
    # 检查板块名称是否已存在
    existing = get_channel_by_name(db, channel_data.name)
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Channel name already exists"
        )
    
    channel = create_channel(db, channel_data, current_user.id)
    return ChannelResponse.model_validate(channel)

@router.get("/{channel_id}", response_model=ChannelResponse)
async def get_channel(
    channel_id: UUID,
    db: DBSessionDependency,
    current_user: UserDependency
):
    """获取板块详情"""
    channel = get_channel_by_id(db, channel_id)
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Channel not found"
        )
    is_member = is_user_member(db, channel_id, current_user.id)
    channel_dict = channel.__dict__.copy()
    channel_dict["is_member"] = is_member
    
    # 添加版主信息
    if channel.moderator_id:
        from ..models.user import User
        moderator = db.exec(select(User).where(User.id == channel.moderator_id)).first()
        if moderator:
            channel_dict["moderator_name"] = moderator.display_name or moderator.username
            channel_dict["moderator_username"] = moderator.username
    
    return ChannelResponse.model_validate(channel_dict)

@router.get("/name/{channel_name}", response_model=ChannelResponse)
async def get_channel_by_name_endpoint(
    channel_name: str,
    db: DBSessionDependency
):
    """根据名称获取板块详情"""
    channel = get_channel_by_name(db, channel_name)
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Channel not found"
        )
    
    return ChannelResponse.model_validate(channel)

@router.put("/{channel_id}", response_model=ChannelResponse)
async def update_channel_endpoint(
    channel_id: UUID,
    channel_data: ChannelUpdate,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """更新板块信息"""
    channel = get_channel_by_id(db, channel_id)
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Channel not found"
        )
    
    # 检查权限（只有创建者或管理员可以更新）
    if channel.created_by != current_user.id and current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    updated_channel = update_channel(db, channel_id, channel_data)
    return ChannelResponse.model_validate(updated_channel)

@router.post("/{channel_id}/join")
async def join_channel_endpoint(
    channel_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """加入板块"""
    channel = get_channel_by_id(db, channel_id)
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Channel not found"
        )
    
    if not channel.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Channel is not active"
        )
    
    success = join_channel(db, channel_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Already a member of this channel"
        )
    
    return {"detail": "Successfully joined the channel"}

@router.delete("/{channel_id}/leave")
async def leave_channel_endpoint(
    channel_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """离开板块"""
    success = leave_channel(db, channel_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Not a member of this channel"
        )
    
    return {"detail": "Successfully left the channel"}

@router.get("/{channel_id}/members", response_model=List[ChannelMemberResponse])
async def get_channel_members_endpoint(
    channel_id: UUID,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """获取板块成员列表"""
    channel = get_channel_by_id(db, channel_id)
    if not channel:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Channel not found"
        )
    
    return get_channel_members(db, channel_id, skip=skip, limit=limit)

@router.get("/user/joined", response_model=List[ChannelListResponse])
async def get_user_joined_channels(
    current_user: UserDependency,
    db: DBSessionDependency
):
    """获取用户加入的板块列表"""
    return get_user_channels(db, current_user.id)

@router.get("/{channel_id}/membership")
async def check_membership(
    channel_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """检查用户是否为板块成员"""
    is_member = is_user_member(db, channel_id, current_user.id)
    return {"is_member": is_member}