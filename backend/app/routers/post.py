from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Body, status
from typing import List, Optional, Any, Dict
from uuid import UUID
import os
import math
from datetime import datetime, timezone
from pydantic import BaseModel

from sqlmodel import Session, select,func
from ..models.user import User
from ..models.channel import Channel
# from app.database import get_session
from app.models import Post, PostAttached, PostLike, PostRetweet
from app.models.post import PostType
# from app.models.user import User
# from app.auth.auth import get_current_user
# from app.config import settings
from app.services.storage import upload_file_to_storage
# 添加依赖导入
from ..dependencies import DBSessionDependency, UserDependency, SupabaseDependency
from uuid import UUID, uuid4
from app.dependencies import UserOptionalDependency


class UserInfo(BaseModel):
    id: UUID
    username: Optional[str] = None
    display_name: Optional[str] = None
    email: Optional[str] = None
    thumbnail: Optional[str] = None

class ChannelInfo(BaseModel):
    id: UUID
    name: str
    display_name: str
    color: Optional[str] = None
    icon: Optional[str] = None

class PostResponse(BaseModel):
    post: Post
    images: List[str]
    owner: Optional[UserInfo] = None
    channel: Optional[ChannelInfo] = None

# 新增：分页响应模型
class PaginatedPostResponse(BaseModel):
    items: List[PostResponse]
    total: int
    page: int
    limit: int
    total_pages: int
    has_next: bool
    has_prev: bool

router = APIRouter(prefix="/api/posts", tags=["posts"])



@router.post("/", response_model=Post, status_code=status.HTTP_201_CREATED)
async def create_post(
    current_user: UserDependency,
    db: DBSessionDependency,
    content: str = Form(...),
    channel_id: Optional[str] = Form(None),
    images: List[UploadFile] = File(default=[])
):
    """
    创建一条新微博，可以包含文字和图片
    """
    import logging
    logger = logging.getLogger(__name__)

    # 验证用户
    if not current_user or not current_user.id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not authenticated"
        )

    # 验证内容
    if not content or not content.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Content cannot be empty"
        )

    # 截断内容以防超过数据库限制
    content = content.strip()
    if len(content) > 2000:
        content = content[:2000]
        logger.warning(f"Content truncated from {len(content)} to 2000 characters")

    # 验证板块ID（如果提供）或设置默认板块
    channel_uuid = None
    if channel_id:
        try:
            channel_uuid = UUID(channel_id)
            # 验证板块是否存在
            from ..models.channel import Channel
            channel = db.get(Channel, channel_uuid)
            if not channel or not channel.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or inactive channel"
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid channel ID format"
            )
    else:
        # 如果没有指定频道，使用默认的"general"频道
        from ..models.channel import Channel
        from sqlmodel import select

        default_channel = db.exec(
            select(Channel).where(
                Channel.name == "general",
                Channel.is_active == True
            )
        ).first()

        if default_channel:
            channel_uuid = default_channel.id
            logger.info(f"Using default channel 'general': {channel_uuid}")
        else:
            # 如果没有general频道，使用第一个活跃频道
            first_channel = db.exec(
                select(Channel).where(Channel.is_active == True)
            ).first()

            if first_channel:
                channel_uuid = first_channel.id
                logger.info(f"Using first available channel '{first_channel.name}': {channel_uuid}")
            else:
                logger.warning("No active channels found, creating post without channel")
                # 可以选择抛出错误或者允许无频道的帖子

    # 创建新的Post记录
    try:
        new_post = Post(
            content=content,
            owner_id=current_user.id,
            timestamp=datetime.now(timezone.utc),
            channel_id=channel_uuid,
            type=PostType.NORMAL  # 明确设置类型
        )
        db.add(new_post)
        db.commit()
        db.refresh(new_post)
        logger.info(f"Post created successfully: {new_post.id}")
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to create post: {str(e)}")
        # 检查是否是数据库约束错误
        if "NotNullViolation" in str(e):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Required field is missing or empty"
            )
        elif "StringDataRightTruncation" in str(e):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content too long for database field"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error: {str(e)}"
            )

    # 如果帖子发布在频道中，创建频道新帖通知
    if channel_uuid:
        from .notifications import create_channel_post_notification
        await create_channel_post_notification(db, channel_uuid, new_post.id, current_user.id)

    # 处理图片上传
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"开始处理图片上传，接收到 {len(images) if images else 0} 个文件")
    
    # 详细记录每个图片文件的信息
    if images:
        for i, img in enumerate(images):
            if img:
                logger.info(f"图片 {i+1}: 文件名={img.filename}, 大小={img.size}, 类型={img.content_type}")
            else:
                logger.warning(f"图片 {i+1}: 空文件对象")
    
    # 过滤掉空的图片文件 - 不能使用 img.size，需要读取内容检查
    valid_images = []
    if images:
        for img in images:
            if img and img.filename:
                try:
                    await img.seek(0)
                    content = await img.read()
                    if len(content) > 0:
                        await img.seek(0)  # 重置文件指针
                        valid_images.append(img)
                except Exception as e:
                    logger.warning(f"检查图片 {img.filename} 时出错: {str(e)}")
    logger.info(f"有效图片数量: {len(valid_images)}")
    
    if valid_images:
        logger.info(f"开始处理 {len(valid_images)} 张图片")
        
        try:
            # 并行上传图片到存储服务
            import asyncio
            import time
            import uuid
            from concurrent.futures import ThreadPoolExecutor
            
            async def upload_single_image(image: UploadFile, index: int):
                """上传单张图片的异步函数"""
                try:
                    # 重置文件指针
                    await image.seek(0)
                    
                    # 生成存储路径
                    storage_path = f"{current_user.id}"
                    file_ext = os.path.splitext(image.filename)[1] or '.jpg'
                    unique_id = str(uuid.uuid4())[:8]
                    timestamp = int(time.time() * 1000) + index  # 添加索引避免冲突
                    file_name = f"post_{new_post.id}_{index}_{timestamp}_{unique_id}{file_ext}"
                    full_path = f"{storage_path}/{file_name}"
                    
                    # 上传图片
                    image_url = await upload_file_to_storage(
                        file=image,
                        bucket="diagrams",
                        path=full_path
                    )
                    
                    return {
                        'index': index,
                        'image_url': image_url,
                        'image': image,
                        'success': True
                    }
                except Exception as e:
                    logger.error(f"图片 {index + 1} 上传失败: {str(e)}")
                    return {
                        'index': index,
                        'error': str(e),
                        'success': False
                    }
            
            # 并行上传所有图片
            upload_tasks = [
                upload_single_image(image, index) 
                for index, image in enumerate(valid_images)
            ]
            
            upload_results = await asyncio.gather(*upload_tasks, return_exceptions=True)
            
            # 处理上传结果
            successful_uploads = []
            failed_uploads = []
            
            for result in upload_results:
                if isinstance(result, Exception):
                    failed_uploads.append({'error': str(result), 'success': False})
                elif result['success']:
                    successful_uploads.append(result)
                else:
                    failed_uploads.append(result)
            
            # 批量创建数据库记录
            if successful_uploads:
                diagram_records = []
                post_attached_records = []
                
                for result in successful_uploads:
                    # 创建diagram记录 - 计算文件大小
                    await result['image'].seek(0)
                    file_content = await result['image'].read()
                    file_size = len(file_content)
                    await result['image'].seek(0)

                    diagram_data = {
                        "name": f"Post_{new_post.id}_{result['index']}_{int(time.time())}",
                        "description": f"Post attachment {result['index'] + 1}",
                        "source": str(new_post.id),
                        "type": "blog",
                        "content_type": result['image'].content_type,
                        "file_size": file_size,
                        "image_url": result['image_url'],
                    }

                    from ..db.diagram_crud import create_diagram
                    db_diagram = create_diagram(db, diagram_data, user_id=current_user.id)
                    diagram_records.append(db_diagram)

                    # 创建关联记录
                    post_attached = PostAttached(
                        post_id=new_post.id,
                        attached_id=db_diagram.id,
                        attached_type="image"
                    )
                    post_attached_records.append(post_attached)
                    db.add(post_attached)
                
                # 一次性提交所有数据库操作
                db.commit()
                logger.info(f"成功处理 {len(successful_uploads)} 张图片")
            
            # 检查结果
            if len(successful_uploads) == 0 and len(valid_images) > 0:
                raise Exception(f"所有 {len(valid_images)} 张图片上传失败")
            elif failed_uploads:
                logger.warning(f"有 {len(failed_uploads)} 张图片上传失败，但 {len(successful_uploads)} 张成功")
            
        except Exception as e:
            logger.error(f"图片处理过程中发生严重错误: {str(e)}")
            # 回滚数据库操作并删除帖子
            try:
                db.rollback()
                db.delete(new_post)
                db.commit()
            except Exception as rollback_error:
                logger.error(f"回滚失败: {str(rollback_error)}")
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"图片处理失败: {str(e)}"
            )

    return new_post

@router.get("/", response_model=PaginatedPostResponse)
async def get_posts(
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20,
    channel_id: Optional[str] = None,
):
    """
    获取微博列表，包含关联的附件信息和作者信息，支持分页
    """
    # 计算当前页码
    page = (skip // limit) + 1
    
    # 构建查询条件
    where_conditions = [Post.type == 'NORMAL']
    
    # 如果指定了板块ID，添加板块筛选条件
    if channel_id:
        try:
            channel_uuid = UUID(channel_id)
            where_conditions.append(Post.channel_id == channel_uuid)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid channel ID format"
            )
    
    # 获取总数
    total_count = db.exec(
        select(func.count(Post.id)).where(*where_conditions)
    ).first() or 0
    
    # 计算总页数
    total_pages = math.ceil(total_count / limit) if total_count > 0 else 0
    
    # 获取分页数据
    statement = (
        select(Post)
        .where(*where_conditions)
        .offset(skip)
        .limit(limit)
        .order_by(Post.timestamp.desc())
    )
    posts = db.exec(statement).all()

    # 构建响应列表
    response_list = []
    for post in posts:
        # 加载附件关系
        db.refresh(post, ["attachments"])

        # 获取附件信息列表
        attached_files = []
        for attached in post.attachments:
            # 根据附件类型获取URL
            if attached.attached_type == "image":
                # 从diagram表获取图片URL
                from ..models.diagram import Diagram
                diagram = db.get(Diagram, attached.attached_id)
                if diagram and diagram.image_url:
                    attached_files.append(diagram.image_url)
            elif attached.attached_type == "document":
                # 从document表获取文档URL
                from ..models.document import Document
                document = db.get(Document, attached.attached_id)
                if document and document.file_url:
                    attached_files.append(document.file_url)

        # 获取作者信息
        owner_info = None
        if post.owner_id:
            user = db.get(User, post.owner_id)
            if user:
                owner_info = UserInfo(
                    id=user.id,
                    username=user.username,
                    display_name=user.display_name,
                    email=user.email,
                    thumbnail=user.thumbnail
                )

        # 获取板块信息
        channel_info = None
        if post.channel_id:
            from ..models.channel import Channel
            channel = db.get(Channel, post.channel_id)
            if channel:
                channel_info = ChannelInfo(
                    id=channel.id,
                    name=channel.name,
                    display_name=channel.display_name,
                    color=channel.color,
                    icon=channel.icon
                )

        # 构建响应对象
        response_list.append(PostResponse(post=post, images=attached_files, owner=owner_info, channel=channel_info))

    # 返回分页响应
    return PaginatedPostResponse(
        items=response_list,
        total=total_count,
        page=page,
        limit=limit,
        total_pages=total_pages,
        has_next=page < total_pages,
        has_prev=page > 1
    )

@router.get("/hot", response_model=PaginatedPostResponse)
async def get_hot_posts(
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20,
):
    """
    获取热门微博列表，规则：
    1. 7天内发布的帖子
    2. 优先展示回复数大于4的帖子，按回复数排序
    3. 如果不足，补充点赞数大于3的帖子
    4. 如果仍不足，按回复数排序补充
    """
    # 计算当前页码
    page = (skip // limit) + 1
    
    # 计算7天前的时间
    from datetime import datetime, timezone, timedelta
    seven_days_ago = datetime.now(timezone.utc) - timedelta(days=7)
    
    # 基本条件：普通帖子且7天内发布
    base_conditions = [
        Post.type == 'NORMAL',
        Post.timestamp >= seven_days_ago
    ]
    
    # 获取所有符合基本条件的帖子ID
    all_post_ids = db.exec(
        select(Post.id).where(*base_conditions)
    ).all()
    
    # 统计每个帖子的回复数
    post_reply_counts = {}
    for post_id in all_post_ids:
        reply_count = db.exec(
            select(func.count(Post.id)).where(
                Post.parent_id == post_id,
                Post.type == 'COMMENT'
            )
        ).first() or 0
        post_reply_counts[post_id] = reply_count
    
    # 统计每个帖子的点赞数
    post_like_counts = {}
    for post_id in all_post_ids:
        like_count = db.exec(
            select(func.count(PostLike.user_id)).where(PostLike.post_id == post_id)
        ).first() or 0
        post_like_counts[post_id] = like_count
    
    # 按规则筛选和排序帖子ID
    # 1. 回复数大于4的帖子
    high_reply_posts = [post_id for post_id, count in post_reply_counts.items() if count > 4]
    high_reply_posts.sort(key=lambda post_id: post_reply_counts[post_id], reverse=True)
    
    # 2. 如果不足，补充点赞数大于3的帖子
    high_like_posts = []
    if len(high_reply_posts) < limit:
        remaining_posts = [post_id for post_id in all_post_ids if post_id not in high_reply_posts]
        high_like_posts = [post_id for post_id in remaining_posts if post_like_counts[post_id] > 3]
        high_like_posts.sort(key=lambda post_id: post_like_counts[post_id], reverse=True)
    
    # 3. 如果仍不足，按回复数排序补充
    remaining_posts = []
    if len(high_reply_posts) + len(high_like_posts) < limit:
        excluded_posts = set(high_reply_posts + high_like_posts)
        remaining_posts = [post_id for post_id in all_post_ids if post_id not in excluded_posts]
        remaining_posts.sort(key=lambda post_id: post_reply_counts[post_id], reverse=True)
    
    # 合并排序后的帖子ID列表
    sorted_post_ids = high_reply_posts + high_like_posts + remaining_posts
    
    # 计算总数和总页数
    total_count = len(sorted_post_ids)
    total_pages = math.ceil(total_count / limit) if total_count > 0 else 0
    
    # 分页
    paginated_post_ids = sorted_post_ids[skip:skip+limit]
    
    # 获取帖子详情
    posts = []
    for post_id in paginated_post_ids:
        post = db.get(Post, post_id)
        if post:
            posts.append(post)
    
    # 构建响应列表
    response_list = []
    for post in posts:
        # 加载附件关系
        db.refresh(post, ["attachments"])

        # 获取附件信息列表
        attached_files = []
        for attached in post.attachments:
            # 根据附件类型获取URL
            if attached.attached_type == "image":
                # 从diagram表获取图片URL
                from ..models.diagram import Diagram
                diagram = db.get(Diagram, attached.attached_id)
                if diagram and diagram.image_url:
                    attached_files.append(diagram.image_url)
            elif attached.attached_type == "document":
                # 从document表获取文档URL
                from ..models.document import Document
                document = db.get(Document, attached.attached_id)
                if document and document.file_url:
                    attached_files.append(document.file_url)

        # 获取作者信息
        owner_info = None
        if post.owner_id:
            user = db.get(User, post.owner_id)
            if user:
                owner_info = UserInfo(
                    id=user.id,
                    username=user.username,
                    display_name=user.display_name,
                    email=user.email,
                    thumbnail=user.thumbnail
                )

        # 获取板块信息
        channel_info = None
        if post.channel_id:
            from ..models.channel import Channel
            channel = db.get(Channel, post.channel_id)
            if channel:
                channel_info = ChannelInfo(
                    id=channel.id,
                    name=channel.name,
                    display_name=channel.display_name,
                    color=channel.color,
                    icon=channel.icon
                )

        # 构建响应对象
        response_list.append(PostResponse(post=post, images=attached_files, owner=owner_info, channel=channel_info))

    # 返回分页响应
    return PaginatedPostResponse(
        items=response_list,
        total=total_count,
        page=page,
        limit=limit,
        total_pages=total_pages,
        has_next=page < total_pages,
        has_prev=page > 1
    )

@router.get("/likes", response_model=List[PostResponse])
async def get_liked_posts(
    current_user: UserDependency,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """
    获取用户点赞的微博列表
    """
    # 查询用户点赞的所有微博ID
    liked_post_ids = db.exec(
        select(PostLike.post_id).where(PostLike.user_id == current_user.id)
    ).all()

    # 查询这些微博的详细信息
    posts = db.exec(
        select(Post).where(Post.id.in_(liked_post_ids)).offset(skip).limit(limit)
    ).all()

    # 构建响应列表，包含图片信息
    response_list = []
    for post in posts:
        db.refresh(post, ["images"])
        image_urls = [image.image_url for image in post.images]
        response_list.append(PostResponse(post=post, images=image_urls))

    return response_list

@router.get("/retweets", response_model=List[PostResponse])
async def get_retweeted_posts(
    current_user: UserDependency,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """
    获取用户转发的微博列表
    """
    # 查询用户转发的所有微博ID
    retweeted_post_ids = db.exec(
        select(PostRetweet.post_id).where(PostRetweet.user_id == current_user.id)
    ).all()

    # 查询这些微博的详细信息
    posts = db.exec(
        select(Post).where(Post.id.in_(retweeted_post_ids)).offset(skip).limit(limit)
    ).all()

    return posts

# Move these routes after /likes and /retweets
@router.get("/{post_id}", response_model=PostResponse)
async def get_post(
    post_id: UUID,
    db: DBSessionDependency
):
    """
    获取单条微博详情，包含关联的图片信息和作者信息
    """
    # 获取微博
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 加载附件关系
    db.refresh(post, ["attachments"])

    # 获取附件信息列表
    attached_files = []
    for attached in post.attachments:
        # 根据附件类型获取URL
        if attached.attached_type == "image":
            # 从diagram表获取图片URL
            from ..models.diagram import Diagram
            diagram = db.get(Diagram, attached.attached_id)
            if diagram and diagram.image_url:
                attached_files.append(diagram.image_url)
        elif attached.attached_type == "document":
            # 从document表获取文档URL
            from ..models.document import Document
            document = db.get(Document, attached.attached_id)
            if document and document.file_url:
                attached_files.append(document.file_url)

    # 获取作者信息
    owner_info = None
    if post.owner_id:
        user = db.get(User, post.owner_id)
        if user:
            owner_info = UserInfo(
                id=user.id,
                username=user.username,
                display_name=user.display_name,
                email=user.email,
                thumbnail=user.thumbnail
            )

    return PostResponse(
        post=post,
        images=attached_files,
        owner=owner_info
    )

@router.delete("/{post_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """
    删除微博
    """
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否是微博所有者
    if post.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this post"
        )

    # 删除相关的所有记录
    # 删除附件记录
    attached_records = db.exec(select(PostAttached).where(PostAttached.post_id == post_id)).all()
    for attached in attached_records:
        db.delete(attached)
    
    # 删除点赞记录
    like_records = db.exec(select(PostLike).where(PostLike.post_id == post_id)).all()
    for like in like_records:
        db.delete(like)
    
    # 删除转发记录
    retweet_records = db.exec(select(PostRetweet).where(PostRetweet.post_id == post_id)).all()
    for retweet in retweet_records:
        db.delete(retweet)
    
    # 删除以此帖子为原始帖子的转发记录
    original_retweet_records = db.exec(select(PostRetweet).where(PostRetweet.original_post_id == post_id)).all()
    for retweet in original_retweet_records:
        db.delete(retweet)
    
    # 删除相关的通知记录
    from ..models.notification import Notification
    notification_records = db.exec(
        select(Notification).where(
            (Notification.target_post_id == post_id) |
            (Notification.source_post_id == post_id) |
            (Notification.source_comment_id == post_id)
        )
    ).all()
    for notification in notification_records:
        db.delete(notification)

    # 删除微博
    db.delete(post)
    db.commit()

    return None

@router.put("/{post_id}", response_model=Post)
async def update_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency,
    content: str = Body(..., description="更新后的微博内容")
):
    """
    更新微博内容（仅文字内容，不修改附件）
    """
    # 获取微博
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否是微博所有者
    if post.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this post"
        )

    # 更新微博内容
    post.content = content
    db.add(post)
    db.commit()
    db.refresh(post)

    return post


@router.get("/{post_id}/likes/count")
async def get_post_likes_count(
    post_id: UUID,
    db: DBSessionDependency
):
    """
    获取帖子的点赞数
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 统计点赞数
    like_count = db.exec(
        select(func.count(PostLike.user_id)).where(PostLike.post_id == post_id)
    ).first() or 0

    return {"like_count": like_count}


@router.get("/{post_id}/likes/users")
async def get_post_likes_users(
    post_id: UUID,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """
    获取点赞帖子的用户列表
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 获取点赞用户信息（包含点赞时间）
    likes = db.exec(
        select(PostLike).where(PostLike.post_id == post_id).order_by(PostLike.created_at.desc()).offset(skip).limit(limit)
    ).all()

    # 构建用户信息列表
    users_info = []
    for like in likes:
        user = db.get(User, like.user_id)
        if user:
            users_info.append({
                "user": UserInfo(
                    id=user.id,
                    username=user.username,
                    display_name=user.display_name,
                    email=user.email,
                    thumbnail=user.thumbnail
                ),
                "liked_at": like.created_at
            })

    return users_info


@router.get("/{post_id}/likes/check")
async def check_user_liked_post(
    post_id: UUID,
    current_user: UserOptionalDependency,  # 改为可选
    db: DBSessionDependency
):
    """
    检查当前用户是否已点赞该帖子
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )
    if not current_user:
        return {"is_liked": False}
    # 检查是否已点赞
    existing_like = db.exec(
        select(PostLike).where(
            PostLike.post_id == post_id,
            PostLike.user_id == current_user.id
        )
    ).first()
    return {"is_liked": existing_like is not None}


@router.post("/{post_id}/like", status_code=status.HTTP_201_CREATED)
async def like_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """
    点赞微博
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否已经点赞
    existing_like = db.exec(
        select(PostLike).where(
            PostLike.post_id == post_id,
            PostLike.user_id == current_user.id
        )
    ).first()

    if existing_like:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You have already liked this post"
        )

    # 创建点赞记录
    new_like = PostLike(
        user_id=current_user.id,
        post_id=post_id
    )
    db.add(new_like)
    db.commit()

    # 创建点赞通知（如果不是自己点赞自己的帖子）
    if post.owner_id != current_user.id:
        from .notifications import create_like_notification
        await create_like_notification(db, current_user.id, post_id)

    # 返回更新后的点赞数
    like_count = db.exec(
        select(func.count(PostLike.user_id)).where(PostLike.post_id == post_id)
    ).first() or 0

    return {"detail": "Post liked successfully", "like_count": like_count}


@router.delete("/{post_id}/like", status_code=status.HTTP_204_NO_CONTENT)
async def unlike_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """
    取消点赞微博
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 查找点赞记录
    like = db.exec(
        select(PostLike).where(
            PostLike.post_id == post_id,
            PostLike.user_id == current_user.id
        )
    ).first()

    if not like:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="You have not liked this post"
        )

    # 删除点赞记录
    db.delete(like)
    db.commit()

    return None

from pydantic import BaseModel

class RetweetRequest(BaseModel):
    content: str
    images: List[str] = []

@router.post("/{post_id}/retweet", status_code=status.HTTP_201_CREATED)
async def retweet_post(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency,
    request: RetweetRequest
):
    try:
        """
        跟贴微博
        """
        # 检查原始微博是否存在
        original_post = db.get(Post, post_id)
        if not original_post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Original post not found"
            )

        # 递归查找祖先帖子channel_id
        def find_channel_id(post, db):
            while post and not post.channel_id and post.parent_id:
                post = db.get(Post, post.parent_id)
            return post.channel_id if post else None

        channel_id = find_channel_id(original_post, db)
        if not channel_id:
            raise HTTPException(status_code=400, detail="原始帖子无板块信息，无法评论")

        # 1. 首先创建一个新的评论帖子
        comment_post = Post(
            content=request.content,
            owner_id=current_user.id,
            timestamp=datetime.now(timezone.utc),
            type="COMMENT",  # 直接使用字符串而不是枚举
            parent_id=post_id,  # 设置父帖子ID
            channel_id=channel_id  # 递归查找祖先channel_id
        )
        db.add(comment_post)
        db.commit()
        db.refresh(comment_post)

        # 2. 处理图片（如果有）
        if request.images:
            for image_url in request.images:
                # 从 URL 中提取图片 ID
                from ..models.diagram import Diagram
                diagram = db.exec(
                    select(Diagram).where(Diagram.image_url == image_url)
                ).first()
                
                if diagram:
                    post_attached = PostAttached(
                        post_id=comment_post.id,
                        attached_id=diagram.id,  # 使用图片的实际 ID
                        attached_type="image"  # 指定为图片类型
                    )
                    db.add(post_attached)
            db.commit()

        # 3. 创建转发关系，将评论帖子与原始帖子关联
        new_retweet = PostRetweet(
            user_id=current_user.id,
            post_id=comment_post.id,  # 这是新创建的评论帖子ID
            original_post_id=post_id  # 这是原始帖子ID
            # timestamp 使用模型的默认值
        )
        db.add(new_retweet)
        db.commit()

        # 4. 处理通知
        from .notifications import create_mention_notifications, create_comment_notification
        
        # 处理 @ 提醒
        await create_mention_notifications(
            db=db,
            content=request.content,
            sender_id=current_user.id,
            post_id=post_id,
            comment_id=comment_post.id
        )
        
        # 创建评论通知（通知帖子作者）
        await create_comment_notification(
            db=db,
            sender_id=current_user.id,
            post_id=post_id,
            comment_id=comment_post.id,
            comment_content=request.content
        )

        return {
            "detail": "Post retweeted successfully",
            "comment_post_id": str(comment_post.id)
        }
    except Exception as e:
        import traceback
        print("=== Retweet Exception ===")
        print(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Retweet failed: {str(e)}")


@router.delete("/{post_id}/retweet", status_code=status.HTTP_204_NO_CONTENT)
async def undo_retweet(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """
    取消转发微博
    """
    # 检查微博是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 查找转发记录
    retweet = db.exec(
        select(PostRetweet).where(
            PostRetweet.post_id == post_id,
            PostRetweet.user_id == current_user.id
        )
    ).first()

    if not retweet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="You have not retweeted this post"
        )

    # 删除转发记录
    db.delete(retweet)
    db.commit()

    return None


@router.get("/{post_id}/comments", response_model=List[PostResponse])
async def get_post_comments(
    post_id: UUID,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """
    获取指定帖子的所有评论，包含作者信息
    """
    comments = db.exec(
        select(Post).where(Post.parent_id == post_id).offset(skip).limit(limit).order_by(Post.timestamp.desc())
    ).all()

    response_list = []
    for comment in comments:
        # 加载附件关系
        db.refresh(comment, ["attachments"])

        # 获取附件信息列表
        attached_files = []
        for attached in comment.attachments:
            # 根据附件类型获取URL
            if attached.attached_type == "image":
                # 从diagram表获取图片URL
                from ..models.diagram import Diagram
                diagram = db.get(Diagram, attached.attached_id)
                if diagram and diagram.image_url:
                    attached_files.append(diagram.image_url)
            elif attached.attached_type == "document":
                # 从document表获取文档URL
                from ..models.document import Document
                document = db.get(Document, attached.attached_id)
                if document and document.file_url:
                    attached_files.append(document.file_url)

        # 获取作者信息
        owner_info = None
        if comment.owner_id:
            user = db.get(User, comment.owner_id)
            if user:
                owner_info = UserInfo(
                    id=user.id,
                    username=user.username,
                    display_name=user.display_name,
                    email=user.email,
                    thumbnail=user.thumbnail
                )

        # 构建响应对象
        response_list.append(PostResponse(post=comment, images=attached_files, owner=owner_info))

    return response_list

# 可选：添加缓存支持
from functools import lru_cache
from datetime import datetime, timedelta

# 缓存总数查询结果（5分钟过期）
_count_cache = {}
_cache_expiry = {}

def get_cached_total_count(db: Session) -> int:
    """获取缓存的总数，如果缓存过期则重新查询"""
    cache_key = "total_posts_count"
    now = datetime.now()
    
    # 检查缓存是否存在且未过期
    if (cache_key in _count_cache and 
        cache_key in _cache_expiry and 
        _cache_expiry[cache_key] > now):
        return _count_cache[cache_key]
    
    # 重新查询并更新缓存
    total_count = db.exec(
        select(func.count(Post.id)).where(Post.type == 'NORMAL')
    ).first() or 0
    
    _count_cache[cache_key] = total_count
    _cache_expiry[cache_key] = now + timedelta(minutes=5)
    
    return total_count

@router.patch("/{post_id}/channel")
async def update_post_channel(
    post_id: UUID,
    channel_data: dict,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """更新帖子的板块归属（仅管理员可操作）"""
    # 检查管理员权限
    if current_user.role != 'admin':
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can change post channels"
        )
    
    # 获取帖子
    post = db.exec(select(Post).where(Post.id == post_id)).first()
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )
    
    channel_id = channel_data.get('channel_id')
    if channel_id:
        # 验证频道是否存在
        channel = db.exec(select(Channel).where(Channel.id == channel_id)).first()
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Channel not found"
            )
        
        if not channel.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Channel is not active"
            )
    
    # 更新帖子的频道
    old_channel_id = post.channel_id
    post.channel_id = channel_id
    db.add(post)
    
    # 更新频道的帖子计数
    if old_channel_id:
        old_channel = db.exec(select(Channel).where(Channel.id == old_channel_id)).first()
        if old_channel and old_channel.post_count > 0:
            old_channel.post_count -= 1
            db.add(old_channel)
    
    if channel_id:
        new_channel = db.exec(select(Channel).where(Channel.id == channel_id)).first()
        if new_channel:
            new_channel.post_count += 1
            db.add(new_channel)
    
    db.commit()
    
    return {"detail": "Post channel updated successfully"}

@router.post("/admin/fix_missing_channel_ids", tags=["admin"])
def fix_missing_channel_ids(db: DBSessionDependency, user: UserDependency):
    """
    递归补全所有 channel_id 为空的帖子，将其 channel_id 设置为最近的祖先有 channel_id 的帖子。
    仅限管理员调用。
    """
    # 权限校验（假设有 is_admin 字段）
    if not getattr(user, 'role', None) == 'admin':
        raise HTTPException(status_code=403, detail="仅管理员可操作")

    from app.models import Post
    # 查询所有 channel_id 为空的帖子
    posts = db.exec(select(Post).where(Post.channel_id == None)).all()
    fixed_count = 0
    for post in posts:
        ancestor = post
        # 递归向上查找有 channel_id 的祖先
        while ancestor and not ancestor.channel_id and ancestor.parent_id:
            ancestor = db.get(Post, ancestor.parent_id)
        if ancestor and ancestor.channel_id:
            post.channel_id = ancestor.channel_id
            db.add(post)
            fixed_count += 1
    db.commit()
    return {"fixed": fixed_count, "total": len(posts)}

class PostReplyTreeResponse(BaseModel):
    post: PostResponse
    replies: list['PostReplyTreeResponse'] = []

@router.get("/{post_id}/replies", response_model=PostReplyTreeResponse)
async def get_post_replies_tree(
    post_id: UUID,
    db: DBSessionDependency,
):
    """
    递归获取某条帖子的所有回复（树结构）
    """
    def build_tree(post_id: UUID) -> PostReplyTreeResponse:
        # 获取当前帖子
        post = db.get(Post, post_id)
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        # 加载附件关系
        db.refresh(post, ["attachments"])
        attached_files = []
        for attached in post.attachments:
            if attached.attached_type == "image":
                from ..models.diagram import Diagram
                diagram = db.get(Diagram, attached.attached_id)
                if diagram and diagram.image_url:
                    attached_files.append(diagram.image_url)
            elif attached.attached_type == "document":
                from ..models.document import Document
                document = db.get(Document, attached.attached_id)
                if document and document.file_url:
                    attached_files.append(document.file_url)
        owner_info = None
        if post.owner_id:
            user = db.get(User, post.owner_id)
            if user:
                owner_info = UserInfo(
                    id=user.id,
                    username=user.username,
                    display_name=user.display_name,
                    email=user.email,
                    thumbnail=user.thumbnail
                )
        channel_info = None
        if post.channel_id:
            from ..models.channel import Channel
            channel = db.get(Channel, post.channel_id)
            if channel:
                channel_info = ChannelInfo(
                    id=channel.id,
                    name=channel.name,
                    display_name=channel.display_name,
                    color=channel.color,
                    icon=channel.icon
                )
        post_response = PostResponse(post=post, images=attached_files, owner=owner_info, channel=channel_info)
        # 获取所有直接回复
        children = db.exec(select(Post).where(Post.parent_id == post_id).order_by(Post.timestamp.asc())).all()
        replies = [build_tree(child.id) for child in children]
        return PostReplyTreeResponse(post=post_response, replies=replies)
    return build_tree(post_id)
# ==================== 图片管理 API ====================

class ImageDeleteRequest(BaseModel):
    image_url: str

class ImageUploadResponse(BaseModel):
    image_urls: List[str]
    message: str



@router.delete("/{post_id}/images", status_code=status.HTTP_200_OK)
async def delete_post_image(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency,
    request: dict = Body(...)
):
    """
    删除帖子中的指定图片
    """
    # 从请求中获取图片URL
    image_url = request.get("image_url")
    if not image_url:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing image_url in request body"
        )
    # 检查帖子是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否是帖子所有者
    if post.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to modify this post"
        )

    # 查找对应的图片记录
    from ..models.diagram import Diagram

    # 尝试精确匹配
    diagram = db.exec(
        select(Diagram).where(Diagram.image_url == image_url)
    ).first()

    # 如果精确匹配失败，尝试去除查询参数后匹配
    if not diagram:
        # 去除URL中的查询参数
        clean_image_url = image_url.split('?')[0]
        diagram = db.exec(
            select(Diagram).where(
                Diagram.image_url.like(f"{clean_image_url}%")
            )
        ).first()

    if not diagram:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Image not found"
        )

    # 查找PostAttached记录
    post_attached = db.exec(
        select(PostAttached).where(
            PostAttached.post_id == post_id,
            PostAttached.attached_id == diagram.id,
            PostAttached.attached_type == "image"
        )
    ).first()

    if not post_attached:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Image not attached to this post"
        )

    try:
        # 删除PostAttached关联记录
        db.delete(post_attached)
        
        # 检查这个diagram是否还被其他帖子使用
        other_attachments = db.exec(
            select(PostAttached).where(
                PostAttached.attached_id == diagram.id,
                PostAttached.attached_type == "image",
                PostAttached.post_id != post_id
            )
        ).first()

        # 如果没有其他帖子使用这个图片，删除diagram记录
        # 注意：这里不删除存储中的实际文件，因为可能有其他引用
        if not other_attachments:
            db.delete(diagram)
        
        db.commit()
        
        return {"message": "Image deleted successfully"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete image: {str(e)}"
        )

@router.post("/{post_id}/images", response_model=ImageUploadResponse, status_code=status.HTTP_201_CREATED)
async def add_post_images(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency,
    images: List[UploadFile] = File(...)
):
    """
    为帖子添加新图片
    """
    # 检查帖子是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否是帖子所有者
    if post.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to modify this post"
        )

    # 检查当前帖子的图片数量
    current_image_count = db.exec(
        select(func.count(PostAttached.id)).where(
            PostAttached.post_id == post_id,
            PostAttached.attached_type == "image"
        )
    ).first() or 0

    # 检查图片数量限制（最多9张）
    if current_image_count + len(images) > 9:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot add {len(images)} images. Current: {current_image_count}, Max: 9"
        )

    # 过滤有效图片 - 不能使用 img.size
    valid_images = []
    for img in images:
        if img and img.filename:
            try:
                await img.seek(0)
                content = await img.read()
                if len(content) > 0:
                    await img.seek(0)  # 重置文件指针
                    valid_images.append(img)
            except Exception as e:
                logger.warning(f"检查图片 {img.filename} 时出错: {str(e)}")

    if not valid_images:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No valid images provided"
        )

    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"开始为帖子 {post_id} 添加 {len(valid_images)} 张图片")

    uploaded_diagrams = []
    uploaded_image_urls = []

    try:
        import time
        import uuid
        
        # 处理每张图片
        for index, image in enumerate(valid_images):
            # 验证图片文件并计算大小
            if not image.filename:
                logger.warning(f"图片 {index} 无效，跳过")
                continue

            # 计算文件大小
            await image.seek(0)
            file_content = await image.read()
            file_size = len(file_content)
            await image.seek(0)

            if file_size == 0:
                logger.warning(f"图片 {index} 大小为0，跳过")
                continue

            # 检查文件大小 (10MB)
            if file_size > 10 * 1024 * 1024:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Image {image.filename} is too large (max 10MB)"
                )
            
            # 检查文件类型
            if not image.content_type or not image.content_type.startswith('image/'):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File {image.filename} is not a valid image"
                )
            
            # 生成文件路径
            storage_path = f"{current_user.id}"
            file_ext = os.path.splitext(image.filename)[1]
            unique_id = str(uuid.uuid4())[:8]
            timestamp = int(time.time() * 1000)
            file_name = f"{post_id}_{index}_{timestamp}_{unique_id}{file_ext}"
            full_path = f"{storage_path}/{file_name}"
            
            # 重置文件指针
            await image.seek(0)
            
            # 上传图片到存储
            logger.info(f"上传图片 {index + 1}: {image.filename}")
            image_url = await upload_file_to_storage(
                file=image,
                bucket="diagrams",
                path=full_path
            )
            
            # 创建diagram记录
            diagram_data = {
                "name": f"Post_{post_id}_{index}_{timestamp}_{unique_id}",
                "description": "Post attachment",
                "source": str(post_id),
                "type": "blog",
                "content_type": image.content_type,
                "file_size": file_size,
                "image_url": image_url,
            }
            
            from ..db.diagram_crud import create_diagram
            db_diagram = create_diagram(db, diagram_data, user_id=current_user.id)
            uploaded_diagrams.append(db_diagram)
            uploaded_image_urls.append(image_url)
            
            # 创建关联记录
            post_attached = PostAttached(
                post_id=post_id,
                attached_id=db_diagram.id,
                attached_type="image"
            )
            db.add(post_attached)
            
            logger.info(f"图片 {index + 1} 处理完成")
        
        # 提交所有更改
        db.commit()
        logger.info(f"成功为帖子 {post_id} 添加了 {len(uploaded_image_urls)} 张图片")
        
        return ImageUploadResponse(
            image_urls=uploaded_image_urls,
            message=f"Successfully added {len(uploaded_image_urls)} images"
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        db.rollback()
        raise
    except Exception as e:
        # 处理其他异常
        db.rollback()
        logger.error(f"添加图片失败: {str(e)}")
        
        # 尝试清理已上传的diagram记录
        for diagram in uploaded_diagrams:
            try:
                db.delete(diagram)
            except:
                pass
        
        try:
            db.commit()
        except:
            db.rollback()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add images: {str(e)}"
        )

@router.get("/{post_id}/images", response_model=List[str])
async def get_post_images(
    post_id: UUID,
    db: DBSessionDependency
):
    """
    获取帖子的所有图片URL列表
    """
    # 检查帖子是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 获取帖子的所有图片附件
    attached_records = db.exec(
        select(PostAttached).where(
            PostAttached.post_id == post_id,
            PostAttached.attached_type == "image"
        )
    ).all()

    # 获取图片URL列表
    image_urls = []
    for attached in attached_records:
        from ..models.diagram import Diagram
        diagram = db.get(Diagram, attached.attached_id)
        if diagram and diagram.image_url:
            image_urls.append(diagram.image_url)

    return image_urls

@router.put("/{post_id}/images", response_model=ImageUploadResponse)
async def replace_post_image(
    post_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency,
    old_image_url: str = Form(...),
    new_image: UploadFile = File(...)
):
    """
    替换帖子中的指定图片
    """
    # 检查帖子是否存在
    post = db.get(Post, post_id)
    if not post:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Post not found"
        )

    # 检查是否是帖子所有者
    if post.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to modify this post"
        )

    # 验证新图片并计算大小
    if not new_image or not new_image.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid image file"
        )

    # 计算文件大小
    await new_image.seek(0)
    file_content = await new_image.read()
    file_size = len(file_content)
    await new_image.seek(0)

    if file_size == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid image file"
        )

    # 检查文件大小 (10MB)
    if file_size > 10 * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Image is too large (max 10MB)"
        )

    # 检查文件类型
    if not new_image.content_type or not new_image.content_type.startswith('image/'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File is not a valid image"
        )

    # 查找要替换的图片记录
    from ..models.diagram import Diagram
    old_diagram = db.exec(
        select(Diagram).where(Diagram.image_url == old_image_url)
    ).first()
    
    if not old_diagram:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Old image not found"
        )

    # 查找PostAttached记录
    post_attached = db.exec(
        select(PostAttached).where(
            PostAttached.post_id == post_id,
            PostAttached.attached_id == old_diagram.id,
            PostAttached.attached_type == "image"
        )
    ).first()

    if not post_attached:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Image not attached to this post"
        )

    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"替换帖子 {post_id} 的图片: {old_image_url}")

    try:
        import time
        import uuid
        
        # 生成新文件路径
        storage_path = f"{current_user.id}"
        file_ext = os.path.splitext(new_image.filename)[1]
        unique_id = str(uuid.uuid4())[:8]
        timestamp = int(time.time() * 1000)
        file_name = f"{post_id}_replace_{timestamp}_{unique_id}{file_ext}"
        full_path = f"{storage_path}/{file_name}"
        
        # 重置文件指针
        await new_image.seek(0)
        
        # 上传新图片
        logger.info(f"上传新图片: {new_image.filename}")
        new_image_url = await upload_file_to_storage(
            file=new_image,
            bucket="diagrams",
            path=full_path
        )
        
        # 创建新的diagram记录
        diagram_data = {
            "name": f"Post_{post_id}_replace_{timestamp}_{unique_id}",
            "description": "Post attachment (replaced)",
            "source": str(post_id),
            "type": "blog",
            "content_type": new_image.content_type,
            "file_size": file_size,
            "image_url": new_image_url,
        }
        
        from ..db.diagram_crud import create_diagram
        new_diagram = create_diagram(db, diagram_data, user_id=current_user.id)
        
        # 更新PostAttached记录指向新图片
        post_attached.attached_id = new_diagram.id
        db.add(post_attached)
        
        # 检查旧图片是否还被其他帖子使用
        other_attachments = db.exec(
            select(PostAttached).where(
                PostAttached.attached_id == old_diagram.id,
                PostAttached.attached_type == "image",
                PostAttached.post_id != post_id
            )
        ).first()

        # 如果没有其他帖子使用旧图片，删除旧diagram记录
        if not other_attachments:
            db.delete(old_diagram)
        
        db.commit()
        logger.info(f"成功替换图片: {old_image_url} -> {new_image_url}")
        
        return ImageUploadResponse(
            image_urls=[new_image_url],
            message="Image replaced successfully"
        )
        
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"替换图片失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to replace image: {str(e)}"
        )