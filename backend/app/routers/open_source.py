from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session
from app.db.open_source_crud import *
from app.schemas.open_source import *
from app.dependencies import get_db_session, get_current_user
from app.models import User
from typing import List, Optional

router = APIRouter(prefix="/api/open-source", tags=["open-source"])

@router.get("/", response_model=OpenSourceProjectListResponse)
def list_projects(skip: int = 0, limit: int = 20, db: Session = Depends(get_db_session)):
    return get_projects(db, skip, limit)

@router.get("/{project_id}", response_model=OpenSourceProjectOut)
def get_project_detail(project_id: str, db: Session = Depends(get_db_session)):
    project = get_project(db, project_id)
    if not project or (project.is_private):
        raise HTTPException(status_code=404, detail="Project not found")
    return project

@router.post("/", response_model=OpenSourceProjectOut)
def create_project_api(project: OpenSourceProjectCreate, db: Session = Depends(get_db_session), user: User = Depends(get_current_user)):
    return create_project(db, project, user.id)

@router.put("/{project_id}", response_model=OpenSourceProjectOut)
def update_project_api(project_id: str, project: OpenSourceProjectUpdate, db: Session = Depends(get_db_session), user: User = Depends(get_current_user)):
    db_project = update_project(db, project_id, project, user.id)
    if not db_project:
        raise HTTPException(status_code=403, detail="No permission or not found")
    return db_project

@router.delete("/{project_id}")
def delete_project_api(project_id: str, db: Session = Depends(get_db_session), user: User = Depends(get_current_user)):
    db_project = delete_project(db, project_id, user.id)
    if not db_project:
        raise HTTPException(status_code=403, detail="No permission or not found")
    return {"ok": True}

@router.get("/{project_id}/discussions", response_model=List[OpenSourceDiscussionOut])
def list_discussions_api(project_id: str, db: Session = Depends(get_db_session)):
    return get_discussions(db, project_id)

@router.post("/{project_id}/discussions", response_model=OpenSourceDiscussionOut)
def create_discussion_api(project_id: str, discussion: OpenSourceDiscussionCreate, db: Session = Depends(get_db_session), user: Optional[User] = Depends(get_current_user)):
    user_id = user.id if user else None
    return create_discussion(db, project_id, user_id, discussion.content)

@router.patch("/{project_id}/rate", response_model=OpenSourceProjectOut)
def update_project_rate(project_id: str, rate_data: dict, db: Session = Depends(get_db_session), user: User = Depends(get_current_user)):
    # 检查用户是否为管理员
    if user.role != "admin":
        raise HTTPException(status_code=403, detail="Only administrators can update project ratings")
    
    # 验证rate值
    rate = rate_data.get("rate")
    if rate is None or not isinstance(rate, (int, float)) or rate < 0 or rate > 5:
        raise HTTPException(status_code=400, detail="Rate must be a number between 0 and 5")
    
    # 更新项目评分
    db_project = update_project_rate_by_admin(db, project_id, rate)
    if not db_project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return db_project 