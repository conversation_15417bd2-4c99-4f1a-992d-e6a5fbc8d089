from fastapi import APIRouter, Depends, HTTPException, status
from typing import List
from uuid import UUID
import re

from sqlmodel import Session
from ..dependencies import DBSessionDependency, UserDependency
from ..schemas.notification import (
    NotificationResponse, 
    NotificationUpdate, 
    UserSearchResult,
    NotificationCreate
)
from ..db.notification_crud import (
    get_user_notifications,
    mark_notification_as_read,
    mark_all_notifications_as_read,
    get_unread_count,
    search_users_by_username,
    create_notification
)
from ..models.notification import NotificationType
from ..models.user import User
from ..models.channel import UserChannelSubscription

router = APIRouter(prefix="/api/notifications", tags=["notifications"])

@router.get("/", response_model=List[NotificationResponse])
async def get_notifications(
    current_user: UserDependency,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """获取用户的所有通知"""
    return get_user_notifications(db, current_user.id, skip, limit)

@router.get("/unread", response_model=List[NotificationResponse])
async def get_unread_notifications(
    current_user: UserDependency,
    db: DBSessionDependency,
    skip: int = 0,
    limit: int = 20
):
    """获取用户的未读通知"""
    return get_user_notifications(db, current_user.id, skip, limit, unread_only=True)

@router.get("/unread/count", response_model=dict)
async def get_unread_count_endpoint(
    current_user: UserDependency,
    db: DBSessionDependency
):
    """获取用户未读通知数量"""
    count = get_unread_count(db, current_user.id)
    return {"count": count}

@router.patch("/{notification_id}/read", response_model=dict)
async def mark_notification_read(
    notification_id: UUID,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """标记单个通知为已读"""
    notification = mark_notification_as_read(db, notification_id, current_user.id)
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    return {"detail": "Notification marked as read"}

@router.patch("/read-all", response_model=dict)
async def mark_all_read(
    current_user: UserDependency,
    db: DBSessionDependency
):
    """标记所有通知为已读"""
    count = mark_all_notifications_as_read(db, current_user.id)
    return {"detail": f"Marked {count} notifications as read"}

@router.post("/create", response_model=dict)
async def create_notification_endpoint(
    notification_data: NotificationCreate,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """创建新通知"""
    # 验证发送者是当前用户
    if notification_data.sender_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Cannot create notification for another user"
        )
    
    # 不能给自己发通知
    if notification_data.user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot send notification to yourself"
        )
    
    notification = create_notification(db, notification_data)
    return {"detail": "Notification created successfully", "id": str(notification.id)}

@router.post("/create", response_model=dict)
async def create_notification_endpoint(
    notification_data: NotificationCreate,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """创建通知（用于测试和手动创建）"""
    try:
        notification = create_notification(db, notification_data)
        return {"detail": "Notification created successfully", "id": str(notification.id)}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create notification: {str(e)}"
        )

@router.get("/users/search", response_model=List[UserSearchResult])
async def search_users(
    q: str,
    db: DBSessionDependency,
    limit: int = 10
):
    """搜索用户（用于 @ 自动补全）"""
    print(f"🔍 [DEBUG] 用户搜索请求 - 查询参数: '{q}', 限制: {limit}")
    
    if len(q.strip()) < 1:
        print("❌ [DEBUG] 查询参数太短，返回空列表")
        return []
    
    users = search_users_by_username(db, q.strip(), limit)
    print(f"📊 [DEBUG] 数据库查询结果: 找到 {len(users)} 个用户")
    
    result = [
        UserSearchResult(
            id=user.id,
            username=user.username,
            display_name=user.display_name,
            thumbnail=user.thumbnail
        )
        for user in users
    ]
    
    print(f"✅ [DEBUG] 返回用户列表: {[{'username': u.username, 'display_name': u.display_name} for u in result]}")
    return result

def extract_mentions(content: str) -> List[str]:
    """从内容中提取 @ 用户名"""
    # 匹配 @username 格式，用户名可以包含字母、数字、下划线、中文
    pattern = r'@([a-zA-Z0-9_\u4e00-\u9fa5]+)'
    mentions = re.findall(pattern, content)
    return list(set(mentions))  # 去重

async def create_mention_notifications(
    db: Session,
    content: str,
    sender_id: UUID,
    post_id: UUID,
    comment_id: UUID = None
):
    """创建 @ 提醒通知"""
    mentions = extract_mentions(content)
    
    if not mentions:
        return
    
    # 查找被 @ 的用户
    from sqlmodel import select
    for username in mentions:
        user = db.exec(
            select(User).where(User.username == username)
        ).first()
        
        if user and user.id != sender_id:  # 不给自己发通知
            notification_data = NotificationCreate(
                type=NotificationType.MENTION,
                content=content[:200],  # 截取前200个字符
                user_id=user.id,
                sender_id=sender_id,
                target_post_id=post_id,
                source_post_id=post_id,
                source_comment_id=comment_id
            )
            create_notification(db, notification_data)

async def create_comment_notification(
    db: Session,
    sender_id: UUID,
    post_id: UUID,
    comment_id: UUID,
    comment_content: str
):
    """创建评论通知（通知帖子作者）"""
    from sqlmodel import select
    from ..models.post import Post
    
    # 获取原始帖子信息
    original_post = db.exec(
        select(Post).where(Post.id == post_id)
    ).first()
    
    if not original_post or original_post.owner_id == sender_id:
        # 如果帖子不存在或者是自己评论自己的帖子，不发送通知
        return
    
    # 创建评论通知
    notification_data = NotificationCreate(
        type=NotificationType.COMMENT,
        content=comment_content[:200],  # 截取前200个字符
        user_id=original_post.owner_id,  # 通知帖子作者
        sender_id=sender_id,  # 评论者
        target_post_id=post_id,  # 被评论的帖子
        source_post_id=post_id,
        source_comment_id=comment_id  # 评论ID
    )
    create_notification(db, notification_data)

async def create_like_notification(
    db: Session,
    sender_id: UUID,
    post_id: UUID
):
    """创建点赞通知"""
    from sqlmodel import select
    from ..models.post import Post
    
    # 获取被点赞的帖子信息
    post = db.exec(
        select(Post).where(Post.id == post_id)
    ).first()
    
    if not post or post.owner_id == sender_id:
        # 如果帖子不存在或者是自己点赞自己的帖子，不发送通知
        return
    
    # 创建点赞通知
    notification_data = NotificationCreate(
        type=NotificationType.LIKE,
        content="点赞了你的帖子",
        user_id=post.owner_id,  # 通知帖子作者
        sender_id=sender_id,  # 点赞者
        target_post_id=post_id,  # 被点赞的帖子
        source_post_id=post_id
    )
    create_notification(db, notification_data)

async def create_follow_notification(
    db: Session,
    follower_id: UUID,
    followed_id: UUID
):
    """创建关注通知"""
    if follower_id == followed_id:
        # 不能关注自己
        return
    
    # 创建关注通知
    notification_data = NotificationCreate(
        type=NotificationType.FOLLOW,
        content="关注了你",
        user_id=followed_id,  # 被关注的用户
        sender_id=follower_id,  # 关注者
    )
    create_notification(db, notification_data)

async def create_channel_post_notification(
    db: Session,
    channel_id: UUID,
    post_id: UUID,
    author_id: UUID
):
    """创建频道新帖通知"""
    from sqlmodel import select
    
    # 获取频道的所有订阅者
    subscribers = db.exec(
        select(UserChannelSubscription).where(
            UserChannelSubscription.channel_id == channel_id,
            UserChannelSubscription.notification_enabled == True,
            UserChannelSubscription.is_active == True,
            UserChannelSubscription.user_id != author_id  # 不通知作者自己
        )
    ).all()
    
    # 为每个订阅者创建通知
    for subscription in subscribers:
        notification_data = NotificationCreate(
            type=NotificationType.CHANNEL_POST,
            content="在你关注的频道发布了新帖子",
            user_id=subscription.user_id,
            sender_id=author_id,
            target_post_id=post_id,
            channel_id=channel_id
        )
        create_notification(db, notification_data)

async def create_follow_notification(
    db: Session,
    follower_id: UUID,
    followed_id: UUID
):
    """创建关注通知"""
    if follower_id == followed_id:
        # 不能关注自己
        return
    
    # 创建关注通知
    notification_data = NotificationCreate(
        type=NotificationType.FOLLOW,
        content="关注了你",
        user_id=followed_id,  # 被关注的用户
        sender_id=follower_id  # 关注者
    )
    create_notification(db, notification_data)

async def create_channel_post_notification(
    db: Session,
    author_id: UUID,
    post_id: UUID,
    channel_id: UUID
):
    """创建频道新帖通知"""
    from sqlmodel import select
    
    # 获取频道的所有订阅者
    subscribers = db.exec(
        select(UserChannelSubscription).where(
            UserChannelSubscription.channel_id == channel_id,
            UserChannelSubscription.notification_enabled == True,
            UserChannelSubscription.is_active == True,
            UserChannelSubscription.user_id != author_id  # 不通知作者自己
        )
    ).all()
    
    # 为每个订阅者创建通知
    for subscription in subscribers:
        notification_data = NotificationCreate(
            type=NotificationType.CHANNEL_POST,
            content="在你关注的频道发布了新帖子",
            user_id=subscription.user_id,
            sender_id=author_id,
            target_post_id=post_id,
            source_post_id=post_id,
            channel_id=channel_id
        )
        create_notification(db, notification_data)