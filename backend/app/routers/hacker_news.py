import asyncio
from fastapi import APIRouter, HTTPException
from hn import Client

router = APIRouter(
    prefix="/api/hacker-news",
    tags=["hacker-news"],
)

class HNStory(object):
    def __init__(self, item):
        self.id = item.item_id
        self.title = item.title
        self.url = item.url
        self.points = item.score
        self.author = item.by
        self.commentsCount = item.descendants
        
    def to_dict(self):
        return {
            "id": self.id,
            "title": self.title,
            "url": self.url,
            "points": self.points,
            "author": self.author,
            "commentsCount": self.commentsCount,
        }

@router.get("/top-stories")
async def get_top_stories():
    """
    Fetches the top stories from Hacker News.
    """
    try:
        client = Client()
        top_stories_ids = client.get_topstories()[:10]

        async def get_story_details(story_id):
            loop = asyncio.get_event_loop()
            item_dict = await loop.run_in_executor(None, client.get_item, story_id)
            if item_dict and item_dict.get('type') == 'story' and item_dict.get('url'):
                 # Reconstruct an object that HNStory expects
                 class TempItem:
                     def __init__(self, d):
                         self.item_id = d.get('id')
                         self.title = d.get('title')
                         self.url = d.get('url')
                         self.score = d.get('score')
                         self.by = d.get('by')
                         self.descendants = d.get('descendants')
                 
                 item = TempItem(item_dict)
                 return HNStory(item).to_dict()
            return None

        tasks = [get_story_details(story_id) for story_id in top_stories_ids]
        stories = await asyncio.gather(*tasks)
        
        # Filter out None results
        stories = [story for story in stories if story]

        return stories
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 