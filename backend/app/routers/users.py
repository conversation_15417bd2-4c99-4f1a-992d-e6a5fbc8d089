import logging
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Security, Body
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.models import User
from ..dependencies import DBSessionDependency, SupabaseDependency, UserDependency
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/users", tags=["users"])
security = HTTPBearer(auto_error=False)

# 获取所有用户列表
@router.get("/")
async def get_all_users(
    supabase: SupabaseDependency,
    db: DBSessionDependency,
    credentials: HTTPAuthorizationCredentials = Security(security)
):
    # 验证用户权限
    if credentials is None:
        raise HTTPException(
            status_code=401,
            detail="请提供有效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 验证token并获取当前用户
        user_response = supabase.auth.get_user(credentials.credentials)
        current_user_id = user_response.user.id
        
        # 检查当前用户是否为管理员
        current_user = db.query(User).filter(User.id == current_user_id).first()
        if not current_user or current_user.role != "admin":
            raise HTTPException(status_code=403, detail="只有管理员可以访问此功能")
        
        # 获取所有用户
        users = db.query(User).all()
        
        return [
            {
                "id": user.id,
                "email": user.email,
                "username": user.username,
                "display_name": user.display_name,
                "role": user.role,
                "is_active": user.is_active,
                "created_at": user.created_at,
                "last_login": user.last_login,
                "thumbnail": user.thumbnail  # 添加thumbnail字段
            } for user in users
        ]
    except Exception as e:
        logger.error(f"获取用户列表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

# 更新用户状态（激活/停用）
@router.patch("/{user_id}/status")
async def update_user_status(
    user_id: UUID,
    supabase: SupabaseDependency,
    db: DBSessionDependency,
    credentials: HTTPAuthorizationCredentials = Security(security),
    data: dict = Body(...) # 添加请求体参数
):
    # 验证用户权限
    if credentials is None:
        raise HTTPException(
            status_code=401,
            detail="请提供有效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 验证token并获取当前用户
        user_response = supabase.auth.get_user(credentials.credentials)
        current_user_id = user_response.user.id
        
        # 检查当前用户是否为管理员
        current_user = db.query(User).filter(User.id == current_user_id).first()
        if not current_user or current_user.role != "admin":
            raise HTTPException(status_code=403, detail="只有管理员可以访问此功能")
        
        # 获取要更新的用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 从请求体获取is_active参数
        is_active = data.get("is_active")
        if is_active is None:
            raise HTTPException(status_code=422, detail="缺少is_active参数")
        
        # 更新用户状态
        user.is_active = is_active
        db.commit()
        
        return {"message": "用户状态已更新", "user_id": user_id, "is_active": is_active}
    except Exception as e:
        logger.error(f"更新用户状态时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新用户状态失败: {str(e)}")

# 更新用户角色
@router.patch("/{user_id}/role")
async def update_user_role(
    user_id: UUID,
    supabase: SupabaseDependency,
    db: DBSessionDependency,
    credentials: HTTPAuthorizationCredentials = Security(security),
    data: dict = Body(...)  # 添加请求体参数
):
    # 验证用户权限
    if credentials is None:
        raise HTTPException(
            status_code=401,
            detail="请提供有效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 从请求体获取role参数
    role = data.get("role")
    if not role:
        raise HTTPException(status_code=422, detail="缺少role参数")
    
    # 验证角色值
    valid_roles = ["admin", "user"]
    if role not in valid_roles:
        raise HTTPException(status_code=400, detail=f"无效的角色值，有效值为: {valid_roles}")
    
    try:
        # 验证token并获取当前用户
        user_response = supabase.auth.get_user(credentials.credentials)
        current_user_id = user_response.user.id
        
        # 检查当前用户是否为管理员
        current_user = db.query(User).filter(User.id == current_user_id).first()
        if not current_user or current_user.role != "admin":
            raise HTTPException(status_code=403, detail="只有管理员可以访问此功能")
        
        # 获取要更新的用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 更新用户角色
        user.role = role
        db.commit()
        
        return {"message": "用户角色已更新", "user_id": user_id, "role": role}
    except Exception as e:
        logger.error(f"更新用户角色时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新用户角色失败: {str(e)}")

# 更新用户信息（显示名称、头像、角色）
@router.patch("/{user_id}/info")
async def update_user_info(
    user_id: UUID,
    supabase: SupabaseDependency,
    db: DBSessionDependency,
    credentials: HTTPAuthorizationCredentials = Security(security),
    data: dict = Body(...)  # 添加请求体参数
):
    # 验证用户权限
    if credentials is None:
        raise HTTPException(
            status_code=401,
            detail="请提供有效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 验证token并获取当前用户
        user_response = supabase.auth.get_user(credentials.credentials)
        current_user_id = user_response.user.id
        
        # 检查当前用户权限：管理员可以更新任何用户，普通用户只能更新自己
        current_user = db.query(User).filter(User.id == current_user_id).first()
        if not current_user:
            raise HTTPException(status_code=404, detail="当前用户不存在")
        
        # 权限检查：管理员可以更新任何用户，普通用户只能更新自己
        if current_user.role != "admin" and str(current_user_id) != str(user_id):
            raise HTTPException(status_code=403, detail="您只能更新自己的信息")
        
        # 获取要更新的用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 更新用户信息
        updated_fields = []
        
        # 更新显示名称
        if "display_name" in data:
            display_name = data["display_name"]
            if display_name and display_name.strip():
                user.display_name = display_name.strip()
                updated_fields.append("显示名称")
        
        # 更新头像
        if "thumbnail" in data:
            thumbnail = data["thumbnail"]
            user.thumbnail = thumbnail if thumbnail else None
            updated_fields.append("头像")
        
        # 更新角色（只有管理员可以更新角色）
        if "role" in data:
            if current_user.role != "admin":
                raise HTTPException(status_code=403, detail="只有管理员可以更新用户角色")
            role = data["role"]
            valid_roles = ["admin", "user"]
            if role in valid_roles:
                user.role = role
                updated_fields.append("角色")
            else:
                raise HTTPException(status_code=400, detail=f"无效的角色值，有效值为: {valid_roles}")
        
        if not updated_fields:
            raise HTTPException(status_code=422, detail="没有提供要更新的字段")
        
        db.commit()
        
        return {
            "message": f"用户信息已更新: {', '.join(updated_fields)}",
            "user_id": user_id,
            "updated_fields": updated_fields
        }
    except Exception as e:
        logger.error(f"更新用户信息时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新用户信息失败: {str(e)}")

# 获取用户列表用于@功能（简化版本）
@router.get("/mention")
async def get_users_for_mention(
    supabase: SupabaseDependency,
    db: DBSessionDependency,
    credentials: HTTPAuthorizationCredentials = Security(security)
):
    """
    获取用于@功能的用户列表
    返回简化的用户信息：id, display_name, username
    只返回活跃用户
    """
    # 验证用户权限
    if credentials is None:
        raise HTTPException(
            status_code=401,
            detail="请提供有效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 验证token（确保是有效的登录用户）
        user_response = supabase.auth.get_user(credentials.credentials)
        current_user_id = user_response.user.id
        
        # 验证当前用户存在且活跃
        current_user = db.query(User).filter(
            User.id == current_user_id,
            User.is_active == True
        ).first()
        
        if not current_user:
            raise HTTPException(status_code=403, detail="用户不存在或已被停用")
        
        # 获取所有活跃用户的简化信息
        users = db.query(User).filter(User.is_active == True).all()
        
        return [
            {
                "id": str(user.id),
                "display_name": user.display_name or user.username,
                "username": user.username
            } for user in users
        ]
        
    except Exception as e:
        logger.error(f"获取@用户列表时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

# 通知偏好设置模型
class NotificationPreferences(BaseModel):
    mention: bool = True
    comment: bool = True
    like: bool = True
    follow: bool = True
    channel_post: bool = True
    email_notifications: bool = False
    push_notifications: bool = True

# 获取用户通知偏好设置
@router.get("/notification-preferences", response_model=NotificationPreferences)
async def get_notification_preferences(
    current_user: UserDependency,
    db: DBSessionDependency
):
    """获取用户的通知偏好设置"""
    # 这里可以从数据库中获取用户的通知偏好设置
    # 目前返回默认设置，后续可以扩展到数据库存储
    return NotificationPreferences()

# 更新用户通知偏好设置
@router.put("/notification-preferences")
async def update_notification_preferences(
    preferences: NotificationPreferences,
    current_user: UserDependency,
    db: DBSessionDependency
):
    """更新用户的通知偏好设置"""
    try:
        # 这里可以将偏好设置保存到数据库
        # 目前只是返回成功响应，后续可以扩展到数据库存储
        logger.info(f"用户 {current_user.id} 更新了通知偏好设置: {preferences}")
        
        return {"message": "通知偏好设置已更新", "preferences": preferences}
    except Exception as e:
        logger.error(f"更新通知偏好设置时出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新设置失败: {str(e)}")