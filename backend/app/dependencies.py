import logging
import os
from typing import Annotated, Optional

from dotenv import load_dotenv
from fastapi import Depends, HTTPException
from fastapi.security import OAuth2<PERSON><PERSON>word<PERSON>earer
from sqlmodel import Session, create_engine, select
from supabase import Client, create_client

from app.models import User

logger = logging.getLogger(__name__)

load_dotenv()

# Supabase connection
url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_KEY")

# Direct Postgres connection
engine_url = os.environ.get("SUPABASE_DB_STRING")
engine = create_engine(engine_url)


def get_supabase_client() -> Client:
    logger.info("Initializing Supabase client")
    return create_client(url, key)


SupabaseDependency = Annotated[Client, Depends(get_supabase_client)]


def get_db_session():
    with Session(engine) as session:
        yield session


DBSessionDependency = Annotated[Session, Depends(get_db_session)]

reusable_oauth2 = OAuth2PasswordBearer(tokenUrl="token")
AccessTokenDependency = Annotated[str, Depends(reusable_oauth2)]


async def get_current_user(
    access_token: AccessTokenDependency,
    db: DBSessionDependency,
    supabase_client: Client = Depends(get_supabase_client),
) -> User:
    """Get current user from access_token and validate at the same time"""
    try:
        response = supabase_client.auth.get_user(access_token)
    except Exception as e:
        logger.error("Error during getting user %s", e)
        raise HTTPException(status_code=401, detail="Invalid token")

    user = db.exec(select(User).where(User.id == response.user.id)).first()

    if not user:
        logger.warning(f"用户 ID={response.user.id}, Email={response.user.email} 在数据库中不存在，尝试自动创建")
        
        # 自动创建用户
        try:
            from datetime import datetime
            from uuid import UUID
            
            # 创建新用户
            new_user = User(
                id=UUID(response.user.id),
                # email=response.user.email,
                # username=response.user.email.split('@')[0],  # 使用邮箱前缀作为用户名
                is_active=True,
                # is_admin=False,
                created_at=datetime.now()
            )
            
            # 添加到数据库
            db.add(new_user)
            db.commit()
            db.refresh(new_user)
            
            logger.info(f"成功创建新用户: ID={new_user.id}, Email={new_user.email}")
            return new_user
            
        except Exception as create_error:
            logger.error(f"创建用户失败: {create_error}")
            raise HTTPException(status_code=500, detail="Failed to create user")
    
    return user


UserDependency = Annotated[User, Depends(get_current_user)]


async def get_current_user_optional(
    access_token: AccessTokenDependency = None,
    db: DBSessionDependency = None,
    supabase_client: Client = Depends(get_supabase_client),
) -> Optional[User]:
    if not access_token:
        return None
    try:
        response = supabase_client.auth.get_user(access_token)
    except Exception:
        return None
    user = db.exec(select(User).where(User.id == response.user.id)).first()
    return user

UserOptionalDependency = Annotated[Optional[User], Depends(get_current_user_optional)]
