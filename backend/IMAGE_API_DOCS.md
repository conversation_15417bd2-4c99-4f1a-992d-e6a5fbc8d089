# 微博图片管理 API 文档

## 概述

本文档描述了微博系统中图片管理的API接口，支持对微博图片的增删改查操作。

## 认证

所有API都需要Bearer Token认证：
```
Authorization: Bearer <your_token>
```

## API接口

### 1. 获取帖子图片列表

**GET** `/api/posts/{post_id}/images`

获取指定帖子的所有图片URL列表。

#### 参数
- `post_id` (path): 帖子ID (UUID)

#### 响应
```json
[
  "https://example.com/image1.jpg",
  "https://example.com/image2.jpg"
]
```

#### 状态码
- `200`: 成功
- `404`: 帖子不存在

---

### 2. 为帖子添加图片

**POST** `/api/posts/{post_id}/images`

为指定帖子添加新图片。

#### 参数
- `post_id` (path): 帖子ID (UUID)
- `images` (form-data): 图片文件列表，支持多文件上传

#### 限制
- 单张图片最大10MB
- 每个帖子最多9张图片
- 支持格式：PNG, JPG, JPEG, GIF

#### 响应
```json
{
  "image_urls": [
    "https://example.com/new_image1.jpg",
    "https://example.com/new_image2.jpg"
  ],
  "message": "Successfully added 2 images"
}
```

#### 状态码
- `201`: 创建成功
- `400`: 请求参数错误（文件无效、超出限制等）
- `403`: 无权限修改此帖子
- `404`: 帖子不存在
- `500`: 服务器内部错误

---

### 3. 删除帖子图片

**DELETE** `/api/posts/{post_id}/images`

删除帖子中的指定图片。

#### 参数
- `post_id` (path): 帖子ID (UUID)

#### 请求体
```json
{
  "image_url": "https://example.com/image_to_delete.jpg"
}
```

#### 响应
```json
{
  "message": "Image deleted successfully"
}
```

#### 状态码
- `200`: 删除成功
- `403`: 无权限修改此帖子
- `404`: 帖子或图片不存在
- `500`: 服务器内部错误

---

### 4. 替换帖子图片

**PUT** `/api/posts/{post_id}/images`

替换帖子中的指定图片。

#### 参数
- `post_id` (path): 帖子ID (UUID)
- `old_image_url` (form-data): 要替换的图片URL
- `new_image` (form-data): 新图片文件

#### 限制
- 单张图片最大10MB
- 支持格式：PNG, JPG, JPEG, GIF

#### 响应
```json
{
  "image_urls": [
    "https://example.com/new_replaced_image.jpg"
  ],
  "message": "Image replaced successfully"
}
```

#### 状态码
- `200`: 替换成功
- `400`: 请求参数错误
- `403`: 无权限修改此帖子
- `404`: 帖子或原图片不存在
- `500`: 服务器内部错误

## 使用示例

### JavaScript/TypeScript 示例

```typescript
// 1. 获取图片列表
const getImages = async (postId: string, token: string) => {
  const response = await fetch(`/api/posts/${postId}/images`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// 2. 添加图片
const addImages = async (postId: string, files: File[], token: string) => {
  const formData = new FormData();
  files.forEach(file => formData.append('images', file));
  
  const response = await fetch(`/api/posts/${postId}/images`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  return response.json();
};

// 3. 删除图片
const deleteImage = async (postId: string, imageUrl: string, token: string) => {
  const response = await fetch(`/api/posts/${postId}/images`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ image_url: imageUrl })
  });
  return response.json();
};

// 4. 替换图片
const replaceImage = async (postId: string, oldImageUrl: string, newFile: File, token: string) => {
  const formData = new FormData();
  formData.append('old_image_url', oldImageUrl);
  formData.append('new_image', newFile);
  
  const response = await fetch(`/api/posts/${postId}/images`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  return response.json();
};
```

## 错误处理

所有API在出错时都会返回标准的错误格式：

```json
{
  "detail": "错误描述信息"
}
```

常见错误：
- `Post not found`: 帖子不存在
- `Not authorized to modify this post`: 无权限修改此帖子
- `Image not found`: 图片不存在
- `Image is too large (max 10MB)`: 图片文件过大
- `File is not a valid image`: 文件不是有效的图片格式
- `Cannot add X images. Current: Y, Max: 9`: 超出图片数量限制

## 注意事项

1. **权限控制**: 只有帖子的所有者才能修改图片
2. **文件限制**: 严格限制文件大小和格式，防止恶意上传
3. **数量限制**: 每个帖子最多9张图片
4. **存储管理**: 删除图片时会检查是否被其他帖子引用，避免误删
5. **事务处理**: 所有操作都在数据库事务中进行，确保数据一致性
6. **错误回滚**: 上传失败时会自动清理已创建的记录

## 数据库设计

图片管理涉及以下数据表：
- `Post`: 帖子主表
- `PostAttached`: 帖子附件关联表
- `Diagram`: 图片/文件存储表

关系：`Post` ← `PostAttached` → `Diagram`

这种设计允许：
- 一个帖子有多张图片
- 一张图片可以被多个帖子引用（虽然当前业务逻辑不支持）
- 灵活的附件类型扩展（图片、文档等）