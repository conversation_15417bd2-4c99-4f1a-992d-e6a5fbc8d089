#!/usr/bin/env python3
"""
测试微博图片管理API的脚本
"""

import requests
import json
import os
from pathlib import Path

# API配置
BASE_URL = "http://localhost:8000/v1/api"
TOKEN = "your_auth_token_here"  # 需要替换为实际的认证token

def test_image_apis():
    """测试图片管理API"""
    
    headers = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    # 假设的帖子ID（需要替换为实际存在的帖子ID）
    post_id = "your_post_id_here"
    
    print("🧪 开始测试微博图片管理API")
    print("=" * 50)
    
    # 1. 测试获取帖子图片列表
    print("1. 测试获取帖子图片列表")
    try:
        response = requests.get(f"{BASE_URL}/posts/{post_id}/images", headers=headers)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            images = response.json()
            print(f"   图片数量: {len(images)}")
            for i, img_url in enumerate(images):
                print(f"   图片 {i+1}: {img_url}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print()
    
    # 2. 测试上传新图片（需要准备测试图片文件）
    print("2. 测试上传新图片")
    test_image_path = "test_image.jpg"  # 需要准备一个测试图片文件
    
    if os.path.exists(test_image_path):
        try:
            with open(test_image_path, 'rb') as f:
                files = {'images': ('test.jpg', f, 'image/jpeg')}
                upload_headers = {"Authorization": f"Bearer {TOKEN}"}
                
                response = requests.post(
                    f"{BASE_URL}/posts/{post_id}/images", 
                    headers=upload_headers,
                    files=files
                )
                
                print(f"   状态码: {response.status_code}")
                if response.status_code == 201:
                    result = response.json()
                    print(f"   上传成功: {result['message']}")
                    print(f"   新图片URL: {result['image_urls']}")
                else:
                    print(f"   错误: {response.text}")
        except Exception as e:
            print(f"   异常: {e}")
    else:
        print(f"   跳过: 测试图片文件 {test_image_path} 不存在")
    
    print()
    
    # 3. 测试删除图片
    print("3. 测试删除图片")
    image_url_to_delete = "your_image_url_here"  # 需要替换为实际的图片URL
    
    try:
        delete_data = {"image_url": image_url_to_delete}
        response = requests.delete(
            f"{BASE_URL}/posts/{post_id}/images",
            headers=headers,
            data=json.dumps(delete_data)
        )
        
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   删除成功: {result['message']}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print()
    
    # 4. 测试替换图片
    print("4. 测试替换图片")
    old_image_url = "your_old_image_url_here"  # 需要替换为实际的图片URL
    
    if os.path.exists(test_image_path):
        try:
            with open(test_image_path, 'rb') as f:
                files = {'new_image': ('new_test.jpg', f, 'image/jpeg')}
                data = {'old_image_url': old_image_url}
                replace_headers = {"Authorization": f"Bearer {TOKEN}"}
                
                response = requests.put(
                    f"{BASE_URL}/posts/{post_id}/images",
                    headers=replace_headers,
                    files=files,
                    data=data
                )
                
                print(f"   状态码: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    print(f"   替换成功: {result['message']}")
                    print(f"   新图片URL: {result['image_urls']}")
                else:
                    print(f"   错误: {response.text}")
        except Exception as e:
            print(f"   异常: {e}")
    else:
        print(f"   跳过: 测试图片文件 {test_image_path} 不存在")
    
    print()
    print("🎉 API测试完成")

if __name__ == "__main__":
    print("⚠️  请先修改脚本中的以下配置:")
    print("   - TOKEN: 你的认证token")
    print("   - post_id: 实际存在的帖子ID")
    print("   - image_url_to_delete: 要删除的图片URL")
    print("   - old_image_url: 要替换的图片URL")
    print("   - 准备测试图片文件: test_image.jpg")
    print()
    
    confirm = input("配置完成后，按 Enter 继续测试，或按 Ctrl+C 退出: ")
    test_image_apis()