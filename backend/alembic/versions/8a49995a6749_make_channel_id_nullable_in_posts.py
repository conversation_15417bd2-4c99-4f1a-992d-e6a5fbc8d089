"""make_channel_id_nullable_in_posts

Revision ID: 8a49995a6749
Revises: eca6c1802055
Create Date: 2025-08-09 09:15:34.620096

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

import sqlmodel



# revision identifiers, used by Alembic.
revision: str = '8a49995a6749'
down_revision: Union[str, None] = 'eca6c1802055'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 修改posts表的channel_id字段为可空
    op.alter_column('posts', 'channel_id', nullable=True)


def downgrade() -> None:
    # 回滚：将channel_id字段改回非空（注意：这可能会失败如果有NULL值）
    op.alter_column('posts', 'channel_id', nullable=False)
