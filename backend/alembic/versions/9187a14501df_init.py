"""init

Revision ID: 9187a14501df
Revises: 
Create Date: 2025-07-23 10:44:35.117249

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

import sqlmodel

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '9187a14501df'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('channels',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('display_name', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('icon', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('color', sqlmodel.sql.sqltypes.AutoString(length=7), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('post_count', sa.Integer(), nullable=False),
    sa.Column('member_count', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('created_by', sa.Uuid(), nullable=False),
    sa.Column('moderator_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
    sa.ForeignKeyConstraint(['moderator_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_channels_id'), 'channels', ['id'], unique=False)
    op.create_index(op.f('ix_channels_is_active'), 'channels', ['is_active'], unique=False)
    op.create_index(op.f('ix_channels_moderator_id'), 'channels', ['moderator_id'], unique=False)
    op.create_index(op.f('ix_channels_name'), 'channels', ['name'], unique=True)
    op.create_table('team',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=128), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=False),
    sa.Column('update_time', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('channel_members',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('channel_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('role', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('joined_at', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['channel_id'], ['channels.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('team_member',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('team_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('create_time', sa.DateTime(), nullable=False),
    sa.Column('update_time', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['team_id'], ['team.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_channel_subscriptions',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('channel_id', sa.Uuid(), nullable=False),
    sa.Column('joined_at', sa.DateTime(), nullable=False),
    sa.Column('notification_enabled', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['channel_id'], ['channels.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_channel_subscriptions_channel_id'), 'user_channel_subscriptions', ['channel_id'], unique=False)
    op.create_index(op.f('ix_user_channel_subscriptions_id'), 'user_channel_subscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_user_channel_subscriptions_user_id'), 'user_channel_subscriptions', ['user_id'], unique=False)
    op.create_table('team_member_permission',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('member_id', sa.Uuid(), nullable=False),
    sa.Column('auth_target_type', sqlmodel.sql.sqltypes.AutoString(length=128), nullable=False),
    sa.Column('target', sa.Uuid(), nullable=False),
    sa.Column('operate', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('create_time', sa.DateTime(), nullable=False),
    sa.Column('update_time', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['member_id'], ['team_member.id'], ),
    sa.PrimaryKeyConstraint('id')
    )

    notificationtype = sa.Enum('MENTION', 'LIKE', 'COMMENT', 'FOLLOW', 'CHANNEL_POST', name='notificationtype')
    notificationtype.create(op.get_bind())

    op.add_column('notifications', sa.Column('channel_id', sa.Uuid(), nullable=True))
    # 1. 先移除默认值
    op.alter_column('notifications', 'type', server_default=None)

    # 2. 再修改字段类型
    op.alter_column('notifications', 'type',
        existing_type=sa.VARCHAR(length=50),
        type_=notificationtype,
        existing_nullable=False,
        postgresql_using="type::notificationtype"
    )

    # 3. 最后加回默认值
    op.alter_column('notifications', 'type', server_default=sa.text("'MENTION'::notificationtype"))
    op.alter_column('notifications', 'content',
               existing_type=sa.TEXT(),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=True)
    op.drop_index('ix_notifications_user_id', table_name='notifications')
    op.create_foreign_key(None, 'notifications', 'channels', ['channel_id'], ['id'])
    op.add_column('post_likes', sa.Column('created_at', sa.DateTime(), nullable=False))
    op.add_column('posts', sa.Column('channel_id', sa.Uuid(), nullable=True))
    op.create_foreign_key(None, 'posts', 'channels', ['channel_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'posts', type_='foreignkey')
    op.drop_column('posts', 'channel_id')
    op.drop_column('post_likes', 'created_at')
    op.drop_constraint(None, 'notifications', type_='foreignkey')
    op.create_index('ix_notifications_user_id', 'notifications', ['user_id'], unique=False)
    op.alter_column('notifications', 'content',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('notifications', 'type',
               existing_type=sa.Enum('MENTION', 'LIKE', 'COMMENT', 'FOLLOW', 'CHANNEL_POST', name='notificationtype'),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False,
               existing_server_default=sa.text("'mention'::character varying"))
    op.drop_column('notifications', 'channel_id')
    op.drop_table('team_member_permission')
    op.drop_index(op.f('ix_user_channel_subscriptions_user_id'), table_name='user_channel_subscriptions')
    op.drop_index(op.f('ix_user_channel_subscriptions_id'), table_name='user_channel_subscriptions')
    op.drop_index(op.f('ix_user_channel_subscriptions_channel_id'), table_name='user_channel_subscriptions')
    op.drop_table('user_channel_subscriptions')
    op.drop_table('team_member')
    op.drop_table('channel_members')
    op.drop_table('team')
    op.drop_index(op.f('ix_channels_name'), table_name='channels')
    op.drop_index(op.f('ix_channels_moderator_id'), table_name='channels')
    op.drop_index(op.f('ix_channels_is_active'), table_name='channels')
    op.drop_index(op.f('ix_channels_id'), table_name='channels')
    op.drop_table('channels')
    # ### end Alembic commands ###
