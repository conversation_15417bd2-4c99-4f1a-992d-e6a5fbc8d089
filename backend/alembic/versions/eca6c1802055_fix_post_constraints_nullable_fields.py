"""fix_post_constraints_nullable_fields

Revision ID: eca6c1802055
Revises: 9187a14501df
Create Date: 2025-08-09 17:09:05.884224

"""
from typing import Sequence, Union

import sqlalchemy as sa

from alembic import op

import sqlmodel



# revision identifiers, used by Alembic.
revision: str = 'eca6c1802055'
down_revision: Union[str, None] = '9187a14501df'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 确保posts表的owner_id和type字段不能为空
    # 首先更新任何可能为空的记录（虽然理论上不应该有）
    op.execute("UPDATE posts SET type = 'NORMAL' WHERE type IS NULL")

    # 修改字段约束
    op.alter_column('posts', 'owner_id', nullable=False)
    op.alter_column('posts', 'type', nullable=False)
    op.alter_column('posts', 'content', nullable=False)


def downgrade() -> None:
    # 回滚约束修改
    op.alter_column('posts', 'owner_id', nullable=True)
    op.alter_column('posts', 'type', nullable=True)
    op.alter_column('posts', 'content', nullable=True)
