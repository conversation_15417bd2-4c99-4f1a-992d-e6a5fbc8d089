#!/bin/bash
set -e

# 显示彩色输出的函数
function echo_info() {
    echo -e "\033[1;34m[INFO]\033[0m $1"
}

function echo_success() {
    echo -e "\033[1;32m[SUCCESS]\033[0m $1"
}

function echo_warning() {
    echo -e "\033[1;33m[WARNING]\033[0m $1"
}

function echo_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

# 检查环境变量
if [ -z "$DATABASE_URL" ]; then
    echo_warning "未设置 DATABASE_URL 环境变量，将使用 .env 文件中的配置"
    if [ -f .env ]; then
        export $(grep -v '^#' .env | xargs)
        echo_info "已加载 .env 文件中的环境变量"
    else
        echo_error "未找到 .env 文件，请确保数据库连接信息已正确配置"
        exit 1
    fi
fi

# 显示升级信息
echo_info "开始数据库升级流程..."
echo_info "此升级将执行以下操作:"
echo "  1. 创建学术类别板块（数字孪生、Agentic AI、上下文工程、时序智能、几何智能等）"
echo "  2. 根据内容将现有帖子分配到适当的学术板块"
echo ""

# 确认是否继续
read -p "是否继续升级? (y/n): " confirm
if [[ $confirm != "y" && $confirm != "Y" ]]; then
    echo_info "已取消升级"
    exit 0
fi

# 创建数据库备份
echo_info "正在创建数据库备份..."
timestamp=$(date +"%Y%m%d_%H%M%S")
backup_file="db_backup_$timestamp.sql"

# 根据数据库类型执行不同的备份命令
if [[ $DATABASE_URL == postgres://* ]]; then
    # 提取PostgreSQL连接信息
    db_user=$(echo $DATABASE_URL | sed -n 's/.*:\/\/\([^:]*\):.*/\1/p')
    db_pass=$(echo $DATABASE_URL | sed -n 's/.*:\/\/[^:]*:\([^@]*\)@.*/\1/p')
    db_host=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
    db_port=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    db_name=$(echo $DATABASE_URL | sed -n 's/.*\/\([^?]*\).*/\1/p')
    
    # 设置PGPASSWORD环境变量，避免密码提示
    export PGPASSWORD="$db_pass"
    
    # 执行PostgreSQL备份
    pg_dump -h $db_host -p $db_port -U $db_user -d $db_name -f $backup_file
    
    echo_success "数据库备份已创建: $backup_file"
else
    echo_warning "不支持的数据库类型或无法自动备份，请手动备份数据库"
    read -p "是否已手动备份数据库? (y/n): " backup_confirm
    if [[ $backup_confirm != "y" && $backup_confirm != "Y" ]]; then
        echo_info "已取消升级，请先备份数据库"
        exit 0
    fi
fi

# 执行数据库迁移
echo_info "正在执行数据库迁移..."
uv run alembic upgrade heads

echo_success "数据库升级完成!"
echo_info "现在可以启动应用程序了"
echo_info "如需回滚，请使用命令: uv run alembic downgrade migrate_posts_to_academic_channels-1"