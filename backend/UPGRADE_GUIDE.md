# 学术板块升级指南

本文档详细说明如何将现有的帖子（posts）归类到新的学术板块中，包括数字孪生、Agentic AI、上下文工程、时序智能、几何智能等方向。

## 目录

- [背景](#背景)
- [升级内容](#升级内容)
- [升级前准备](#升级前准备)
- [升级方案](#升级方案)
  - [方案一：容器部署后手动执行升级（推荐）](#方案一容器部署后手动执行升级推荐)
  - [方案二：容器启动时自动执行升级](#方案二容器启动时自动执行升级)
- [升级后验证](#升级后验证)
- [回滚方案](#回滚方案)
- [常见问题](#常见问题)

## 背景

系统已经运行了一段时间，积累了大量帖子（posts）。目前这些帖子没有归属到特定的板块（panel）中。随着功能的完善，需要将这些帖子按照学术方向分类到不同的板块中，以便用户更好地浏览和管理内容。

## 升级内容

本次升级将执行以下操作：

1. 创建新的学术板块：
   - 数字孪生 (digital-twin)
   - Agentic AI (agentic-ai)
   - 上下文工程 (context-engineering)
   - 时序智能 (temporal-intelligence)
   - 几何智能 (geometric-intelligence)
   - 学术综合 (academic-general)

2. 根据帖子内容，将现有帖子分配到适当的学术板块：
   - 使用关键词匹配算法，根据帖子内容自动分类
   - 无法明确分类的帖子将归入"学术综合"板块

3. 更新各板块的帖子数量统计

## 升级前准备

1. **备份数据库**：确保在执行任何升级前有完整的数据库备份
2. **检查系统状态**：确保系统当前运行正常，没有未解决的错误
3. **选择合适的升级时间**：建议在非高峰时段执行升级，减少对用户的影响
4. **准备回滚计划**：了解如何在必要时回滚升级

## 升级方案

### 方案一：容器部署后手动执行升级（推荐）

这种方法允许您完全控制升级过程，并在必要时进行手动干预。

#### 步骤 1: 部署容器

```bash
./docker-start-prod.sh --build
```

这将构建并启动您的生产环境容器。

#### 步骤 2: 进入后端容器

```bash
# 查找后端容器ID
docker ps | grep backend

# 进入容器
docker exec -it <容器ID或名称> /bin/bash
```

#### 步骤 3: 在容器内执行升级脚本

```bash
cd /app  # 假设您的应用代码在容器内的/app目录
chmod +x backend/upgrade.sh
./backend/upgrade.sh
```

升级脚本会执行以下操作：
- 创建数据库备份
- 创建新的学术板块
- 根据内容将现有帖子分配到适当的学术板块
- 更新各板块的帖子数量统计

#### 步骤 4: 检查升级结果

```bash
# 查看数据库迁移状态
uv run alembic history

# 查看各板块的帖子数量
psql -U <用户名> -d <数据库名> -c "SELECT name, display_name, post_count FROM channels ORDER BY post_count DESC;"
```

### 方案二：容器启动时自动执行升级

如果您希望自动化整个过程，可以修改后端容器的启动命令，使其在启动时执行数据库升级。

#### 步骤 1: 修改Docker配置

编辑 `docker/docker-compose.yml` 文件，修改后端服务的配置：

```yaml
backend:
  # 其他配置...
  command: >
    bash -c "
      cd /app/backend &&
      chmod +x upgrade.sh &&
      ./upgrade.sh &&
      exec uv run uvicorn app.main:app --host 0.0.0.0 --port 8000
    "
```

#### 步骤 2: 重新构建并启动容器

```bash
./docker-start-prod.sh --build
```

#### 步骤 3: 查看容器日志，确认升级是否成功

```bash
docker logs <后端容器ID或名称>
```

## 升级后验证

升级完成后，请执行以下验证步骤：

1. **检查数据库迁移状态**：
   ```bash
   uv run alembic history
   ```
   确认最新的迁移已应用

2. **检查板块数据**：
   ```bash
   psql -U <用户名> -d <数据库名> -c "SELECT name, display_name, post_count FROM channels ORDER BY post_count DESC;"
   ```
   确认新的学术板块已创建，并且帖子数量合理

3. **检查帖子分配**：
   ```bash
   psql -U <用户名> -d <数据库名> -c "SELECT channel_id, COUNT(*) FROM posts GROUP BY channel_id;"
   ```
   确认所有帖子都已分配到板块

4. **验证应用功能**：
   - 登录应用，确认可以看到新的学术板块
   - 检查每个板块中的帖子是否正确显示
   - 尝试在不同板块中发布新帖子

## 回滚方案

如果升级过程中出现问题，可以使用以下步骤回滚：

1. **回滚帖子分类**：
   ```bash
   uv run alembic downgrade migrate_posts_to_academic_channels-1
   ```
   这将撤销帖子分类，但保留新创建的学术板块

2. **完全回滚**（如果需要）：
   ```bash
   uv run alembic downgrade create_academic_channels-1
   ```
   这将删除新创建的学术板块

3. **从备份恢复**（最后手段）：
   如果上述回滚方法不起作用，可以使用升级前创建的数据库备份进行恢复

## 常见问题

### Q: 升级过程中出现"Default channel 'general' not found"错误怎么办？

A: 这表示系统中没有找到默认的"general"板块。请确保已经运行了`seed_channels_data`迁移。如果问题仍然存在，可以手动创建一个默认板块：

```sql
INSERT INTO channels (id, name, display_name, description, is_default, is_active, post_count, member_count, created_at, created_by)
VALUES (
  uuid_generate_v4(),
  'general',
  '综合讨论',
  '学术交流的综合讨论区，适合各种学术话题的交流',
  true,
  true,
  0,
  0,
  CURRENT_TIMESTAMP,
  (SELECT id FROM "user" LIMIT 1)
);
```

### Q: 如何查看特定板块中的帖子？

A: 可以使用以下SQL查询：

```sql
SELECT p.id, p.content, u.display_name
FROM posts p
JOIN "user" u ON p.owner_id = u.id
WHERE p.channel_id = (SELECT id FROM channels WHERE name = 'digital-twin')
ORDER BY p.timestamp DESC
LIMIT 10;
```

### Q: 如何手动将帖子移动到其他板块？

A: 可以使用以下SQL命令：

```sql
-- 将帖子ID为<post_id>的帖子移动到"数字孪生"板块
UPDATE posts
SET channel_id = (SELECT id FROM channels WHERE name = 'digital-twin')
WHERE id = '<post_id>';

-- 更新板块的帖子数量统计
UPDATE channels
SET post_count = (SELECT COUNT(*) FROM posts WHERE channel_id = channels.id)
WHERE name IN ('digital-twin', 'general');
```

### Q: 升级后为什么有些帖子没有正确分类？

A: 自动分类基于帖子内容中的关键词匹配。如果帖子内容没有包含预定义的关键词，它会被归类到"学术综合"板块。您可以：

1. 修改`migrate_posts_to_academic_channels.py`中的关键词列表，添加更多相关关键词
2. 手动将帖子重新分类到适当的板块