#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
  echo -e "${2}${1}${NC}"
}

# 检查命令是否存在
check_command() {
  if ! command -v $1 &> /dev/null; then
    print_message "错误: $1 命令未找到，请先安装" "$RED"
    exit 1
  fi
}

# 检查必要的命令
check_command "docker"
check_command "docker-compose"

# 加载环境变量
load_env() {
  if [ -f ".env" ]; then
    print_message "加载环境变量文件 .env" "$YELLOW"
    export $(cat .env | grep -v '^#' | xargs)
  else
    print_message "未找到 .env 文件，使用默认配置" "$YELLOW"
    print_message "如需自定义配置，请复制 .env.example 为 .env 并修改" "$BLUE"
  fi
}

# 检查局域网配置
check_network_config() {
  if [ -n "$SUPABASE_HOST" ] && [ "$SUPABASE_HOST" != "host.docker.internal" ]; then
    print_message "检测到局域网配置: SUPABASE_HOST=$SUPABASE_HOST" "$BLUE"
    print_message "确保以下条件满足:" "$YELLOW"
    print_message "  1. Supabase 在 $SUPABASE_HOST:54321 上运行" "$BLUE"
    print_message "  2. 防火墙允许访问 54321 端口" "$BLUE"
    print_message "  3. 网络连接正常" "$BLUE"
  fi
}

# 显示帮助信息
show_help() {
  print_message "3Stooges Portal Docker 部署工具" "$BLUE"
  echo ""
  print_message "用法:" "$YELLOW"
  echo "  ./docker-start-prod.sh [选项]"
  echo ""
  print_message "选项:" "$YELLOW"
  echo "  --help, -h       显示帮助信息"
  echo "  --build, -b      构建并启动生产环境服务"
  echo "  --start, -s      启动生产环境服务（不重新构建）"
  echo "  --stop, -d       停止服务"
  echo "  --restart, -r    重启服务"
  echo "  --logs, -l       查看日志"
  echo "  --status, -t     查看服务状态"
  echo ""
  print_message "开发环境选项:" "$YELLOW"
  echo "  --dev-build, -db 构建并启动开发环境服务（包含 Deep Search）"
  echo "  --dev-build-selective, -dbs 构建并启动开发环境服务（Deep Search 不重新构建）"
  echo "  --dev-start, -ds 启动开发环境服务（包含 Deep Search）"
  echo "  --dev-stop, -dd  停止开发环境服务"
  echo "  --dev-logs, -dl  查看开发环境日志"
  echo "  --dev-status, -dt 查看开发环境服务状态"
  echo ""
  print_message "Deep Search 服务选项:" "$YELLOW"
  echo "  --deep-search-build, -dsb 构建并启动 Deep Search 服务"
  echo "  --deep-search-start, -dss 启动 Deep Search 服务（不重新构建）"
  echo "  --deep-search-stop, -dsd  停止 Deep Search 服务"
  echo "  --deep-search-logs, -dsl  查看 Deep Search 服务日志"
  echo "  --deep-search-status, -dst 查看 Deep Search 服务状态"
  echo ""
  print_message "局域网访问配置:" "$YELLOW"
  echo "  1. 复制配置文件: cp .env.example .env"
  echo "  2. 编辑 .env 文件，设置 SUPABASE_HOST 为 Supabase 服务器的 IP 地址"
  echo "  3. 重新构建并启动: ./docker-start-prod.sh --build"
  echo ""
  print_message "注意:" "$RED"
  echo "  生产环境使用 docker/docker-compose.yml 配置文件"
  echo "  开发环境使用根目录的 docker-compose.yml 配置文件"
  echo ""
}

# 检查 Supabase 是否运行
check_supabase() {
  print_message "检查 Supabase 服务状态..." "$YELLOW"
  
  # 检查 supabase CLI 是否安装
  if ! command -v supabase &> /dev/null; then
    print_message "错误: supabase CLI 未安装，请先安装" "$RED"
    print_message "安装方法: brew install supabase/tap/supabase" "$BLUE"
    exit 1
  fi

  # 获取 Supabase 状态
  SUPABASE_STATUS=$(supabase status 2>&1)
  if [ $? -ne 0 ]; then
    print_message "⚠️ Supabase 服务未运行" "$RED"
    print_message "请先启动 Supabase 服务:" "$YELLOW"
    print_message "  supabase start" "$BLUE"
    
    read -p "是否继续部署？(y/n): " continue_deploy
    if [[ "$continue_deploy" != "y" && "$continue_deploy" != "Y" ]]; then
      print_message "部署已取消" "$RED"
      exit 1
    fi
    
    print_message "⚠️ 警告: 在没有 Supabase 的情况下继续部署，应用可能无法正常工作" "$YELLOW"
    return 1
  fi

  # 从状态输出中提取 API URL
  API_URL=$(echo "$SUPABASE_STATUS" | grep "API URL:" | awk '{print $3}')
  if [ -z "$API_URL" ]; then
    print_message "⚠️ 无法获取 Supabase API URL" "$RED"
    return 1
  fi

  # 验证 API URL 是否可访问
  if curl -s -f "$API_URL/rest/v1/" > /dev/null; then
    print_message "✅ Supabase 服务已运行" "$GREEN"
    print_message "API URL: $API_URL" "$BLUE"
    return 0
  else
    print_message "⚠️ Supabase API 无法访问: $API_URL" "$RED"
    print_message "请检查 Supabase 服务状态" "$YELLOW"
    return 1
  fi
}

# 构建并启动服务
build_and_start() {
  print_message "构建并启动生产环境 Docker 服务..." "$YELLOW"
  COMPOSE_FILE=docker/docker-compose.yml docker-compose up -d --build
  
  if [ $? -eq 0 ]; then
    print_message "✅ 生产环境服务已成功启动" "$GREEN"
    print_message "应用可通过以下地址访问:" "$BLUE"
    print_message "  前端: http://localhost:8080" "$BLUE"
    print_message "  后端 API: http://localhost:8080/api/" "$BLUE"
    print_message "  PDF 服务: http://localhost:8080/pdf-service/" "$BLUE"
    print_message "  存储代理: http://localhost:8080/storage-proxy/" "$BLUE"
    echo ""
    print_message "局域网访问:" "$BLUE"
    print_message "  将 localhost 替换为本机 IP 地址即可在局域网内访问" "$BLUE"
  else
    print_message "❌ 服务启动失败，请检查错误信息" "$RED"
  fi
}

# 启动服务（不重新构建）
start_services() {
  print_message "启动生产环境 Docker 服务..." "$YELLOW"
  COMPOSE_FILE=docker/docker-compose.yml docker-compose up -d
  
  if [ $? -eq 0 ]; then
    print_message "✅ 生产环境服务已成功启动" "$GREEN"
    print_message "应用可通过以下地址访问:" "$BLUE"
    print_message "  前端: http://localhost:8080" "$BLUE"
    print_message "  后端 API: http://localhost:8080/api/" "$BLUE"
    print_message "  PDF 服务: http://localhost:8080/pdf-service/" "$BLUE"
    print_message "  存储代理: http://localhost:8080/storage-proxy/" "$BLUE"
  else
    print_message "❌ 服务启动失败，请检查错误信息" "$RED"
  fi
}

# 停止服务
stop_services() {
  print_message "停止生产环境 Docker 服务..." "$YELLOW"
  COMPOSE_FILE=docker/docker-compose.yml docker-compose down

  if [ $? -eq 0 ]; then
    print_message "✅ 服务已成功停止" "$GREEN"
  else
    print_message "❌ 服务停止失败，请检查错误信息" "$RED"
  fi
}

# 重启服务
restart_services() {
  print_message "重启生产环境 Docker 服务..." "$YELLOW"
  COMPOSE_FILE=docker/docker-compose.yml docker-compose restart
  
  if [ $? -eq 0 ]; then
    print_message "✅ 服务已成功重启" "$GREEN"
  else
    print_message "❌ 服务重启失败，请检查错误信息" "$RED"
  fi
}

# 查看日志
view_logs() {
  print_message "查看生产环境服务日志..." "$YELLOW"
  COMPOSE_FILE=docker/docker-compose.yml docker-compose logs -f
}

# 查看服务状态
check_status() {
  print_message "生产环境服务状态:" "$YELLOW"
  COMPOSE_FILE=docker/docker-compose.yml docker-compose ps
}

# 开发环境相关函数
build_and_start_dev() {
  print_message "构建并启动开发环境 Docker 服务（包含 Deep Search）..." "$YELLOW"
  
  # 检查并创建 deep_search 环境配置文件
  if [ ! -f "deep_search/.env.docker" ]; then
    print_message "创建 Deep Search 环境配置文件..." "$YELLOW"
    cat > deep_search/.env.docker << EOF
# API配置 - 从backend服务获取模型配置
BACKEND_API_BASE=http://backend:8000/v1/api
# 模型配置刷新间隔（秒）
MODEL_CONFIG_REFRESH_INTERVAL=300
# 默认模型配置（作为fallback）
OPENAI_API_KEY=sk-YyiBg6DSn1Fc2KBNU6ZYtw
OPENAI_API_BASE=http://*************:4033/
EOF
  fi
  
  # 构建并启动所有开发环境服务，包括 deep_search
  docker-compose up -d --build
  
  if [ $? -eq 0 ]; then
    print_message "✅ 开发环境服务（包含 Deep Search）已成功启动" "$GREEN"
    print_message "应用可通过以下地址访问:" "$BLUE"
    print_message "  前端: http://localhost:5173" "$BLUE"
    print_message "  后端 API: http://localhost:8000" "$BLUE"
    print_message "  PDF 服务: http://localhost:8002" "$BLUE"
    print_message "  Deep Search API: http://localhost:2024" "$BLUE"
    print_message "  LangGraph Studio: https://smith.langchain.com/studio/?baseUrl=http://localhost:2024" "$BLUE"
  else
    print_message "❌ 服务启动失败，请检查错误信息" "$RED"
  fi
}

start_dev_services() {
  print_message "启动开发环境 Docker 服务（包含 Deep Search）..." "$YELLOW"
  
  # 启动所有开发环境服务，包括 deep_search
  docker-compose up -d
  
  if [ $? -eq 0 ]; then
    print_message "✅ 开发环境服务（包含 Deep Search）已成功启动" "$GREEN"
    print_message "应用可通过以下地址访问:" "$BLUE"
    print_message "  前端: http://localhost:5173" "$BLUE"
    print_message "  后端 API: http://localhost:8000" "$BLUE"
    print_message "  PDF 服务: http://localhost:8002" "$BLUE"
    print_message "  Deep Search API: http://localhost:2024" "$BLUE"
    print_message "  LangGraph Studio: https://smith.langchain.com/studio/?baseUrl=http://localhost:2024" "$BLUE"
  else
    print_message "❌ 服务启动失败，请检查错误信息" "$RED"
  fi
}

stop_dev_services() {
  print_message "停止开发环境 Docker 服务..." "$YELLOW"
  docker-compose down

  if [ $? -eq 0 ]; then
    print_message "✅ 开发环境服务已成功停止" "$GREEN"
  else
    print_message "❌ 服务停止失败，请检查错误信息" "$RED"
  fi
}

view_dev_logs() {
  print_message "查看开发环境服务日志..." "$YELLOW"
  docker-compose logs -f
}

check_dev_status() {
  print_message "开发环境服务状态:" "$YELLOW"
  docker-compose ps
}

# Deep Search 服务相关函数
build_and_start_deep_search() {
  print_message "构建并启动 Deep Search 服务..." "$YELLOW"
  
  # 检查 deep_search 目录是否存在
  if [ ! -d "deep_search" ]; then
    print_message "错误: deep_search 目录不存在" "$RED"
    exit 1
  fi
  
  # 检查 .env.docker 文件是否存在，如果不存在则创建
  if [ ! -f "deep_search/.env.docker" ]; then
    print_message "创建 Deep Search 环境配置文件..." "$YELLOW"
    cat > deep_search/.env.docker << EOF
# API配置 - 从backend服务获取模型配置
BACKEND_API_BASE=http://backend:8000/v1/api
# 模型配置刷新间隔（秒）
MODEL_CONFIG_REFRESH_INTERVAL=300
# 默认模型配置（作为fallback）
OPENAI_API_KEY=sk-YyiBg6DSn1Fc2KBNU6ZYtw
OPENAI_API_BASE=http://*************:4033/
EOF
  fi
  
  # 启动 backend 服务（deep_search 依赖它）
  print_message "启动依赖的 backend 服务..." "$YELLOW"
  docker-compose up -d backend
  
  # 等待 backend 服务启动
  print_message "等待 backend 服务启动..." "$YELLOW"
  sleep 10
  
  # 启动 deep_search 服务
  docker-compose up -d --build deep_search
  
  if [ $? -eq 0 ]; then
    print_message "✅ Deep Search 服务已成功启动" "$GREEN"
    print_message "服务可通过以下地址访问:" "$BLUE"
    print_message "  Deep Search API: http://localhost:2024" "$BLUE"
    print_message "  LangGraph Studio: https://smith.langchain.com/studio/?baseUrl=http://localhost:2024" "$BLUE"
    print_message "  API 文档: http://localhost:2024/docs" "$BLUE"
    print_message "  Backend API: http://localhost:8000" "$BLUE"
  else
    print_message "❌ Deep Search 服务启动失败，请检查错误信息" "$RED"
  fi
}

start_deep_search_services() {
  print_message "启动 Deep Search 服务..." "$YELLOW"
  
  # 启动 backend 服务（deep_search 依赖它）
  docker-compose up -d backend
  
  # 等待 backend 服务启动
  print_message "等待 backend 服务启动..." "$YELLOW"
  sleep 5
  
  # 启动 deep_search 服务
  docker-compose up -d deep_search
  
  if [ $? -eq 0 ]; then
    print_message "✅ Deep Search 服务已成功启动" "$GREEN"
    print_message "服务可通过以下地址访问:" "$BLUE"
    print_message "  Deep Search API: http://localhost:2024" "$BLUE"
    print_message "  LangGraph Studio: https://smith.langchain.com/studio/?baseUrl=http://localhost:2024" "$BLUE"
    print_message "  API 文档: http://localhost:2024/docs" "$BLUE"
    print_message "  Backend API: http://localhost:8000" "$BLUE"
  else
    print_message "❌ Deep Search 服务启动失败，请检查错误信息" "$RED"
  fi
}

stop_deep_search_services() {
  print_message "停止 Deep Search 服务..." "$YELLOW"
  docker-compose stop deep_search

  if [ $? -eq 0 ]; then
    print_message "✅ Deep Search 服务已成功停止" "$GREEN"
  else
    print_message "❌ Deep Search 服务停止失败，请检查错误信息" "$RED"
  fi
}

view_deep_search_logs() {
  print_message "查看 Deep Search 服务日志..." "$YELLOW"
  docker-compose logs -f deep_search
}

check_deep_search_status() {
  print_message "Deep Search 服务状态:" "$YELLOW"
  docker-compose ps deep_search backend
}

# 构建并启动开发环境服务（Deep Search 不重新构建）
build_and_start_dev_selective() {
  print_message "构建并启动开发环境 Docker 服务（Deep Search 不重新构建）..." "$YELLOW"
  
  # 检查并创建 deep_search 环境配置文件
  if [ ! -f "deep_search/.env.docker" ]; then
    print_message "创建 Deep Search 环境配置文件..." "$YELLOW"
    cat > deep_search/.env.docker << EOF
# API配置 - 从backend服务获取模型配置
BACKEND_API_BASE=http://backend:8000/v1/api
# 模型配置刷新间隔（秒）
MODEL_CONFIG_REFRESH_INTERVAL=300
# 默认模型配置（作为fallback）
OPENAI_API_KEY=sk-YyiBg6DSn1Fc2KBNU6ZYtw
OPENAI_API_BASE=http://*************:4033/
EOF
  fi
  
  # 先构建并启动除 deep_search 外的其他服务
  print_message "构建并启动基础服务..." "$BLUE"
  docker-compose up -d --build backend frontend pdf_service
  
  # 等待 backend 服务启动
  print_message "等待 backend 服务启动..." "$BLUE"
  sleep 5
  
  # 启动 deep_search 服务（不重新构建）
  print_message "启动 Deep Search 服务（不重新构建）..." "$BLUE"
  docker-compose up -d deep_search
  
  if [ $? -eq 0 ]; then
    print_message "✅ 开发环境服务已成功启动（Deep Search 未重新构建）" "$GREEN"
    print_message "应用可通过以下地址访问:" "$BLUE"
    print_message "  前端: http://localhost:5173" "$BLUE"
    print_message "  后端 API: http://localhost:8000" "$BLUE"
    print_message "  PDF 服务: http://localhost:8002" "$BLUE"
    print_message "  Deep Search API: http://localhost:2024" "$BLUE"
    print_message "  LangGraph Studio: https://smith.langchain.com/studio/?baseUrl=http://localhost:2024" "$BLUE"
  else
    print_message "❌ 服务启动失败，请检查错误信息" "$RED"
  fi
}

stop_dev_services() {
  print_message "停止开发环境 Docker 服务..." "$YELLOW"
  docker-compose down

  if [ $? -eq 0 ]; then
    print_message "✅ 开发环境服务已成功停止" "$GREEN"
  else
    print_message "❌ 服务停止失败，请检查错误信息" "$RED"
  fi
}

view_dev_logs() {
  print_message "查看开发环境服务日志..." "$YELLOW"
  docker-compose logs -f
}

check_dev_status() {
  print_message "开发环境服务状态:" "$YELLOW"
  docker-compose ps
}

# Deep Search 服务相关函数
build_and_start_deep_search() {
  print_message "构建并启动 Deep Search 服务..." "$YELLOW"
  
  # 检查 deep_search 目录是否存在
  if [ ! -d "deep_search" ]; then
    print_message "错误: deep_search 目录不存在" "$RED"
    exit 1
  fi
  
  # 检查 .env.docker 文件是否存在，如果不存在则创建
  if [ ! -f "deep_search/.env.docker" ]; then
    print_message "创建 Deep Search 环境配置文件..." "$YELLOW"
    cat > deep_search/.env.docker << EOF
# API配置 - 从backend服务获取模型配置
BACKEND_API_BASE=http://backend:8000/v1/api
# 模型配置刷新间隔（秒）
MODEL_CONFIG_REFRESH_INTERVAL=300
# 默认模型配置（作为fallback）
OPENAI_API_KEY=sk-YyiBg6DSn1Fc2KBNU6ZYtw
OPENAI_API_BASE=http://*************:4033/
EOF
  fi
  
  # 启动 backend 服务（deep_search 依赖它）
  print_message "启动依赖的 backend 服务..." "$YELLOW"
  docker-compose up -d backend
  
  # 等待 backend 服务启动
  print_message "等待 backend 服务启动..." "$YELLOW"
  sleep 10
  
  # 启动 deep_search 服务
  docker-compose up -d --build deep_search
  
  if [ $? -eq 0 ]; then
    print_message "✅ Deep Search 服务已成功启动" "$GREEN"
    print_message "服务可通过以下地址访问:" "$BLUE"
    print_message "  Deep Search API: http://localhost:2024" "$BLUE"
    print_message "  LangGraph Studio: https://smith.langchain.com/studio/?baseUrl=http://localhost:2024" "$BLUE"
    print_message "  API 文档: http://localhost:2024/docs" "$BLUE"
    print_message "  Backend API: http://localhost:8000" "$BLUE"
  else
    print_message "❌ Deep Search 服务启动失败，请检查错误信息" "$RED"
  fi
}

start_deep_search_services() {
  print_message "启动 Deep Search 服务..." "$YELLOW"
  
  # 启动 backend 服务（deep_search 依赖它）
  docker-compose up -d backend
  
  # 等待 backend 服务启动
  print_message "等待 backend 服务启动..." "$YELLOW"
  sleep 5
  
  # 启动 deep_search 服务
  docker-compose up -d deep_search
  
  if [ $? -eq 0 ]; then
    print_message "✅ Deep Search 服务已成功启动" "$GREEN"
    print_message "服务可通过以下地址访问:" "$BLUE"
    print_message "  Deep Search API: http://localhost:2024" "$BLUE"
    print_message "  LangGraph Studio: https://smith.langchain.com/studio/?baseUrl=http://localhost:2024" "$BLUE"
    print_message "  API 文档: http://localhost:2024/docs" "$BLUE"
    print_message "  Backend API: http://localhost:8000" "$BLUE"
  else
    print_message "❌ Deep Search 服务启动失败，请检查错误信息" "$RED"
  fi
}

stop_deep_search_services() {
  print_message "停止 Deep Search 服务..." "$YELLOW"
  docker-compose stop deep_search

  if [ $? -eq 0 ]; then
    print_message "✅ Deep Search 服务已成功停止" "$GREEN"
  else
    print_message "❌ Deep Search 服务停止失败，请检查错误信息" "$RED"
  fi
}

view_deep_search_logs() {
  print_message "查看 Deep Search 服务日志..." "$YELLOW"
  docker-compose logs -f deep_search
}

check_deep_search_status() {
  print_message "Deep Search 服务状态:" "$YELLOW"
  docker-compose ps deep_search backend
}

# 主函数
main() {
  # 如果没有参数，显示帮助信息
  if [ $# -eq 0 ]; then
    show_help
    exit 0
  fi
  
  # 处理参数
  case "$1" in
    --help|-h)
      show_help
      ;;
    --build|-b)
      load_env
      check_network_config
      check_supabase
      build_and_start
      ;;
    --start|-s)
      load_env
      check_network_config
      check_supabase
      start_services
      ;;
    --stop|-d)
      stop_services
      ;;
    --restart|-r)
      restart_services
      ;;
    --logs|-l)
      view_logs
      ;;
    --status|-t)
      check_status
      ;;
    # 开发环境选项
    --dev-build|-db)
      load_env
      check_network_config
      check_supabase
      build_and_start_dev
      ;;
    --dev-build-selective|-dbs)
      load_env
      check_network_config
      check_supabase
      build_and_start_dev_selective
      ;;
    --dev-start|-ds)
      load_env
      check_network_config
      check_supabase
      start_dev_services
      ;;
    --dev-stop|-dd)
      stop_dev_services
      ;;
    --dev-logs|-dl)
      view_dev_logs
      ;;
    --dev-status|-dt)
      check_dev_status
      ;;
    # Deep Search 服务选项
    --deep-search-build|-dsb)
      load_env
      check_network_config
      build_and_start_deep_search
      ;;
    --deep-search-start|-dss)
      load_env
      check_network_config
      start_deep_search_services
      ;;
    --deep-search-stop|-dsd)
      stop_deep_search_services
      ;;
    --deep-search-logs|-dsl)
      view_deep_search_logs
      ;;
    --deep-search-status|-dst)
      check_deep_search_status
      ;;
    *)
      print_message "未知选项: $1" "$RED"
      show_help
      exit 1
      ;;
  esac
}

# 执行主函数
main "$@"
