import { useState, useEffect, useCallback } from 'react';
import { NotificationService } from '@/utils/notificationService';
import { NotificationData } from '@/types/notification';

export function useNotifications() {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);

  // 获取未读通知数量
  const fetchUnreadCount = useCallback(async () => {
    const count = await NotificationService.getUnreadCount();
    setUnreadCount(count);
  }, []);

  // 获取通知列表
  const fetchNotifications = useCallback(async (params?: {
    skip?: number;
    limit?: number;
    unreadOnly?: boolean;
  }) => {
    setLoading(true);
    try {
      const data = await NotificationService.getNotifications(params);
      setNotifications(data);
    } catch (error) {
      console.error('获取通知失败:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // 标记通知为已读
  const markAsRead = useCallback(async (notificationId: string) => {
    const success = await NotificationService.markAsRead(notificationId);
    if (success) {
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
    return success;
  }, []);

  // 标记所有通知为已读
  const markAllAsRead = useCallback(async () => {
    const success = await NotificationService.markAllAsRead();
    if (success) {
      setNotifications([]);
      setUnreadCount(0);
    }
    return success;
  }, []);

  // 定期检查未读通知
  useEffect(() => {
    fetchUnreadCount();
    const interval = setInterval(fetchUnreadCount, 30000); // 每30秒检查一次
    return () => clearInterval(interval);
  }, [fetchUnreadCount]);

  return {
    notifications,
    unreadCount,
    loading,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    refresh: fetchNotifications
  };
}