import { cn } from "@/lib/utils";

interface SparklesIconProps {
  className?: string;
  size?: number;
}

export function SparklesIcon({ className, size = 24 }: SparklesIconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={cn("lucide lucide-sparkles-icon", className)}
    >
      <style>
        {`
          @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
          @keyframes sparkle {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
          }
          @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
          }
          @keyframes colorChange {
            0% { stroke: #FFD700; filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.5)); }
            20% { stroke: #FF6B6B; filter: drop-shadow(0 0 2px rgba(255, 107, 107, 0.5)); }
            40% { stroke: #4ECDC4; filter: drop-shadow(0 0 2px rgba(78, 205, 196, 0.5)); }
            60% { stroke: #FFE66D; filter: drop-shadow(0 0 2px rgba(255, 230, 109, 0.5)); }
            80% { stroke: #FF9F1C; filter: drop-shadow(0 0 2px rgba(255, 159, 28, 0.5)); }
            100% { stroke: #FFD700; filter: drop-shadow(0 0 2px rgba(255, 215, 0, 0.5)); }
          }
          .sparkle-main {
            animation: rotate 8s linear infinite;
            transform-origin: center;
          }
          .sparkle-ray {
            animation: sparkle 1.5s ease-in-out infinite, colorChange 4s linear infinite;
          }
          .sparkle-pulse {
            animation: pulse 2s ease-in-out infinite;
            fill: #FFA500;
            filter: drop-shadow(0 0 3px rgba(255, 165, 0, 0.3));
          }
        `}
      </style>
      <g className="sparkle-main">
        <path 
          d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"
          className="sparkle-pulse"
        />
      </g>
      <path d="M20 3v4" className="sparkle-ray" />
      <path d="M22 5h-4" className="sparkle-ray" />
      <path d="M4 17v2" className="sparkle-ray" />
      <path d="M5 18H3" className="sparkle-ray" />
    </svg>
  );
} 