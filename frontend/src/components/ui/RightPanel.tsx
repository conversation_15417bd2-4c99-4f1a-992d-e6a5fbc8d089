import React from "react"
import { useRightPanelStore } from "@/store/rightPanelStore"
import { X } from "lucide-react"

export function RightPanel({ children }: { children?: React.ReactNode }) {
  const { isOpen, close } = useRightPanelStore()
  if (!isOpen) return null
  return (
    <div className="fixed top-0 right-0 h-full w-[540px] bg-white shadow-lg z-50 flex flex-col">
      <div className="flex justify-end p-2">
        <button onClick={close} className="text-gray-500 hover:text-black">
          <X className="w-5 h-5" />
        </button>
      </div>
      <div className="flex-1 overflow-y-auto">{children}</div>
    </div>
  )
} 