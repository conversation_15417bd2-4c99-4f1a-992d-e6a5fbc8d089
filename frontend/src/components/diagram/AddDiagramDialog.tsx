import { useState, useEffect } from "react";
// import { mockAuth } from "@/mocks/authMock";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useDiagramStore } from "@/store/diagramStore";
import { FileDropzone } from "../FileDropzone";
import { Switch } from "@/components/ui/switch";
// 导入 DiagramType 类型
import type { DiagramType } from "@/store/diagramStore";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function AddDiagramDialog({ open, onOpenChange }: Props) {
  // 不再使用 mockAuth
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'flowchart',
    source: '',
  });

  const [file, setFile] = useState<File | null>(null);
  const { fetchDiagrams } = useDiagramStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAIEnabled, setIsAIEnabled] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    if (open) {
      setFormData({
        name: '',
        description: '',
        type: 'flowchart' as DiagramType,
        source: '',
      });
      setFile(null);
      setIsAIEnabled(false);
    }
  }, [open]);

  const handleAIToggle = async (enabled: boolean) => {
    setIsAIEnabled(enabled);
    if (enabled && file) {
      setIsGenerating(true);
      await generateAIDescription(file);
      setIsGenerating(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;
    setError(null);

    try {
      setIsSubmitting(true);

      // 获取 token
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('用户未登录，请先登录');
      }

      // 创建 FormData 对象用于上传文件
      const uploadData = new FormData();
      uploadData.append('file', file);
      uploadData.append('name', formData.name);

      if (formData.description) {
        uploadData.append('description', formData.description);
      }

      if (formData.source) {
        uploadData.append('source', formData.source);
      }

      if (formData.type) {
        uploadData.append('type', formData.type);
      }

      // console.log('Uploading data:', {
      //   file: file,
      //   name: formData.name,
      //   description: formData.description,
      //   source: formData.source,
      //   type: formData.type
      // });

      // 发送请求到后端 API
      const response = await fetch('/api/diagram/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          // 不要设置 Content-Type，让浏览器自动设置正确的 multipart/form-data 和边界
        },
        body: uploadData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Upload error response:', errorText);

        let errorMessage = `上传失败，状态码: ${response.status}`;
        try {
          const errorData = JSON.parse(errorText);
          if (errorData.detail) {
            errorMessage = typeof errorData.detail === 'string'
              ? errorData.detail
              : JSON.stringify(errorData.detail);
          }
        } catch (e) {
          // 如果无法解析 JSON，使用默认错误消息
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      // console.log('图表上传成功:', data);

      onOpenChange(false);
      fetchDiagrams(); // 刷新图表列表
    } catch (error: any) {
      console.error('Error:', error);
      setError(error.message || '上传失败，请重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateAIDescription = async (imageFile: File) => {
    if (!imageFile || !imageFile.type.includes('jpeg')) {
      setError('目前仅支持 JPG 格式的图片');
      return;
    }

    const formData = new FormData();
    formData.append('file', imageFile);
    formData.append('prompt', '请描述该图片，用简单的50个字给出总结');

    try {
      const response = await fetch('http://***********:4066/image', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      if (response.ok) {
        setFormData(prev => ({ ...prev, description: data.output }));
      } else {
        throw new Error(data.message || '生成描述失败');
      }
    } catch (error) {
      console.error('AI 描述生成错误:', error);
      setError('AI 描述生成失败，请手动输入或重试');
      setIsAIEnabled(false);
    }
  };

  const handleFileSelect = async (file: File) => {
    setFile(file);
    // 移除缩略图生成逻辑
    if (isAIEnabled) {
      setIsGenerating(true);
      await generateAIDescription(file);
      setIsGenerating(false);
    }
  };

  // try {
  //   const thumbnailBase64 = await generateThumbnail(file);
  //   setFormData(prev => ({ ...prev, thumbnail: thumbnailBase64 }));
  //   if (isAIEnabled) {
  //     generateAIDescription(file);
  //   }
  // } catch (error) {
  //   console.error('生成缩略图失败:', error);
  //   setError('生成缩略图失败');
  // }
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[400px] p-6">
        <DialogHeader>
          <DialogTitle>收藏好图</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="text-red-500 text-sm">{error}</div>
          )}
          <div className="space-y-4">
            <FileDropzone onFileSelect={handleFileSelect} />
            <div className="flex gap-4">
              <div className="flex-[0.8]">
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="请输入标题 *"
                  required
                  className="placeholder:text-red-500"
                />
              </div>
              <div className="flex-[0.2]">
                <Select
                  value={formData.type}
                  onValueChange={(value: DiagramType) => setFormData(prev => ({ ...prev, type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择类型 *" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="flowchart">流程图</SelectItem>
                    <SelectItem value="framework">框架图</SelectItem>
                    <SelectItem value="architecture">体系图</SelectItem>
                    <SelectItem value="other">其他类型</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <div className="flex items-center justify-end mb-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="ai-mode"
                    checked={isAIEnabled}
                    onCheckedChange={handleAIToggle}
                    disabled={isGenerating}
                  />
                  <Label htmlFor="ai-mode" className="text-sm text-gray-500">
                    {isGenerating ? "生成中..." : "AI自动生成"}
                  </Label>
                </div>
              </div>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                disabled={isGenerating}
                placeholder={isGenerating ? "AI 正在生成描述..." : "请输入描述内容 *"}
                className="placeholder:text-red-500"
              />
            </div>
            <div>
              <Input
                id="source"
                value={formData.source}
                onChange={(e) => setFormData(prev => ({ ...prev, source: e.target.value }))}
                placeholder="请输入来源链接（选填）"
              />
            </div>
          </div>
          <Button type="submit" disabled={isSubmitting} className="w-full">
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2" />
                上传中...
              </div>
            ) : '上传'}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}
