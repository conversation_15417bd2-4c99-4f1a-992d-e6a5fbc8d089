//图库中点击图片查看详细信息组件


import { Diagram } from "@/types"
import { EditableField } from "../EditableField";
import { useState, useRef, useEffect } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Maximize2, Minimize2, X } from "lucide-react";

interface DiagramDetailProps {
  diagram: Diagram;
  children: React.ReactNode;
}



// 2. 移除 useState 和 isFullscreen 状态
export function DiagramDetail({ diagram, children }: DiagramDetailProps) {
  const [showFullImage, setShowFullImage] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 直接进入全屏模式查看图片
  const enterFullscreenView = () => {
    // 先关闭详情对话框
    setShowDetail(false);
    // 然后打开全屏图片查看器
    setShowFullImage(true);
    // 延迟一点时间确保DOM已渲染，然后进入全屏
    setTimeout(() => {
      if (containerRef.current) {
        containerRef.current.requestFullscreen().catch(err => {
          console.error(`全屏模式错误: ${err.message}`);
        });
      }
    }, 200); // 增加延迟时间，确保对话框切换完成
  };

  // 处理全屏切换
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      // 进入全屏模式
      containerRef.current?.requestFullscreen().catch(err => {
        console.error(`全屏模式错误: ${err.message}`);
      });
      setIsFullscreen(true);
    } else {
      // 退出全屏模式
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = !!document.fullscreenElement;
      setIsFullscreen(isCurrentlyFullscreen);
      
      // 如果从全屏模式退出且图片查看器是打开的，则关闭图片查看器
      if (!isCurrentlyFullscreen && showFullImage && isFullscreen) {
        setShowFullImage(false);
      }
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, [showFullImage, isFullscreen]);

  // 完全退出图片查看器
  const exitImageViewer = () => {
    // 如果在全屏模式，先退出全屏
    if (document.fullscreenElement) {
      document.exitFullscreen();
    }
    // 关闭图片查看器
    setShowFullImage(false);
    setIsFullscreen(false);
  };

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!showFullImage) return;

      // ESC 键直接完全退出图片查看器
      if (e.key === 'Escape') {
        e.preventDefault(); // 阻止默认行为
        exitImageViewer();
      }

      // F 键切换全屏
      if (e.key === 'f' || e.key === 'F') {
        toggleFullscreen();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [showFullImage, isFullscreen]);
  const handleUpdate = async (field: string, value: string) => {
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) {
        throw new Error('用户未登录，请先登录');
      }

      // 使用相对路径
      const response = await fetch(`/api/diagram/${diagram.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        },
        body: JSON.stringify({ [field]: value }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.detail || '更新失败');
      }

      // console.log('更新成功:', field, value);

    } catch (error) {
      console.error('Update error:', error);
      throw new Error(error instanceof Error ? error.message : '更新失败');
    }
  };

  return (
    <>
      {/* 触发详情对话框 */}
      <div onClick={() => setShowDetail(true)} className="cursor-pointer">
        {children}
      </div>

      {/* 详情对话框 - 居中显示 */}
      <Dialog open={showDetail} onOpenChange={setShowDetail}>
        <DialogContent className="max-w-[900px] max-h-[85vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              {diagram.name || '图表详情'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 overflow-y-auto pr-2">
            <div 
              className="relative cursor-zoom-in group"
              onClick={enterFullscreenView}
            >
              <img
                src={diagram.image_url}
                alt={diagram.name}
                className="w-full rounded-lg object-contain group-hover:opacity-90 transition-opacity"
              />
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-lg pointer-events-none">
                <div className="bg-white/90 px-3 py-1 rounded-full text-sm font-medium">
                  点击查看大图
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <EditableField
                label="名称"
                value={diagram.name}
                onUpdate={(value) => handleUpdate('name', value)}
              />

              <EditableField
                label="描述"
                value={diagram.description}
                isTextarea
                onUpdate={(value) => handleUpdate('description', value)}
              />

              {diagram.source && (
                <EditableField
                  label="来源"
                  value={diagram.source}
                  onUpdate={(value) => handleUpdate('source', value)}
                />
              )}

              {diagram.paper_title && (
                <div>
                  <h4 className="text-sm font-medium mb-1">论文标题</h4>
                  <p className="text-sm text-gray-700 dark:text-gray-300">{diagram.paper_title}</p>
                </div>
              )}

              {/* {diagram.paper_authors && (
                <div>
                  <h4 className="text-sm font-medium mb-1">作者：{diagram.paper_authors}</h4>
                </div>
              )} */}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={showFullImage} onOpenChange={setShowFullImage}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] p-0 overflow-hidden border-0 bg-black/95 backdrop-blur-md">
          {/* Header with improved styling */}
          <DialogHeader className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/80 to-transparent backdrop-blur-sm">
            <div className="flex justify-between items-center px-6 py-4">
              <DialogTitle className="text-white font-medium text-lg">
                {diagram.name || '详图'}
              </DialogTitle>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleFullscreen}
                  className="h-9 w-9 text-white hover:bg-white/20 hover:text-white transition-colors"
                  title={isFullscreen ? "退出全屏" : "全屏显示"}
                >
                  {isFullscreen ? <Minimize2 className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowFullImage(false)}
                  className="h-9 w-9 text-white hover:bg-white/20 hover:text-white transition-colors"
                  title="关闭"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </DialogHeader>

          {/* Main content area */}
          <div
            ref={containerRef}
            className={`h-[95vh] w-full flex flex-col items-center justify-center relative ${isFullscreen ? 'bg-black h-screen' : 'bg-black/95'
              }`}
          >
            {/* Image container with better centering */}
            <div className="flex-1 flex items-center justify-center p-4 pt-20">
              <img
                ref={imageRef}
                src={diagram.image_url}
                alt={diagram.name}
                className={`max-w-full max-h-full object-contain transition-all duration-300 ease-in-out shadow-2xl ${isFullscreen ? 'max-h-screen p-0' : 'rounded-lg'
                  }`}
                style={{ objectFit: 'contain' }}
              />
            </div>

            {/* Keyboard shortcuts hint with improved styling */}
            <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10">
              <div className="bg-black/60 backdrop-blur-sm rounded-full px-4 py-2 text-white/80 text-sm flex items-center gap-3">
                <span className="flex items-center gap-1">
                  <kbd className="px-2 py-1 bg-white/20 rounded text-xs font-mono">F</kbd>
                  <span>全屏</span>
                </span>
                <span className="text-white/40">|</span>
                <span className="flex items-center gap-1">
                  <kbd className="px-2 py-1 bg-white/20 rounded text-xs font-mono">ESC</kbd>
                  <span>关闭</span>
                </span>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
