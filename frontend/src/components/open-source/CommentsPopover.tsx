import { useEffect, useState } from 'react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, MessageSquare } from 'lucide-react';
import { useOpenSourceStore, Discussion } from '@/store/openSourceStore';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import ReactMarkdown from 'react-markdown';

interface CommentsPopoverProps {
  projectId: string;
  discussions: Discussion[];
}

function CommentForm({ projectId }: { projectId: string }) {
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { addComment } = useOpenSourceStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim()) return;
    setIsLoading(true);
    try {
      await addComment(projectId, content);
      setContent('');
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <form onSubmit={handleSubmit} className="p-4 border-t">
      <Textarea 
        placeholder="添加评论..." 
        className="mb-2" 
        value={content}
        onChange={(e) => setContent(e.target.value)}
      />
      <Button type="submit" size="sm" disabled={isLoading || !content.trim()}>
        {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        提交
      </Button>
    </form>
  );
}

export function CommentsPopover({ projectId, discussions }: CommentsPopoverProps) {
  const [open, setOpen] = useState(false);
  const { fetchDiscussions } = useOpenSourceStore();
  
  useEffect(() => {
    if (open) {
      fetchDiscussions(projectId);
    }
  }, [open, projectId, fetchDiscussions]);
  
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button size="sm" variant="ghost">
          <MessageSquare className="h-4 w-4 mr-1" />
          {discussions?.length || 0}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0">
        <div className="p-4">
          <h4 className="font-medium leading-none">评论</h4>
        </div>
        <ScrollArea className="h-64">
          <div className="p-4 pt-0 space-y-4">
            {discussions && discussions.length > 0 ? (
              discussions.map((comment) => (
                <div key={comment.id} className="flex items-start gap-2.5">
                    <Avatar className="w-8 h-8">
                        <AvatarImage src={comment.creator?.avatar_url} />
                        <AvatarFallback>{comment.creator?.username.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-semibold">{comment.creator?.display_name || comment.creator?.username}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true, locale: zhCN })}
                      </p>
                    </div>
                    <div className="prose prose-sm max-w-none text-muted-foreground">
                      <ReactMarkdown>{comment.content}</ReactMarkdown>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-sm text-muted-foreground text-center">暂无评论，快来抢沙发！</p>
            )}
          </div>
        </ScrollArea>
        <CommentForm projectId={projectId}/>
      </PopoverContent>
    </Popover>
  );
} 