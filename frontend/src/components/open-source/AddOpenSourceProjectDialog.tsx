import { useState } from 'react';
import {
  Dialog,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { useOpenSourceStore } from '@/store/openSourceStore';
import { Loader2, Star } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface AddOpenSourceProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function AddOpenSourceProjectDialog({ open, onOpenChange, onSuccess }: AddOpenSourceProjectDialogProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [githubUrl, setGithubUrl] = useState('');
  const [rate, setRate] = useState(0); // For star rating
  const [isPublic, setIsPublic] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tags, setTags] = useState(''); // 新增标签输入
  const { addProject } = useOpenSourceStore();

  const handleClose = () => {
    setName('');
    setDescription('');
    setGithubUrl('');
    setRate(0); // Reset rating
    setIsPublic(true);
    setTags(''); // 重置标签
    setError(null);
    setIsLoading(false);
    onOpenChange(false);
  };

  const handleUrlBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    let url = e.target.value.trim();
    if (url && !/^https?:\/\//i.test(url)) {
      url = 'https://' + url;
      setGithubUrl(url);
    }
  };

  const validateUrl = (url: string) => {
    try {
      new URL(url);
      return url.includes('github.com');
    } catch (_) {
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !description || !githubUrl) {
      setError('名称、描述和 GitHub 地址是必填项。');
      return;
    }
    if (!validateUrl(githubUrl)) {
      setError('请输入一个有效的 GitHub 项目地址。');
      return;
    }

    setError(null);
    setIsLoading(true);
    try {
      await addProject({
        name,
        description,
        github_url: githubUrl,
        rate: rate,
        is_private: !isPublic,
        image_url: tags, // 用 image_url 字段存标签
      });
      onSuccess();
      handleClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : '添加项目失败，请稍后重试。');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[520px]">
        <DialogHeader>
          <DialogTitle>分享并复现开源项目</DialogTitle>
          <DialogDescription>
            记录一个优秀的开源项目，分享你的发现，并为未来的复现做好准备。
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-6 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                项目名称
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                placeholder="例如：Next.js"
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                项目描述
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                placeholder="详细介绍这个项目的功能、特点和用途。"
                rows={4}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="github_url" className="text-right">
                GitHub 地址
              </Label>
              <Input
                id="github_url"
                value={githubUrl}
                onChange={(e) => setGithubUrl(e.target.value)}
                onBlur={handleUrlBlur}
                className="col-span-3"
                placeholder="https://github.com/vercel/next.js"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right">推荐星级</Label>
              <div className="col-span-3 flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-6 w-6 cursor-pointer ${
                      i < rate ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
                    }`}
                    onClick={() => setRate(i + 1)}
                  />
                ))}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="is-public" className="text-right">
                是否公开
              </Label>
              <div className="col-span-3 flex items-center space-x-2">
                <Switch
                  id="is-public"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                />
                <span className="text-sm text-muted-foreground">
                  {isPublic ? '项目将对所有人可见' : '项目仅自己可见'}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tags" className="text-right">
                标签
              </Label>
              <Input
                id="tags"
                value={tags}
                onChange={e => setTags(e.target.value)}
                className="col-span-3"
                placeholder="如：AI,工具,开源"
              />
            </div>
          </div>
          {error && <p className="text-sm text-destructive text-center pb-2">{error}</p>}
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isLoading ? '正在保存...' : '保存项目'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 