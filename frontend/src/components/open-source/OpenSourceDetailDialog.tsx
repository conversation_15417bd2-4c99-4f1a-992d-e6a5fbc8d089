import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '../ui/dialog';
import { Card } from '../ui/card';
import ReactMarkdown from 'react-markdown';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import type { FC } from 'react';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents';
import 'katex/dist/katex.min.css';
import { Badge } from "@/components/ui/badge";

interface Comment {
  id: string;
  content: string;
  created_at: string;
  creator?: { display_name?: string; username?: string };
  url?: string;
}

interface Project {
  id: string;
  name: string;
  description: string;
  rate?: number;
  tags?: string[];
  github_url?: string;
  image_url?: string;
  [key: string]: any;
}

interface OpenSourceDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project: Project | null;
  comments: Comment[];
}

export const OpenSourceDetailDialog: FC<OpenSourceDetailDialogProps> = ({ open, onOpenChange, project, comments }) => {
  if (!project) return null;
  // 解析标签
  const tags = project.image_url?.split(',').map(tag => tag.trim()).filter(Boolean) ?? [];
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold flex items-center gap-2">
            {project.name}
            {project.rate && project.rate > 4 && (
              <span className="ml-2 px-2 py-0.5 rounded bg-amber-100 text-amber-700 text-xs font-semibold border border-amber-300">高分推荐</span>
            )}
          </DialogTitle>
          <div className="flex items-center gap-2 mt-2">
            <a href={project.github_url} target="_blank" rel="noopener noreferrer" className="inline-flex items-center gap-1 text-blue-600 hover:underline text-sm font-medium">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4"><path d="M12 2C6.477 2 2 6.484 2 12.021c0 4.428 2.865 8.184 6.839 9.504.5.092.682-.217.682-.482 0-.237-.009-.868-.014-1.703-2.782.605-3.369-1.342-3.369-1.342-.454-1.155-1.11-1.463-1.11-1.463-.908-.62.069-.608.069-.608 1.004.07 1.532 1.032 1.532 1.032.892 1.53 2.341 1.088 2.91.832.091-.647.35-1.088.636-1.339-2.221-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.254-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.025A9.564 9.564 0 0 1 12 6.844c.85.004 1.705.115 2.504.337 1.909-1.295 2.748-1.025 2.748-1.025.546 1.378.202 2.396.1 2.65.64.7 1.028 1.595 1.028 2.688 0 3.847-2.337 4.695-4.566 4.944.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.749 0 .267.18.577.688.479C19.138 20.2 22 16.448 22 12.021 22 6.484 17.523 2 12 2Z" /></svg>
              <span>GitHub 仓库地址</span>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-4 h-4">
                <path strokeLinecap="round" strokeLinejoin="round" d="M17.25 6.75v-2.25A2.25 2.25 0 0 0 15 2.25h-7.5A2.25 2.25 0 0 0 5.25 4.5v15A2.25 2.25 0 0 0 7.5 21.75h7.5A2.25 2.25 0 0 0 17.25 19.5v-2.25M15 12h6m0 0-3-3m3 3-3 3" />
              </svg>
            </a>
          </div>
          {/* 标签展示 */}
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {tags.map(tag => (
                <Badge key={tag} className="bg-blue-50 text-blue-700 border border-blue-200 px-2 py-0.5 text-xs">{tag}</Badge>
              ))}
            </div>
          )}
          <DialogDescription className="mt-2 text-base text-muted-foreground">
            <div className="prose max-w-none">
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkMath]}
                rehypePlugins={[rehypeKatex]}
                components={MarkdownComponents}
              >
                {project.description}
              </ReactMarkdown>
            </div>
          </DialogDescription>
        </DialogHeader>
        <div className="mt-6">
          <h3 className="font-bold text-lg mb-2 flex items-center gap-2">
            评论
            <span className="text-xs text-muted-foreground">({comments.length})</span>
          </h3>
          <div className="divide-y divide-muted-foreground/10">
            {comments.length === 0 && <div className="text-muted-foreground text-sm py-4">暂无评论</div>}
            {comments.map(comment => (
              <div key={comment.id} className="py-3">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-semibold text-sm">{comment.creator?.display_name || comment.creator?.username}</span>
                  <span className="text-xs text-muted-foreground">{formatDistanceToNow(new Date(comment.created_at), { addSuffix: true, locale: zhCN })}</span>
                </div>
                <div className="prose prose-sm max-w-none text-foreground">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm, remarkMath]}
                    rehypePlugins={[rehypeKatex]}
                    components={MarkdownComponents}
                  >
                    {comment.content}
                  </ReactMarkdown>
                  {comment.url && (
                    <a
                      href={comment.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-1 text-blue-600 hover:underline text-xs mt-1"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-3 h-3">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M17.25 6.75v-2.25A2.25 2.25 0 0 0 15 2.25h-7.5A2.25 2.25 0 0 0 5.25 4.5v15A2.25 2.25 0 0 0 7.5 21.75h7.5A2.25 2.25 0 0 0 17.25 19.5v-2.25M15 12h6m0 0-3-3m3 3-3 3" />
                      </svg>
                      链接
                    </a>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 