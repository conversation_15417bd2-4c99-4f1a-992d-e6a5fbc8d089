import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Star } from 'lucide-react';
import { useOpenSourceStore } from '@/store/openSourceStore';
import { toast } from 'sonner';

interface AdminRateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  projectName: string;
  currentRate: number;
}

export function AdminRateDialog({
  open,
  onOpenChange,
  projectId,
  projectName,
  currentRate
}: AdminRateDialogProps) {
  const [rate, setRate] = useState(currentRate.toString());
  const [loading, setLoading] = useState(false);
  const { updateProjectRate } = useOpenSourceStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const rateValue = parseFloat(rate);
    if (isNaN(rateValue) || rateValue < 0 || rateValue > 5) {
      toast.error('评分必须是0-5之间的数字');
      return;
    }

    setLoading(true);
    try {
      await updateProjectRate(projectId, rateValue);
      toast.success('评分更新成功');
      onOpenChange(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : '更新评分失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRateClick = (value: number) => {
    setRate(value.toString());
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-amber-500" />
            设置项目评分
          </DialogTitle>
          <DialogDescription>
            为项目 "{projectName}" 设置评分 (0-5分)
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="rate">评分</Label>
            <div className="flex items-center gap-2">
              <Input
                id="rate"
                type="number"
                min="0"
                max="5"
                step="0.1"
                value={rate}
                onChange={(e) => setRate(e.target.value)}
                placeholder="输入0-5之间的评分"
                className="flex-1"
              />
              <div className="flex gap-1">
                {[1, 2, 3, 4, 5].map((value) => (
                  <Button
                    key={value}
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="p-1 h-8 w-8"
                    onClick={() => handleRateClick(value)}
                  >
                    <Star
                      className={`h-4 w-4 ${
                        parseFloat(rate) >= value
                          ? 'fill-amber-400 text-amber-400'
                          : 'text-gray-300'
                      }`}
                    />
                  </Button>
                ))}
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              当前评分: {currentRate} | 评分大于4将显示"推荐"标签
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '更新中...' : '确认更新'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}