import { useEffect, useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { fetchTLDRNews, type NewsItem } from '@/services/newsService';
import { X } from 'lucide-react';

interface NewsPanelProps {
  onClose: () => void;
}

export function NewsPanel({ onClose }: NewsPanelProps) {
  const [news, setNews] = useState<NewsItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadNews() {
      try {
        setLoading(true);
        console.log('NewsPanel: 开始加载新闻...');

        // 获取新闻
        const fetchedNews = await fetchTLDRNews();
        console.log('NewsPanel: 新闻获取成功');
        if (fetchedNews && fetchedNews.length > 0) {
          setNews(fetchedNews);
          setError(null);
        } else {
          console.warn('NewsPanel: 获取到的新闻为空');
          setError('未能获取到新闻内容，请稍后再试');
        }
      } catch (err) {
        console.error('NewsPanel: 加载新闻失败:', err);
        setError('加载新闻失败，请稍后再试');
      } finally {
        setLoading(false);
      }
    }

    loadNews();
  }, []);

  return (
    <div className="flex flex-col h-full bg-background">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-lg font-semibold">今日科技新闻</h2>
        <button
          onClick={onClose}
          className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      <div className="h-[calc(100vh-64px)] overflow-y-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-700">
        {loading ? (
          <div className="flex items-center justify-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="p-4 text-center text-red-500">{error}</div>
        ) : (
          <div className="p-4">
            {news.map((item) => (
              <div key={item.id} className="mb-6">
                <h3 className="font-medium text-base mb-1">{item.translatedTitle || item.title}</h3>
                <p className="whitespace-pre-wrap text-sm text-gray-700 dark:text-gray-300 mb-2">
                  {item.translatedSummary || item.summary}
                </p>
                <div className="flex justify-between text-xs text-gray-500">
                  <span>{item.source}</span>
                  <a 
                    href={item.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline"
                  >
                    阅读原文
                  </a>
                </div>
                <Separator className="mt-4" />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}