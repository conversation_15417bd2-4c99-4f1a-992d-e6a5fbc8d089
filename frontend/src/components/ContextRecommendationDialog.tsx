// TODO

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, ExternalLink, FileText, Bookmark, MessageSquare } from "lucide-react";
import { ContextItem } from "@/utils/context_finder";

interface ContextRecommendationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contextItems: ContextItem[];
  searchQuery: string;
  onAddContext: (item: ContextItem) => void;
}

export function ContextRecommendationDialog({ 
  open, 
  onOpenChange, 
  contextItems, 
  searchQuery,
  onAddContext 
}: ContextRecommendationDialogProps) {
  const [addedItems, setAddedItems] = useState<Set<string>>(new Set());

  const handleAddItem = (item: ContextItem) => {
    setAddedItems(prev => new Set([...prev, item.id]));
    onAddContext(item);
  };

  const getTypeIcon = (type: ContextItem['type']) => {
    switch (type) {
      case 'paper':
        return <FileText className="w-4 h-4" />;
      case 'bookmark':
        return <Bookmark className="w-4 h-4" />;
      case 'weibo':
        return <MessageSquare className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getTypeLabel = (type: ContextItem['type']) => {
    switch (type) {
      case 'paper':
        return '论文';
      case 'bookmark':
        return '书签';
      case 'weibo':
        return '微博';
      default:
        return '文档';
    }
  };

  const getTypeColor = (type: ContextItem['type']) => {
    switch (type) {
      case 'paper':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'bookmark':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'weibo':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span>🤔 猜你想要这些上下文</span>
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            基于搜索关键词 "{searchQuery}" 为您推荐相关内容
          </p>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {contextItems.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>暂无相关上下文推荐</p>
            </div>
          ) : (
            contextItems.map((item) => (
              <Card key={item.id} className="relative">
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-base flex items-center gap-2">
                        {getTypeIcon(item.type)}
                        {item.title}
                      </CardTitle>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge 
                          variant="secondary" 
                          className={getTypeColor(item.type)}
                        >
                          {getTypeLabel(item.type)}
                        </Badge>
                        {item.relevanceScore && (
                          <Badge variant="outline" className="text-xs">
                            相关度: {item.relevanceScore}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant={addedItems.has(item.id) ? "secondary" : "default"}
                      onClick={() => handleAddItem(item)}
                      disabled={addedItems.has(item.id)}
                      className="ml-2"
                    >
                      {addedItems.has(item.id) ? (
                        "已添加"
                      ) : (
                        <>
                          <Plus className="w-3 h-3 mr-1" />
                          添加
                        </>
                      )}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <CardDescription className="text-sm mb-2">
                    {item.content.length > 150 
                      ? `${item.content.substring(0, 150)}...` 
                      : item.content
                    }
                  </CardDescription>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    {item.author && (
                      <span>作者: {item.author}</span>
                    )}
                    {item.date && (
                      <span>日期: {item.date}</span>
                    )}
                    {item.url && (
                      <a 
                        href={item.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 hover:text-primary"
                      >
                        <ExternalLink className="w-3 h-3" />
                        查看原文
                      </a>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
        
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
          <Button 
            onClick={() => onOpenChange(false)}
            disabled={addedItems.size === 0}
          >
            完成 ({addedItems.size} 项已添加)
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}