import { MessageSquare } from "lucide-react";  // 添加图标导入

interface JumbotronProps {
  title: string;
  subtitle?: string;
  bgClassName?: string;
  children?: React.ReactNode;
}

export function Jumbotron({
  title,
  subtitle,
  bgClassName = "dark:bg-gray-900",
  children
}: JumbotronProps) {
  return (
    <section className={bgClassName}>
      <div className="py-4 px-4 mx-auto max-w-screen-xl text-center lg:py-8">
        <h2 className="mb-6 text-xl font-extrabold tracking-tight leading-none text-gray-900 md:text-3xl lg:text-4xl dark:text-white">
          {title}
        </h2>
        {subtitle && (
          <p className="mb-8 text-base font-normal text-red-900 lg:text-lg px-4 dark:text-gray-200 
            border-t border-gray-200 dark:border-gray-700 p-4 w-full flex items-center justify-center gap-2">
            "{subtitle}"
          </p>
        )}
        {children && (
          <div className="flex flex-col space-y-2 sm:flex-row sm:justify-center sm:space-y-0">
            {children}
          </div>
        )}
      </div>
    </section>
  );
}