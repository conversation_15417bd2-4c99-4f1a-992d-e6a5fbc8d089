'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>rigger } from "@/components/ui/sheet";
import { Flame, MessageCircle, ThumbsUp, AlertTriangle, Loader2 } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import API from '@/config/api';

interface HackerNewsItem {
  id: number;
  title: string;
  url: string;
  points: number;
  author: string;
  commentsCount: number;
}

export function HackerNewsDialog() {
  const [stories, setStories] = useState<HackerNewsItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (isOpen && stories.length === 0) { // Only fetch if open and stories are not yet loaded
      const fetchStories = async () => {
        setLoading(true);
        setError(null);
        try {
          const response = await fetch(API.HACKER_NEWS.TOP_STORIES);
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          const data: HackerNewsItem[] = await response.json();
          setStories(data);
        } catch (e: any) {
          setError(e.message || 'Failed to fetch stories');
        } finally {
          setLoading(false);
        }
      };

      fetchStories();
    }
  }, [isOpen, stories.length]);

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" className="fixed right-4 top-1/2 -translate-y-1/2 z-40 bg-orange-500 text-white hover:bg-orange-600 hover:text-white">
          <Flame className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[450px] sm:max-w-[540px] bg-gray-50 dark:bg-gray-900 flex flex-col">
        <SheetHeader className="bg-orange-500 -m-6 p-6 pb-4">
          <SheetTitle className="text-white text-lg flex items-center gap-2">
            <Flame className="h-5 w-5" />
            Hacker News
          </SheetTitle>
        </SheetHeader>
        <ScrollArea className="-mx-6 px-6 pt-4 flex-grow">
          <div className="min-h-full">
            {loading && (
              <div className="flex items-center justify-center h-full py-10">
                <Loader2 className="h-8 w-8 animate-spin text-orange-500" />
              </div>
            )}
            {error && (
              <div className="flex flex-col items-center justify-center h-full text-red-500 py-10">
                <AlertTriangle className="h-8 w-8 mb-2" />
                <p>Error: {error}</p>
              </div>
            )}
            {!loading && !error && (
              <ol className="space-y-4">
                {stories.map((item, index) => (
                  <li key={item.id} className="flex items-start gap-3">
                    <span className="text-gray-500 dark:text-gray-400 text-right w-6 pt-0.5">{index + 1}.</span>
                    <div className="flex-1">
                      <a href={item.url} target="_blank" rel="noopener noreferrer" className="text-gray-800 dark:text-gray-200 hover:underline">
                        {item.title}
                      </a>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <ThumbsUp className="h-3 w-3" />
                          <span>{item.points}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageCircle className="h-3 w-3" />
                          <span>{item.commentsCount}</span>
                        </div>
                        <span>by {item.author}</span>
                      </div>
                    </div>
                  </li>
                ))}
              </ol>
            )}
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
} 