import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Loader2, <PERSON>, Heart, Clock } from "lucide-react"
import { useEffect, useState } from "react"
import API from "@/config/api"

interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}

interface LikeUserInfo {
  user: UserInfo;
  liked_at: string;
}

interface LikesUsersDialogProps {
  postId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function LikesUsersDialog({ postId, isOpen, onClose }: LikesUsersDialogProps) {
  const [likeUsers, setLikeUsers] = useState<LikeUserInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchLikesUsers = async () => {
    if (!isOpen || !postId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const response = await fetch(API.POSTS.LIKES.USERS(postId), {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('获取点赞用户失败');
      }

      const data = await response.json();
      setLikeUsers(data);
    } catch (error) {
      console.error('获取点赞用户失败:', error);
      setError('获取点赞用户失败');
    } finally {
      setIsLoading(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return '刚刚';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}分钟前`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}小时前`;
    } else if (diffInSeconds < 2592000) {
      return `${Math.floor(diffInSeconds / 86400)}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchLikesUsers();
    }
  }, [isOpen, postId]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg">
            <div className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-500 fill-current" />
              <span>点赞用户</span>
            </div>
            <Badge variant="secondary" className="ml-auto">
              {likeUsers.length}
            </Badge>
          </DialogTitle>
        </DialogHeader>
        
        <ScrollArea className="max-h-96">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
              <span className="ml-2 text-muted-foreground">加载中...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-500 mb-2">❌</div>
              <p className="text-red-500">{error}</p>
            </div>
          ) : likeUsers.length === 0 ? (
            <div className="text-center py-12">
              <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-muted-foreground">暂无点赞用户</p>
            </div>
          ) : (
            <div className="space-y-3">
              {likeUsers.map((likeUser, index) => (
                <div 
                  key={likeUser.user.id} 
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors group"
                >
                  <div className="relative">
                    <Avatar className="h-10 w-10 ring-2 ring-white dark:ring-gray-800 shadow-sm">
                      <AvatarImage 
                        src={likeUser.user.thumbnail || "/placeholder.svg"} 
                        alt={likeUser.user.display_name || likeUser.user.username} 
                      />
                      <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white font-medium">
                        {likeUser.user.display_name?.slice(0, 2) || likeUser.user.username?.slice(0, 2) || likeUser.user.id.slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center">
                      <Heart className="h-2.5 w-2.5 text-white fill-current" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium truncate">
                        {likeUser.user.display_name || likeUser.user.username || likeUser.user.id.slice(0, 8)}
                      </span>
                      <Badge variant="outline" className="text-xs">V</Badge>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      {likeUser.user.email && (
                        <p className="text-sm text-muted-foreground truncate">
                          {likeUser.user.email}
                        </p>
                      )}
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{formatTimestamp(likeUser.liked_at)}</span>
                      </div>
                    </div>
                  </div>
                  {index === 0 && (
                    <Badge variant="default" className="text-xs bg-gradient-to-r from-red-400 to-pink-500">
                      最新
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
} 