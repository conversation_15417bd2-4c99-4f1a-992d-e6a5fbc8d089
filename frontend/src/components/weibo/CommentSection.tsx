import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { MentionInput } from "./MentionInput"
import { ImageUploader } from "./ImageUploader"
import {
  MessageSquare,
  SmilePlus,
  Image,
  Send,
  X,
  Trash2
} from "lucide-react"
import { useState } from "react"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents'
import { convertSupabaseUrl } from '@/utils/url-utils'
import { formatTimestamp } from '@/utils/timeUtils'
import 'katex/dist/katex.min.css'
import API from '@/config/api'

interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}

interface ChannelInfo {
  id: string;
  name: string;
  display_name: string;
  color?: string;
  icon?: string;
}

interface Comment {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
  };
  images: string[];
  owner?: UserInfo;
  channel?: ChannelInfo;
}

// 新增树形类型
type PostReplyTreeResponse = {
  post: Comment;
  replies: PostReplyTreeResponse[];
};

interface CommentSectionProps {
  rootReplies: PostReplyTreeResponse[];
  isLoading: boolean;
  user: { id: string, email: string, username: string } | null;
  commentInput: string;
  commentImage: File | null;
  commentImagePreview: string | null;
  showImageUploader: boolean;
  onCommentInputChange: (value: string) => void;
  onCommentImageSelect: (file: File | null) => void;
  onImageUploaderToggle: () => void;
  onSubmitComment: () => void;
  onDeleteComment: (commentId: string) => void;
  onImageClick: (src: string, alt?: string) => void;
  onReplySubmit: (parentId: string, content: string, image?: File | null) => void;
}

import { CommentTree } from "./CommentTree";

export function CommentSection({
  rootReplies,
  isLoading,
  user,
  commentInput,
  commentImage,
  commentImagePreview,
  showImageUploader,
  onCommentInputChange,
  onCommentImageSelect,
  onImageUploaderToggle,
  onSubmitComment,
  onDeleteComment,
  onImageClick,
  onReplySubmit
}: CommentSectionProps) {

  // 使用导入的formatTimestamp函数，已修复时区问题

  return (
    <div className="border-t bg-muted/30">
      <div className="p-4 space-y-4">
        {/* 根评论输入框 */}
        <div className="flex gap-3">
          <Avatar className="h-8 w-8">
            {(() => {
              let avatarUrl = "/placeholder.svg?height=32&width=32";
              if (user && 'thumbnail' in user && typeof user.thumbnail === 'string' && user.thumbnail) {
                avatarUrl = user.thumbnail.startsWith('http')
                  ? user.thumbnail
                  : `${API.BASE_URL}/${user.thumbnail.replace(/^\/+/,'')}`;
              }
              return (
                <AvatarImage
                  src={avatarUrl}
                  onError={e => { e.currentTarget.src = "/placeholder.svg?height=32&width=32"; }}
                />
              );
            })()}
            <AvatarFallback>
              {user?.username?.slice(0, 2) || user?.email?.slice(0, 2) || 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-2">
            <MentionInput
              value={commentInput}
              onChange={onCommentInputChange}
              placeholder="跟帖..."
              className="min-h-[80px]"
            />
            
            {/* 跟帖图片预览 */}
            {commentImagePreview && (
              <div className="relative flex justify-center">
                <img
                  src={commentImagePreview}
                  alt="跟帖图片预览"
                  className="h-20 w-auto object-contain rounded-lg border"
                />
                <button
                  onClick={() => onCommentImageSelect(null)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onImageUploaderToggle}
                  className="text-muted-foreground hover:text-primary"
                >
                  <Image className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  disabled
                  className="text-muted-foreground hover:text-primary"
                >
                  <SmilePlus className="h-4 w-4" />
                </Button>
              </div>
              <Button
                size="sm"
                onClick={onSubmitComment}
                disabled={!commentInput.trim()}
              >
                <Send className="h-4 w-4 mr-1" />
                跟帖
              </Button>
            </div>

            {/* 图片上传器 */}
            {showImageUploader && (
              <div className="border rounded-lg p-3 bg-background">
                <ImageUploader
                  onImageSelect={onCommentImageSelect}
                />
              </div>
            )}
          </div>
        </div>

        {/* 根评论树 */}
        {isLoading ? (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
          </div>
        ) : (
          <div className="space-y-3">
            {rootReplies.map(reply => (
              <CommentTree
                key={reply.post.post.id}
                node={reply}
                user={user}
                onDeleteComment={onDeleteComment}
                onImageClick={onImageClick}
                onReplySubmit={onReplySubmit}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}