import { useState } from 'react'
import { PostCard } from './PostCard'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Heart, Users } from 'lucide-react'

// 模拟数据
const mockPost = {
  post: {
    id: "test-post-1",
    content: "这是一个测试微博，用来展示点赞功能的效果。点赞用户会以小头像的形式显示在右侧，非常优雅！",
    timestamp: new Date().toISOString(),
    owner_id: "test-user-1"
  },
  images: [],
  owner: {
    id: "test-user-1",
    username: "testuser",
    display_name: "测试用户",
    email: "<EMAIL>",
    thumbnail: "/placeholder.svg"
  },
  channel: {
    id: "test-channel-1",
    name: "test",
    display_name: "测试板块",
    color: "#3B82F6"
  }
}

const mockUser = {
  id: "current-user-1",
  email: "<EMAIL>",
  username: "currentuser"
}

export function LikesPreview() {
  const [likeCount, setLikeCount] = useState(8)
  const [showCreatePost, setShowCreatePost] = useState(false)

  const handleLikeCountChange = (postId: string, newCount: number) => {
    setLikeCount(newCount)
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-red-500" />
            点赞功能预览
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground">
            这个组件展示了点赞功能的完整效果，包括：
          </div>
          <ul className="text-sm text-muted-foreground space-y-1 ml-4">
            <li>• 点赞按钮状态变化</li>
            <li>• 点赞数实时更新</li>
            <li>• 右侧点赞用户头像展示</li>
            <li>• 点击头像查看详细列表</li>
            <li>• 优雅的动画效果</li>
          </ul>
        </CardContent>
      </Card>

      <PostCard
        post={mockPost}
        user={mockUser}
        likeCount={likeCount}
        commentCount={3}
        onEdit={(postId, content) => console.log('编辑帖子:', postId, content)}
        onDelete={(postId) => console.log('删除帖子:', postId)}
        onImageClick={(src, alt) => console.log('点击图片:', src, alt)}
        onPostClick={(post) => console.log('点击帖子:', post)}
        onChatOpen={(post) => console.log('打开聊天:', post)}
        showExploreButton={true}
        onLikeCountChange={handleLikeCountChange}
      />

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            功能说明
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm">
            <strong>微信风格点赞设计：</strong>
            <ul className="mt-2 space-y-1 ml-4 text-muted-foreground">
              <li>• 点赞用户以小头像形式横向排列</li>
              <li>• 头像重叠效果，节省空间</li>
              <li>• 悬停放大动画，提升交互体验</li>
              <li>• 超过5个用户时显示"+N"和"等N人"</li>
              <li>• 点击任意头像或文字可查看完整列表</li>
            </ul>
          </div>
          
          <div className="text-sm">
            <strong>技术特性：</strong>
            <ul className="mt-2 space-y-1 ml-4 text-muted-foreground">
              <li>• 实时获取点赞数和用户列表</li>
              <li>• 防重复点击和加载状态</li>
              <li>• 响应式设计，适配不同屏幕</li>
              <li>• 优雅的错误处理和用户反馈</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 