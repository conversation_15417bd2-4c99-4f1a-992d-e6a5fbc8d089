import { Button } from "@/components/ui/button"
import { ThumbsUp, MessageSquare, BotMessageSquare } from "lucide-react"
import React from "react"
import { useRightPanelStore } from "@/store/rightPanelStore"

interface PostActionsProps {
  likeCount: number
  commentCount: number
  isLiked?: boolean
  isLiking?: boolean
  onLike?: (e: React.MouseEvent) => void
  onComment?: (e: React.MouseEvent) => void
  onExplore?: (e: React.MouseEvent) => void
  showExploreButton?: boolean
  likeButtonClassName?: string
  commentButtonClassName?: string
  exploreButtonClassName?: string
  disabledLike?: boolean
  postId?: string // 新增postId
}

export function PostActions({
  likeCount,
  commentCount,
  isLiked = false,
  isLiking = false,
  onLike,
  onComment,
  onExplore,
  showExploreButton = false,
  likeButtonClassName = '',
  commentButtonClassName = '',
  exploreButtonClassName = '',
  disabledLike = false,
  postId,
}: PostActionsProps) {
  const { open } = useRightPanelStore()
  return (
    <div className="flex items-center gap-4">
      <Button
        variant="ghost"
        size="sm"
        className={`text-muted-foreground hover:text-red-500 transition-colors ${isLiked ? 'text-red-500' : ''} ${likeButtonClassName}`}
        onClick={onLike}
        disabled={isLiking || disabledLike}
      >
        <ThumbsUp className={`h-4 w-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
        {likeCount}
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={`text-muted-foreground hover:text-blue-500 ${commentButtonClassName}`}
        onClick={onComment}
      >
        <MessageSquare className="h-4 w-4 mr-1" />
        跟帖 {commentCount > 0 && commentCount}
      </Button>
      {showExploreButton && postId && (
        <Button
          variant="ghost"
          size="sm"
          className={`text-muted-foreground hover:text-green-500 ${exploreButtonClassName}`}
          onClick={() => open(postId)}
        >
          <BotMessageSquare className="h-4 w-4 mr-1" />
          探究
        </Button>
      )}
    </div>
  )
} 