import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Hash, Users, Star, Plus, Check } from 'lucide-react';
import API from '@/config/api';

interface Channel {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  color?: string;
  icon?: string;
  post_count: number;
  member_count: number;
  is_default: boolean;
  is_joined?: boolean; // 用户是否已加入
}

interface ChannelNavBarProps {
  selectedChannelId?: string;
  onChannelChange: (channelId: string | undefined) => void;
  showAllOption?: boolean;
}

export function ChannelNavBar({ 
  selectedChannelId, 
  onChannelChange, 
  showAllOption = true 
}: ChannelNavBarProps) {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [loading, setLoading] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [joinedChannels, setJoinedChannels] = useState<Set<string>>(new Set());
  const [joiningChannels, setJoiningChannels] = useState<Set<string>>(new Set());
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const fetchChannels = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API.API_PATH}/channels/`);
      if (response.ok) {
        const data = await response.json();
        setChannels(data);
      }
    } catch (error) {
      console.error('获取板块列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserJoinedChannels = async () => {
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) return;

      const response = await fetch(API.CHANNELS.USER_JOINED, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });
      
      if (response.ok) {
        const joinedChannelsList = await response.json();
        const joinedIds = new Set(joinedChannelsList.map((ch: Channel) => ch.id));
        setJoinedChannels(joinedIds);
      }
    } catch (error) {
      console.error('获取用户已加入板块失败:', error);
    }
  };

  const handleJoinChannel = async (channelId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 防止触发板块选择
    
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (!token) return;

    setJoiningChannels(prev => new Set([...prev, channelId]));
    
    try {
      const response = await fetch(API.CHANNELS.JOIN(channelId), {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });

      if (response.ok) {
        setJoinedChannels(prev => new Set([...prev, channelId]));
        // 更新板块成员数量
        setChannels(prev => prev.map(ch => 
          ch.id === channelId 
            ? { ...ch, member_count: ch.member_count + 1 }
            : ch
        ));
      }
    } catch (error) {
      console.error('加入板块失败:', error);
    } finally {
      setJoiningChannels(prev => {
        const newSet = new Set(prev);
        newSet.delete(channelId);
        return newSet;
      });
    }
  };

  const handleLeaveChannel = async (channelId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 防止触发板块选择
    
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (!token) return;

    setJoiningChannels(prev => new Set([...prev, channelId]));
    
    try {
      const response = await fetch(API.CHANNELS.LEAVE(channelId), {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });

      if (response.ok) {
        setJoinedChannels(prev => {
          const newSet = new Set(prev);
          newSet.delete(channelId);
          return newSet;
        });
        // 更新板块成员数量
        setChannels(prev => prev.map(ch => 
          ch.id === channelId 
            ? { ...ch, member_count: Math.max(0, ch.member_count - 1) }
            : ch
        ));
      }
    } catch (error) {
      console.error('离开板块失败:', error);
    } finally {
      setJoiningChannels(prev => {
        const newSet = new Set(prev);
        newSet.delete(channelId);
        return newSet;
      });
    }
  };

  useEffect(() => {
    fetchChannels();
    fetchUserJoinedChannels();
  }, []);

  // 检查滚动状态
  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  useEffect(() => {
    checkScrollButtons();
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      window.addEventListener('resize', checkScrollButtons);
      return () => {
        container.removeEventListener('scroll', checkScrollButtons);
        window.removeEventListener('resize', checkScrollButtons);
      };
    }
  }, [channels]);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  const handleChannelClick = (channelId: string | undefined) => {
    onChannelChange(channelId);
  };

  if (loading) {
    return (
      <div className="bg-card border-b">
        <div className="container mx-auto px-4">
          <div className="flex items-center space-x-2 py-3">
            <div className="h-8 w-20 bg-muted animate-pulse rounded-full" />
            <div className="h-8 w-24 bg-muted animate-pulse rounded-full" />
            <div className="h-8 w-28 bg-muted animate-pulse rounded-full" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card border-b sticky top-0 z-40">
      <div className="container mx-auto px-4">
        <div className="relative flex items-center">
          {/* 左滚动按钮 */}
          {canScrollLeft && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute left-0 z-10 bg-card/80 backdrop-blur-sm shadow-sm"
              onClick={scrollLeft}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}

          {/* 板块导航容器 */}
          <div
            ref={scrollContainerRef}
            className="flex items-center space-x-1 py-3 overflow-x-auto scrollbar-hide scroll-smooth"
            style={{ 
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              WebkitScrollbar: { display: 'none' }
            }}
          >
            {/* 全部板块选项 */}
            {showAllOption && (
              <Button
                variant={!selectedChannelId ? "default" : "ghost"}
                size="sm"
                className={`
                  flex-shrink-0 rounded-full px-4 py-2 text-sm font-medium transition-all
                  ${!selectedChannelId 
                    ? 'bg-primary text-primary-foreground shadow-sm' 
                    : 'hover:bg-muted'
                  }
                `}
                onClick={() => handleChannelClick(undefined)}
              >
                <Hash className="w-4 h-4 mr-1" />
                全部
              </Button>
            )}

            {/* 板块列表 */}
            {channels.map((channel) => {
              const isSelected = selectedChannelId === channel.id;
              const isJoined = joinedChannels.has(channel.id);
              const isJoining = joiningChannels.has(channel.id);
              
              return (
                <div key={channel.id} className="flex-shrink-0 flex items-center space-x-1">
                  <Button
                    variant={isSelected ? "default" : "ghost"}
                    size="sm"
                    className={`
                      rounded-full px-4 py-2 text-sm font-medium transition-all
                      ${isSelected 
                        ? 'shadow-sm' 
                        : 'hover:bg-muted'
                      }
                    `}
                    style={{
                      backgroundColor: isSelected ? channel.color || '#3B82F6' : undefined,
                      color: isSelected ? 'white' : undefined,
                      borderColor: isSelected ? channel.color || '#3B82F6' : undefined
                    }}
                    onClick={() => handleChannelClick(channel.id)}
                  >
                    <div className="flex items-center space-x-2">
                      {/* 板块颜色指示器 */}
                      <div 
                        className={`w-2 h-2 rounded-full ${isSelected ? 'bg-white/80' : ''}`}
                        style={{ 
                          backgroundColor: isSelected ? 'rgba(255,255,255,0.8)' : channel.color || '#3B82F6'
                        }}
                      />
                      
                      {/* 板块名称 */}
                      <span>{channel.display_name}</span>
                      
                      {/* 默认板块标识 */}
                      {channel.is_default && (
                        <Star className="w-3 h-3" />
                      )}
                      
                      {/* 帖子数量 */}
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${isSelected ? 'bg-white/20 text-white' : ''}`}
                      >
                        {channel.post_count}
                      </Badge>
                    </div>
                  </Button>

                  {/* Join/Unjoin 按钮 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`
                      w-8 h-8 p-0 rounded-full transition-all
                      ${isJoined 
                        ? 'text-green-600 hover:text-green-700 hover:bg-green-50' 
                        : 'text-muted-foreground hover:text-primary hover:bg-primary/10'
                      }
                    `}
                    disabled={isJoining}
                    onClick={isJoined 
                      ? (e) => handleLeaveChannel(channel.id, e)
                      : (e) => handleJoinChannel(channel.id, e)
                    }
                    title={isJoined ? '取消关注' : '关注板块'}
                  >
                    {isJoining ? (
                      <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    ) : isJoined ? (
                      <Check className="w-3 h-3" />
                    ) : (
                      <Plus className="w-3 h-3" />
                    )}
                  </Button>
                </div>
              );
            })}
          </div>

          {/* 右滚动按钮 */}
          {canScrollRight && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-0 z-10 bg-card/80 backdrop-blur-sm shadow-sm"
              onClick={scrollRight}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

// 添加CSS样式到全局样式中
const styles = `
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
`;

// 如果需要，可以将样式注入到head中
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}