import { useState, useEffect } from 'react';
import { Bell, X, MessageCircle, Heart, AtSign, UserPlus, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { toast } from 'sonner';
import { NotificationType, type NotificationData } from '@/types/notification';
import { useNotifications } from '@/hooks/useNotifications';
import { useNotificationNavigator } from './NotificationNavigator';

type Notification = NotificationData;

export function NotificationBell() {
  const [isOpen, setIsOpen] = useState(false);
  const {
    notifications,
    unreadCount,
    loading,
    fetchNotifications,
    markAsRead,
    markAllAsRead
  } = useNotifications();
  const { handleNotificationClick: navigateToNotification } = useNotificationNavigator();

  // 处理标记所有通知为已读
  const handleMarkAllAsRead = async () => {
    const success = await markAllAsRead();
    if (success) {
      toast.success('所有通知已标记为已读');
    } else {
      toast.error('操作失败');
    }
  };

  // 处理通知点击 - 跳转到相应位置
  const handleNotificationClick = async (notification: Notification) => {
    // 标记为已读
    markAsRead(notification.id);
    setIsOpen(false);
    
    try {
      await navigateToNotification(notification);
    } catch (error) {
      // console.error('处理通知点击失败:', error);
      toast.error('跳转失败，请稍后重试');
    }
  };

  // 获取通知类型的图标和描述
  const getNotificationInfo = (type: NotificationType) => {
    switch (type) {
      case NotificationType.MENTION:
        return {
          icon: <AtSign className="h-4 w-4 text-blue-500" />,
          action: '在帖子中提到了你',
          color: 'text-blue-600'
        };
      case NotificationType.COMMENT:
        return {
          icon: <MessageCircle className="h-4 w-4 text-green-500" />,
          action: '评论了你的帖子',
          color: 'text-green-600'
        };
      case NotificationType.LIKE:
        return {
          icon: <Heart className="h-4 w-4 text-red-500" />,
          action: '点赞了你的帖子',
          color: 'text-red-600'
        };
      case NotificationType.FOLLOW:
        return {
          icon: <UserPlus className="h-4 w-4 text-purple-500" />,
          action: '关注了你',
          color: 'text-purple-600'
        };
      case NotificationType.CHANNEL_POST:
        return {
          icon: <FileText className="h-4 w-4 text-orange-500" />,
          action: '在你关注的频道发布了新帖子',
          color: 'text-orange-600'
        };
      default:
        return {
          icon: <Bell className="h-4 w-4 text-gray-500" />,
          action: '发送了通知',
          color: 'text-gray-600'
        };
    }
  };

  // 格式化时间
  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}天前`;
    
    return date.toLocaleDateString('zh-CN');
  };

  // 当弹窗打开时获取通知列表
  useEffect(() => {
    if (isOpen) {
      fetchNotifications({ limit: 20, unreadOnly: true });
    }
  }, [isOpen, fetchNotifications]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0" align="end">
        <div className="border-b p-3 flex items-center justify-between">
          <h3 className="font-semibold">通知</h3>
          {notifications.length > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleMarkAllAsRead}
              className="text-xs"
            >
              全部已读
            </Button>
          )}
        </div>
        
        <div className="max-h-96 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              加载中...
            </div>
          ) : notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              暂无新通知
            </div>
          ) : (
            notifications.map((notification) => {
              const notificationInfo = getNotificationInfo(notification.type);
              
              return (
                <div
                  key={notification.id}
                  className="group p-3 hover:bg-muted/50 cursor-pointer border-b last:border-b-0 transition-colors"
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start space-x-3">
                    {/* 用户头像 */}
                    <div className="relative">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={notification.sender_avatar} />
                        <AvatarFallback>
                          {notification.sender_name?.slice(0, 2) || '?'}
                        </AvatarFallback>
                      </Avatar>
                      {/* 通知类型图标 */}
                      <div className="absolute -bottom-1 -right-1 bg-background rounded-full p-0.5 border">
                        {notificationInfo.icon}
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      {/* 通知主要信息 */}
                      <div className="flex items-center space-x-1 text-sm">
                        <span className="font-medium">{notification.sender_name}</span>
                        <span className={`${notificationInfo.color} text-xs`}>
                          {notificationInfo.action}
                        </span>
                      </div>
                      
                      {/* 通知内容 */}
                      {notification.content && (
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {notification.content}
                        </p>
                      )}
                      
                      {/* 时间和额外信息 */}
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-muted-foreground">
                          {formatTimeAgo(notification.created_at)}
                        </p>
                        
                        {/* 未读标识 */}
                        {!notification.is_read && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                    </div>
                    
                    {/* 关闭按钮 */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        markAsRead(notification.id);
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}