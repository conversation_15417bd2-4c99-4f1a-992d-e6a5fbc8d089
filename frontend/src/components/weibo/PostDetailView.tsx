import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChannelBadge } from "./ChannelSelector"
import { CommentSection } from "./CommentSection"
import {
  MessageSquare,
  ThumbsUp,
  BotMessageSquare,
  ZoomIn
} from "lucide-react"
import { useState, useEffect } from "react"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents'
import { convertSupabaseUrl } from '@/utils/url-utils'
import { formatTimestamp } from '@/utils/timeUtils'
import { toast } from "sonner"
import API from '@/config/api'
import 'katex/dist/katex.min.css'
import { PostActions } from "./PostActions"

interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}

interface ChannelInfo {
  id: string;
  name: string;
  display_name: string;
  color?: string;
  icon?: string;
}

interface Post {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
  };
  images: string[];
  owner?: UserInfo;
  channel?: ChannelInfo;
}

interface PostDetailViewProps {
  post: Post;
  user: { id: string, email: string, username: string } | null;
  onImageClick: (src: string, alt?: string) => void;
  onOpenChat?: (post: Post) => void;
}

export function PostDetailView({
  post,
  user,
  onImageClick,
  onOpenChat
}: PostDetailViewProps) {
  // 跟帖相关状态
  const [comments, setComments] = useState<Post[]>([]);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [commentInput, setCommentInput] = useState('');
  const [commentImage, setCommentImage] = useState<File | null>(null);
  const [commentImagePreview, setCommentImagePreview] = useState<string | null>(null);
  const [showCommentImageUploader, setShowCommentImageUploader] = useState(false);
  const [likeCount, setLikeCount] = useState(Math.floor(Math.random() * 999) + 1);

  // 获取跟帖
  const fetchComments = async () => {
    setIsLoadingComments(true);
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const response = await fetch(`${API.API_PATH}/posts/${post.post.id}/comments`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) throw new Error('获取跟帖失败');

      const data = await response.json();
      const sortedComments = data.sort((a: any, b: any) => {
        return new Date(a.post.timestamp).getTime() - new Date(b.post.timestamp).getTime();
      });
      setComments(sortedComments);
    } catch (error) {
      console.error('获取跟帖失败:', error);
      toast.error('获取跟帖失败');
    } finally {
      setIsLoadingComments(false);
    }
  };

  // 提交跟帖
  const submitComment = async () => {
    if (!commentInput?.trim()) return;

    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) throw new Error('未登录，无法发表跟帖');

      let imageUrls: string[] = [];

      if (commentImage) {
        const form = new FormData();
        form.append("file", commentImage);
        form.append("name", `Comment_${post.post.id}_${Date.now()}`);
        form.append("description", "Comment attachment");
        form.append("source", post.post.id);
        form.append("type", "blog");

        const uploadResponse = await fetch(`${API.API_PATH}/diagram/upload`, {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${token}` },
          body: form
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(`图片上传失败: ${errorData.detail || '未知错误'}`);
        }

        const uploadData = await uploadResponse.json();
        if (uploadData.image_url) {
          imageUrls = [uploadData.image_url];
        }
      }

      const response = await fetch(`${API.API_PATH}/posts/${post.post.id}/retweet`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: commentInput.slice(0, 2000),
          images: imageUrls
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.detail || '服务器错误');
      }

      // 清空输入
      setCommentInput('');
      setCommentImage(null);
      setCommentImagePreview(null);
      setShowCommentImageUploader(false);

      toast.success('跟帖发送成功');
      fetchComments();
    } catch (error) {
      console.error('跟帖发送失败:', error);
      toast.error(`跟帖发送失败：${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 删除跟帖
  const handleDeleteComment = (commentId: string) => {
    toast.custom((t: any) => (
      <div className={`${t.visible ? 'animate-enter' : 'animate-leave'} max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto flex flex-col`}>
        <div className="p-4">
          <h3 className="text-sm font-medium mb-2">确认删除</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">确定要删除这条跟帖吗？此操作无法撤销。</p>
        </div>
        <div className="flex justify-end gap-2 p-4 pt-0">
          <button
            className="px-3 py-1.5 text-sm bg-gray-200 dark:bg-gray-700 rounded-md"
            onClick={() => toast.dismiss(t.id)}
          >
            取消
          </button>
          <button
            className="px-3 py-1.5 text-sm bg-red-500 text-white rounded-md"
            onClick={async () => {
              try {
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                const response = await fetch(`${API.API_PATH}/posts/${commentId}`, {
                  method: 'DELETE',
                  headers: { 'Authorization': `Bearer ${token}` }
                });

                if (!response.ok) throw new Error(`删除失败: ${response.status}`);

                fetchComments();
                toast.success('跟帖已删除');
                toast.dismiss(t.id);
              } catch (error) {
                console.error('删除跟帖失败:', error);
                toast.error('删除失败，请重试');
              }
            }}
          >
            删除
          </button>
        </div>
      </div>
    ), { duration: Infinity });
  };

  // 处理跟帖图片选择
  const handleCommentImageSelect = (file: File | null) => {
    setCommentImage(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setCommentImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setCommentImagePreview(null);
    }
    setShowCommentImageUploader(false);
  };

  // 组件挂载时加载跟帖
  useEffect(() => {
    fetchComments();
  }, [post.post.id]);

  // 使用导入的formatTimestamp函数，已修复时区问题

  return (
    <div className="flex flex-col h-full">
      {/* 主帖内容 */}
      <div className="p-4 border-b">
        {/* 帖子头部 */}
        <div className="flex items-start gap-3 mb-3">
          <Avatar className="h-10 w-10 border-1 border-orange-100">
            <AvatarImage src={post.owner?.thumbnail || "/placeholder.svg?height=40&width=40"} alt="@user" />
            <AvatarFallback>
              {post.owner?.display_name?.slice(0, 2) ||
                post.owner?.username?.slice(0, 2) ||
                post.post.owner_id.slice(0, 2)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center gap-1 mb-1">
              <span className="font-bold">
                {post.owner?.display_name ||
                  post.owner?.username ||
                  (post.post.owner_id.includes('@')
                    ? post.post.owner_id.split('@')[0]
                    : post.post.owner_id.slice(0, 8))}
              </span>
              <Badge className="bg-yellow-500 text-xs">V</Badge>

              {post.channel && (
                <ChannelBadge
                  channel={post.channel}
                  size="sm"
                  clickable={false}
                />
              )}
            </div>
            <span className="text-sm text-muted-foreground">
              {formatTimestamp(post.post.timestamp)}
            </span>
          </div>
        </div>

        {/* 帖子内容 */}
        <div className="prose prose-sm max-w-none dark:prose-invert mb-3">
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkMath]}
            rehypePlugins={[rehypeKatex]}
            components={MarkdownComponents}
          >
            {post.post.content}
          </ReactMarkdown>
        </div>

        {/* 帖子图片 */}
        {post.images && post.images.length > 0 && (
          <div className="grid grid-cols-1 gap-2 mb-3">
            {post.images.map((imageUrl, index) => {
              const convertedUrl = convertSupabaseUrl(imageUrl);
              return (
                <div key={index} className="relative group cursor-pointer flex justify-center">
                  <img
                    src={convertedUrl}
                    alt={`图片 ${index + 1}`}
                    className="w-full h-auto object-contain rounded-lg hover:opacity-90 transition-opacity"
                    onClick={() => onImageClick(convertedUrl, `图片 ${index + 1}`)}
                    onError={(e) => {
                      console.error('图片加载失败:', convertedUrl);
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg flex items-center justify-center pointer-events-none">
                    <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 帖子操作栏 */}
        <PostActions
          likeCount={likeCount}
          commentCount={comments.length}
          onLike={() => { }}
          onComment={() => { }}
          onExplore={onOpenChat ? (() => onOpenChat(post)) : undefined}
          showExploreButton={!!onOpenChat}
          disabledLike={true}
        />
      </div>

      {/* 跟帖区域 */}
      <div className="flex-1 overflow-y-auto">
        <CommentSection
          rootReplies={comments as any} // TODO: 需将 comments 转为树形结构，暂用 any 保持兼容
          isLoading={isLoadingComments}
          user={user}
          commentInput={commentInput}
          commentImage={commentImage}
          commentImagePreview={commentImagePreview}
          showImageUploader={showCommentImageUploader}
          onCommentInputChange={setCommentInput}
          onCommentImageSelect={handleCommentImageSelect}
          onImageUploaderToggle={() => setShowCommentImageUploader(!showCommentImageUploader)}
          onSubmitComment={submitComment}
          onDeleteComment={handleDeleteComment}
          onImageClick={onImageClick}
          onReplySubmit={() => { }}
        />
      </div>
    </div>
  );
}