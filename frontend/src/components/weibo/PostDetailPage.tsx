import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, CardHeader } from "@/components/ui/card"
import { ChannelBadge } from "./ChannelSelector"
import { CommentSection } from "./CommentSection"
import {
  MessageSquare,
  ThumbsUp,
  BotMessageSquare,
  Pencil,
  Trash2,
  ZoomIn,
  ArrowLeft,
  Share
} from "lucide-react"
import { useState, useEffect } from "react"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents'
import { convertSupabaseUrl } from '@/utils/url-utils'
import { formatTimestamp } from '@/utils/timeUtils'
import { toast } from "sonner"
import API from '@/config/api'
import 'katex/dist/katex.min.css'
import { PostActions } from "./PostActions"
import { RightPanel } from "@/components/ui/RightPanel"
import { useRightPanelStore } from "@/store/rightPanelStore"
import { ChatDialog } from "./ChatDialog"

interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}

interface ChannelInfo {
  id: string;
  name: string;
  display_name: string;
  color?: string;
  icon?: string;
}

interface Post {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
  };
  images: string[];
  owner?: UserInfo;
  channel?: ChannelInfo;
}

interface PostDetailPageProps {
  post: Post;
  user: { id: string, email: string, username: string } | null;
  onBack: () => void;
  onImageClick: (src: string, alt?: string) => void;
  onOpenChat?: (post: Post) => void;
}

// 新增类型
interface PostReplyTreeResponse {
  post: Post;
  replies: PostReplyTreeResponse[];
}

export function PostDetailPage({
  post,
  user,
  onBack,
  onImageClick, // 保留参数但不再使用
  onOpenChat
}: PostDetailPageProps) {
  // 跟帖相关状态
  const [comments, setComments] = useState<Post[]>([]);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [commentInput, setCommentInput] = useState('');
  const [commentImage, setCommentImage] = useState<File | null>(null);
  const [commentImagePreview, setCommentImagePreview] = useState<string | null>(null);
  const [showCommentImageUploader, setShowCommentImageUploader] = useState(false);
  const [likeCount, setLikeCount] = useState(Math.floor(Math.random() * 999) + 1);

  // 新增树形评论状态
  const [rootReplies, setRootReplies] = useState<PostReplyTreeResponse[]>([]);
  const { isOpen: isRightPanelOpen, open: openRightPanel, close: closeRightPanel } = useRightPanelStore()

  // 新增：全屏图片状态
  const [fullscreenImage, setFullscreenImage] = useState<string | null>(null);

  // 获取跟帖
  const fetchComments = async () => {
    setIsLoadingComments(true);
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const response = await fetch(`${API.API_PATH}/posts/${post.post.id}/comments`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) throw new Error('获取跟帖失败');

      const data = await response.json();
      const sortedComments = data.sort((a: any, b: any) => {
        return new Date(a.post.timestamp).getTime() - new Date(b.post.timestamp).getTime();
      });
      setComments(sortedComments);
    } catch (error) {
      console.error('获取跟帖失败:', error);
      toast.error('获取跟帖失败');
    } finally {
      setIsLoadingComments(false);
    }
  };

  // 获取树形评论
  const fetchRepliesTree = async () => {
    try {
      const response = await fetch(API.POSTS.REPLIES(post.post.id));
      if (!response.ok) throw new Error('获取评论树失败');
      const data = await response.json();
      // data 是树结构，根节点是主贴本身，replies为根评论
      setRootReplies(data.replies || []);
    } catch (error) {
      console.error('获取评论树失败:', error);
      toast.error('获取评论树失败');
    }
  };

  // 提交跟帖
  const submitComment = async () => {
    if (!commentInput?.trim()) return;

    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) throw new Error('未登录，无法发表跟帖');

      let imageUrls: string[] = [];

      if (commentImage) {
        const form = new FormData();
        form.append("file", commentImage);
        form.append("name", `Comment_${post.post.id}_${Date.now()}`);
        form.append("description", "Comment attachment");
        form.append("source", post.post.id);
        form.append("type", "blog");

        const uploadResponse = await fetch(`${API.API_PATH}/diagram/upload`, {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${token}` },
          body: form
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(`图片上传失败: ${errorData.detail || '未知错误'}`);
        }

        const uploadData = await uploadResponse.json();
        if (uploadData.image_url) {
          imageUrls = [uploadData.image_url];
        }
      }

      const response = await fetch(`${API.API_PATH}/posts/${post.post.id}/retweet`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: commentInput.slice(0, 2000),
          images: imageUrls
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.detail || '服务器错误');
      }

      // 清空输入
      setCommentInput('');
      setCommentImage(null);
      setCommentImagePreview(null);
      setShowCommentImageUploader(false);

      toast.success('跟帖发送成功');
      await Promise.all([
        fetchComments(),
        fetchRepliesTree()
      ]);
    } catch (error) {
      console.error('跟帖发送失败:', error);
      toast.error(`跟帖发送失败：${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 删除跟帖
  const handleDeleteComment = (commentId: string) => {
    toast.custom((t: any) => (
      <div className={`${t.visible ? 'animate-enter' : 'animate-leave'} max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto flex flex-col`}>
        <div className="p-4">
          <h3 className="text-sm font-medium mb-2">确认删除</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">确定要删除这条跟帖吗？此操作无法撤销。</p>
        </div>
        <div className="flex justify-end gap-2 p-4 pt-0">
          <button
            className="px-3 py-1.5 text-sm bg-gray-200 dark:bg-gray-700 rounded-md"
            onClick={() => toast.dismiss(t.id)}
          >
            取消
          </button>
          <button
            className="px-3 py-1.5 text-sm bg-red-500 text-white rounded-md"
            onClick={async () => {
              try {
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                const response = await fetch(`${API.API_PATH}/posts/${commentId}`, {
                  method: 'DELETE',
                  headers: { 'Authorization': `Bearer ${token}` }
                });

                if (!response.ok) throw new Error(`删除失败: ${response.status}`);

                fetchComments();
                toast.success('跟帖已删除');
                toast.dismiss(t.id);
              } catch (error) {
                console.error('删除跟帖失败:', error);
                toast.error('删除失败，请重试');
              }
            }}
          >
            删除
          </button>
        </div>
      </div>
    ), { duration: Infinity });
  };

  // 回复评论（支持任意层级）
  const handleReplySubmit = async (parentId: string, content: string, image?: File) => {
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) throw new Error('未登录，无法回复');
      let imageUrls: string[] = [];
      if (image) {
        const form = new FormData();
        form.append("file", image);
        form.append("name", `Reply_${parentId}_${Date.now()}`);
        form.append("description", "Reply attachment");
        form.append("source", parentId);
        form.append("type", "blog");
        const uploadResponse = await fetch(`${API.API_PATH}/diagram/upload`, {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${token}` },
          body: form
        });
        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(`图片上传失败: ${errorData.detail || '未知错误'}`);
        }
        const uploadData = await uploadResponse.json();
        if (uploadData.image_url) {
          imageUrls = [uploadData.image_url];
        }
      }
      const response = await fetch(`${API.API_PATH}/posts/${parentId}/retweet`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: content.slice(0, 2000),
          images: imageUrls
        })
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || errorData.detail || '服务器错误');
      }
      toast.success('回复成功');

      // 等待数据刷新完成
      await Promise.all([
        fetchRepliesTree(),
        fetchComments()
      ]);
    } catch (error) {
      console.error('回复失败:', error);
      toast.error(`回复失败：${error instanceof Error ? error.message : '未知错误'}`);
      throw error; // 重新抛出错误，让CommentTree知道提交失败
    }
  };

  // 处理跟帖图片选择
  const handleCommentImageSelect = (file: File | null) => {
    setCommentImage(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setCommentImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setCommentImagePreview(null);
    }
    setShowCommentImageUploader(false);
  };

  // 新增：全屏显示图片的函数
  function handleImageFullscreen(src: string) {
    const img = document.createElement('img');
    img.src = src;
    img.style.maxWidth = '100vw';
    img.style.maxHeight = '100vh';
    img.style.display = 'block';
    img.style.margin = 'auto';
    img.style.background = '#000';
    img.style.objectFit = 'contain';
    img.style.width = '100vw';
    img.style.height = '100vh';

    const wrapper = document.createElement('div');
    wrapper.style.position = 'fixed';
    wrapper.style.top = '0';
    wrapper.style.left = '0';
    wrapper.style.width = '100vw';
    wrapper.style.height = '100vh';
    wrapper.style.background = 'rgba(0,0,0,0.95)';
    wrapper.style.zIndex = '9999';
    wrapper.appendChild(img);

    // 点击任意处退出全屏
    wrapper.onclick = () => document.body.removeChild(wrapper);

    document.body.appendChild(wrapper);
  }

  // 组件挂载时加载跟帖
  useEffect(() => {
    fetchComments();
  }, [post.post.id]);

  // 组件挂载时加载树形评论
  useEffect(() => {
    fetchRepliesTree();
  }, [post.post.id]);

  // 使用导入的formatTimestamp函数，已修复时区问题

  return (
    <div className="relative space-y-4">
      {/* 全屏图片层 */}
      {fullscreenImage && (
        <div
          className="fixed inset-0 z-[99999] flex items-center justify-center bg-black/95"
          onClick={() => setFullscreenImage(null)}
          style={{ cursor: 'zoom-out' }}
        >
          <img
            src={fullscreenImage}
            alt="全屏图片"
            className="max-w-full max-h-full object-contain"
          />
        </div>
      )}
      <RightPanel>
        <ChatDialog open={isRightPanelOpen} onOpenChange={closeRightPanel} post={post} />
      </RightPanel>
      {/* 返回按钮 */}
      <div className="flex items-center gap-2 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          返回列表
        </Button>
      </div>
      {/* 主帖内容 - 红框区域 */}
      <Card className="border-0 border-b border-border rounded-none shadow-none">
        <CardHeader className="p-4 pb-0 flex flex-row items-start gap-3">
          <Avatar className="h-10 w-10 border-1 border-orange-100">
            <AvatarImage src={post.owner?.thumbnail || "/placeholder.svg?height=40&width=40"} alt="@user" />
            <AvatarFallback>
              {post.owner?.display_name?.slice(0, 2) ||
                post.owner?.username?.slice(0, 2) ||
                post.post.owner_id.slice(0, 2)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center gap-1 mb-1">
              <span className="font-bold">
                {post.owner?.display_name ||
                  post.owner?.username ||
                  (post.post.owner_id.includes('@')
                    ? post.post.owner_id.split('@')[0]
                    : post.post.owner_id.slice(0, 8))}
              </span>
              <Badge className="bg-yellow-500 text-xs">V</Badge>
              {post.channel && (
                <ChannelBadge
                  channel={post.channel}
                  size="sm"
                  clickable={false}
                />
              )}
              <span className="text-sm text-muted-foreground ml-2">
                {formatTimestamp(post.post.timestamp)}
              </span>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-4 pt-2">
          {/* 帖子内容 */}
          <div className="prose prose-sm max-w-none dark:prose-invert mb-4">
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkMath]}
              rehypePlugins={[rehypeKatex]}
              components={MarkdownComponents}
            >
              {post.post.content}
            </ReactMarkdown>
          </div>
          {/* 帖子图片 */}
          {post.images && post.images.length > 0 && (
            <div className={post.images.length === 1 ? "mb-4" : "grid grid-cols-2 gap-2 mb-4"}>
              {post.images.map((imageUrl, index) => {
                const convertedUrl = convertSupabaseUrl(imageUrl);
                return (
                  <div key={index} className={`relative group cursor-pointer ${post.images.length === 1 ? 'w-full' : 'flex justify-center'}`}>
                    <img
                      src={convertedUrl}
                      alt={`图片 ${index + 1}`}
                      className={`${post.images.length === 1 ? 'w-full max-h-96' : 'w-full'} h-auto rounded-lg hover:opacity-90 transition-opacity object-contain`}
                      onClick={() => onImageClick(convertedUrl, `图片 ${index + 1}`)}
                      onError={(e) => {
                        console.error('图片加载失败:', convertedUrl);
                        (e.target as HTMLImageElement).style.display = 'none';
                      }}
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg flex items-center justify-center pointer-events-none">
                      <ZoomIn className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
        <CardFooter className="px-4 pb-4 pt-0 flex justify-between items-center">
          <PostActions
            likeCount={likeCount}
            commentCount={comments.length}
            onLike={() => { }}
            onComment={() => { }}
            postId={post.post.id}
            showExploreButton={true}
            disabledLike={true}
          />
        </CardFooter>
      </Card>
      {/* About和板块规则组件，仅在RightPanel关闭时显示 */}
      {!isRightPanelOpen && (
        <>
          {/* About组件示例：<About />，请根据实际路径调整 */}
          {/* 板块规则组件示例：<BoardRuleSection />，请根据实际路径调整 */}
        </>
      )}
      {/* 跟帖区域 */}
      <div className="flex-1 overflow-y-auto">
        <CommentSection
          rootReplies={rootReplies}
          isLoading={isLoadingComments}
          user={user}
          commentInput={commentInput}
          commentImage={commentImage}
          commentImagePreview={commentImagePreview}
          showImageUploader={showCommentImageUploader}
          onCommentInputChange={setCommentInput}
          onCommentImageSelect={handleCommentImageSelect}
          onImageUploaderToggle={() => setShowCommentImageUploader(!showCommentImageUploader)}
          onSubmitComment={submitComment}
          onDeleteComment={handleDeleteComment}
          onImageClick={onImageClick}
          onReplySubmit={handleReplySubmit}
        />
      </div>
    </div>
  );
}