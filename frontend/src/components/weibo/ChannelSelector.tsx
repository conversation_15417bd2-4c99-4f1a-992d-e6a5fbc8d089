import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Hash, Users } from 'lucide-react';
import API from '@/config/api';

interface Channel {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  color?: string;
  icon?: string;
  post_count: number;
  member_count: number;
  is_default: boolean;
}

interface ChannelSelectorProps {
  selectedChannelId?: string;
  onChannelChange: (channelId: string | undefined) => void;
  showAllOption?: boolean;
}

export function ChannelSelector({ 
  selectedChannelId, 
  onChannelChange, 
  showAllOption = true 
}: ChannelSelectorProps) {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchChannels = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API.API_PATH}/channels/`);
      if (response.ok) {
        const data = await response.json();
        setChannels(data);
      }
    } catch (error) {
      console.error('获取板块列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchChannels();
  }, []);

  const selectedChannel = channels.find(c => c.id === selectedChannelId);

  return (
    <div className="w-full">
      <Select
        value={selectedChannelId || 'all'}
        onValueChange={(value) => onChannelChange(value === 'all' ? undefined : value)}
      >
        <SelectTrigger className="w-full">
          <SelectValue>
            <div className="flex items-center space-x-2">
              {selectedChannel ? (
                <>
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: selectedChannel.color || '#3B82F6' }}
                  />
                  <span>{selectedChannel.display_name}</span>
                  <Badge variant="secondary" className="text-xs">
                    {selectedChannel.post_count}
                  </Badge>
                </>
              ) : (
                <>
                  <Hash className="w-4 h-4" />
                  <span>所有板块</span>
                </>
              )}
            </div>
          </SelectValue>
        </SelectTrigger>
        
        <SelectContent>
          {showAllOption && (
            <SelectItem value="all">
              <div className="flex items-center space-x-2">
                <Hash className="w-4 h-4" />
                <span>所有板块</span>
              </div>
            </SelectItem>
          )}
          
          {loading ? (
            <SelectItem value="loading" disabled>
              加载中...
            </SelectItem>
          ) : (
            channels.map((channel) => (
              <SelectItem key={channel.id} value={channel.id}>
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: channel.color || '#3B82F6' }}
                    />
                    <span>{channel.display_name}</span>
                    {channel.is_default && (
                      <Badge variant="outline" className="text-xs">
                        默认
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                    <span>{channel.post_count}</span>
                    <Users className="w-3 h-3" />
                    <span>{channel.member_count}</span>
                  </div>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
    </div>
  );
}

// 板块标签组件
interface ChannelBadgeProps {
  channel: {
    id: string;
    name: string;
    display_name: string;
    color?: string;
  };
  size?: 'sm' | 'md';
  clickable?: boolean;
  onClick?: () => void;
}

export function ChannelBadge({ 
  channel, 
  size = 'sm', 
  clickable = false, 
  onClick 
}: ChannelBadgeProps) {
  const Component = clickable ? Button : 'div';
  
  return (
    <Component
      variant={clickable ? 'ghost' : undefined}
      size={clickable ? 'sm' : undefined}
      className={`
        inline-flex items-center space-x-1 rounded-full px-2 py-1 text-xs font-medium
        ${clickable ? 'hover:bg-muted cursor-pointer' : ''}
        ${size === 'sm' ? 'text-xs' : 'text-sm'}
      `}
      style={{ 
        backgroundColor: clickable ? undefined : `${channel.color || '#3B82F6'}20`,
        color: channel.color || '#3B82F6'
      }}
      onClick={onClick}
    >
      <div 
        className={`rounded-full ${size === 'sm' ? 'w-2 h-2' : 'w-3 h-3'}`}
        style={{ backgroundColor: channel.color || '#3B82F6' }}
      />
      <span>#{channel.name}</span>
    </Component>
  );
}