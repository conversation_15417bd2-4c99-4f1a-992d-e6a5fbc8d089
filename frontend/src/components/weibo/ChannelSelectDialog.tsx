import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import axios from 'axios';
import { API } from '../../config/api';

interface Channel {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  color?: string;
  is_default: boolean;
}

interface ChannelSelectDialogProps {
  isOpen: boolean;
  onClose: () => void;
  postId: string;
  currentChannelId?: string;
  onChannelChanged: () => void;
}

export function ChannelSelectDialog({
  isOpen,
  onClose,
  postId,
  currentChannelId,
  onChannelChanged
}: ChannelSelectDialogProps) {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [selectedChannelId, setSelectedChannelId] = useState<string>(currentChannelId || '');
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);

  // 获取频道列表
  const fetchChannels = async () => {
    try {
      setLoading(true);
      const response = await axios.get(API.CHANNELS.LIST, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      setChannels(response.data);
    } catch (error) {
      console.error('获取频道列表失败:', error);
      toast.error('获取频道列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchChannels();
      setSelectedChannelId(currentChannelId || '');
    }
  }, [isOpen, currentChannelId]);

  // 更新帖子的板块
  const handleUpdateChannel = async () => {
    if (!selectedChannelId) {
      toast.error('请选择一个板块');
      return;
    }

    try {
      setUpdating(true);
      await axios.patch(`${API.POSTS.LIST}/${postId}/channel`, {
        channel_id: selectedChannelId
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      toast.success('帖子板块更新成功');
      onChannelChanged();
      onClose();
    } catch (error) {
      console.error('更新帖子板块失败:', error);
      toast.error('更新帖子板块失败');
    } finally {
      setUpdating(false);
    }
  };

  const currentChannel = channels.find(c => c.id === currentChannelId);
  const selectedChannel = channels.find(c => c.id === selectedChannelId);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>修改帖子板块</DialogTitle>
          <DialogDescription>
            选择帖子应该归属的板块
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {currentChannel && (
            <div className="space-y-2">
              <label className="text-sm font-medium">当前板块:</label>
              <div className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: currentChannel.color || '#3b82f6' }}
                />
                <span className="text-sm">r/{currentChannel.name}</span>
                <Badge variant="outline">{currentChannel.display_name}</Badge>
                {currentChannel.is_default && (
                  <Badge variant="secondary" className="text-xs">默认</Badge>
                )}
              </div>
            </div>
          )}
          
          <div className="space-y-2">
            <label className="text-sm font-medium">选择新板块:</label>
            {loading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
              </div>
            ) : (
              <Select
                value={selectedChannelId}
                onValueChange={setSelectedChannelId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择板块" />
                </SelectTrigger>
                <SelectContent>
                  {channels.map((channel) => (
                    <SelectItem key={channel.id} value={channel.id}>
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: channel.color || '#3b82f6' }}
                        />
                        <span>r/{channel.name}</span>
                        <span className="text-muted-foreground">({channel.display_name})</span>
                        {channel.is_default && (
                          <Badge variant="secondary" className="text-xs ml-1">默认</Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>
          
          {selectedChannel && selectedChannel.description && (
            <div className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
              <strong>板块描述:</strong> {selectedChannel.description}
            </div>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            取消
          </Button>
          <Button 
            onClick={handleUpdateChannel}
            disabled={updating || !selectedChannelId || selectedChannelId === currentChannelId}
          >
            {updating ? '更新中...' : '确认更改'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}