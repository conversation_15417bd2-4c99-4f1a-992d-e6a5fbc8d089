import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { NotificationService } from '@/utils/notificationService';
import { NotificationType } from '@/types/notification';
import { toast } from 'sonner';

export function NotificationTest() {
  const [testData, setTestData] = useState({
    type: NotificationType.MENTION,
    postId: '',
    userId: '',
    senderId: '',
    channelId: '',
    content: '这是一条测试通知'
  });

  const handleCreateTestNotification = async () => {
    try {
      const response = await fetch('/api/notifications/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          type: testData.type,
          user_id: testData.userId,
          sender_id: testData.senderId,
          target_post_id: testData.postId || undefined,
          channel_id: testData.channelId || undefined,
          content: testData.content
        })
      });

      if (response.ok) {
        toast.success('测试通知创建成功');
      } else {
        toast.error('创建失败');
      }
    } catch (error) {
      console.error('创建测试通知失败:', error);
      toast.error('创建失败');
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>通知测试工具</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="type">通知类型</Label>
          <Select 
            value={testData.type} 
            onValueChange={(value) => setTestData(prev => ({ ...prev, type: value as NotificationType }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={NotificationType.MENTION}>@提醒</SelectItem>
              <SelectItem value={NotificationType.COMMENT}>评论通知</SelectItem>
              <SelectItem value={NotificationType.LIKE}>点赞通知</SelectItem>
              <SelectItem value={NotificationType.FOLLOW}>关注通知</SelectItem>
              <SelectItem value={NotificationType.CHANNEL_POST}>频道新帖</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="userId">接收用户ID</Label>
          <Input
            id="userId"
            value={testData.userId}
            onChange={(e) => setTestData(prev => ({ ...prev, userId: e.target.value }))}
            placeholder="接收通知的用户ID"
          />
        </div>

        <div>
          <Label htmlFor="senderId">发送者ID</Label>
          <Input
            id="senderId"
            value={testData.senderId}
            onChange={(e) => setTestData(prev => ({ ...prev, senderId: e.target.value }))}
            placeholder="发送通知的用户ID"
          />
        </div>

        <div>
          <Label htmlFor="postId">帖子ID (可选)</Label>
          <Input
            id="postId"
            value={testData.postId}
            onChange={(e) => setTestData(prev => ({ ...prev, postId: e.target.value }))}
            placeholder="相关帖子ID"
          />
        </div>

        <div>
          <Label htmlFor="channelId">频道ID (可选)</Label>
          <Input
            id="channelId"
            value={testData.channelId}
            onChange={(e) => setTestData(prev => ({ ...prev, channelId: e.target.value }))}
            placeholder="相关频道ID"
          />
        </div>

        <div>
          <Label htmlFor="content">通知内容</Label>
          <Input
            id="content"
            value={testData.content}
            onChange={(e) => setTestData(prev => ({ ...prev, content: e.target.value }))}
            placeholder="通知内容"
          />
        </div>

        <Button onClick={handleCreateTestNotification} className="w-full">
          创建测试通知
        </Button>
      </CardContent>
    </Card>
  );
}