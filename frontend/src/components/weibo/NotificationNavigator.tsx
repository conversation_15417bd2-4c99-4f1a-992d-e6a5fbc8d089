import { useLocation } from 'wouter';
import { toast } from 'sonner';
import { NotificationType, type NotificationData } from '@/types/notification';

export function useNotificationNavigator() {
  const [, setLocation] = useLocation();

  // 处理通知点击 - 跳转到相应位置
  const handleNotificationClick = async (notification: NotificationData) => {
    try {
      // 根据通知类型进行不同的跳转处理
      switch (notification.type) {
        case NotificationType.MENTION:
        case NotificationType.COMMENT:
          // @提醒和评论通知：跳转到帖子详情页
          if (notification.target_post_id) {
            await navigateToPost(notification.target_post_id, notification.source_comment_id);
          }
          break;
          
        case NotificationType.LIKE:
          // 点赞通知：跳转到被点赞的帖子
          if (notification.target_post_id) {
            await navigateToPost(notification.target_post_id);
          }
          break;
          
        case NotificationType.FOLLOW:
          // 关注通知：跳转到关注者的个人资料页
          if (notification.sender_username) {
            setLocation(`/profile/${notification.sender_username}`);
          }
          break;
          
        case NotificationType.CHANNEL_POST:
          // 订阅频道新帖通知：跳转到频道页面并高亮帖子
          if (notification.channel_id && notification.target_post_id) {
            await navigateToChannelPost(notification.channel_id, notification.target_post_id);
          }
          break;
          
        default:
          console.warn('未知的通知类型:', notification.type);
      }
    } catch (error) {
      console.error('处理通知点击失败:', error);
      toast.error('跳转失败，请稍后重试');
    }
  };

  // 跳转到帖子详情页
  const navigateToPost = async (postId: string, commentId?: string) => {
    // 跳转到微博页面，并传递帖子ID参数
    const url = `/weibo?post=${postId}${commentId ? `&comment=${commentId}` : ''}`;
    setLocation(url);
    
    // 等待页面加载后滚动到目标位置
    setTimeout(() => {
      const targetId = commentId ? `comment-${commentId}` : `post-${postId}`;
      const targetElement = document.getElementById(targetId);
      
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        // 添加高亮效果
        targetElement.classList.add('bg-orange-50', 'dark:bg-orange-900/20', 'ring-2', 'ring-orange-500', 'ring-opacity-50');
        setTimeout(() => {
          targetElement.classList.remove('bg-orange-50', 'dark:bg-orange-900/20', 'ring-2', 'ring-orange-500', 'ring-opacity-50');
        }, 3000);
      } else {
        // 如果元素不存在，触发自定义事件让页面加载特定帖子
        window.dispatchEvent(new CustomEvent('loadPost', { 
          detail: { postId, commentId } 
        }));
      }
    }, 500);
  };

  // 跳转到频道页面的特定帖子
  const navigateToChannelPost = async (channelId: string, postId: string) => {
    // 跳转到微博页面，并设置频道和帖子参数
    const url = `/weibo?channel=${channelId}&post=${postId}`;
    setLocation(url);
    
    // 等待页面加载后处理高亮
    setTimeout(() => {
      const postElement = document.getElementById(`post-${postId}`);
      if (postElement) {
        postElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        postElement.classList.add('bg-orange-50', 'dark:bg-orange-900/20', 'ring-2', 'ring-orange-500', 'ring-opacity-50');
        setTimeout(() => {
          postElement.classList.remove('bg-orange-50', 'dark:bg-orange-900/20', 'ring-2', 'ring-orange-500', 'ring-opacity-50');
        }, 3000);
      }
    }, 500);
  };

  return {
    handleNotificationClick,
    navigateToPost,
    navigateToChannelPost
  };
}