import { useState, useCallback, useEffect, useRef } from "react";
import { UploadCloud, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

interface ImageUploaderProps {
  onImageSelect: (file: File | null) => void;
  onMultipleImageSelect?: (files: File[]) => void;
  initialImage?: File | null;
  initialPreview?: string | null;
  allowMultiple?: boolean;
}

export function ImageUploader({ 
  onImageSelect, 
  onMultipleImageSelect, 
  initialImage, 
  initialPreview, 
  allowMultiple = false 
}: ImageUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [preview, setPreview] = useState<string | null>(initialPreview || null);
  const [image, setImage] = useState<File | null>(initialImage || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理拖拽事件
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  // 处理拖放事件
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files?.[0]) {
      handleFile(files[0]);
    }
  }, []);

  // 处理文件选择
  const handleFile = (file: File) => {
    if (file.type.startsWith('image/')) {
      setImage(file);
      onImageSelect(file);
      
      // 创建预览
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      toast.error("请选择图片文件");
    }
  };

  // 处理文件输入变化
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      if (allowMultiple && onMultipleImageSelect) {
        const files = Array.from(e.target.files).filter(file => file.type.startsWith('image/'));
        if (files.length > 0) {
          onMultipleImageSelect(files);
        }
      } else if (e.target.files[0]) {
        handleFile(e.target.files[0]);
      }
    }
  };

  // 处理点击上传按钮
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  // 清除已选择的图片
  const handleClearImage = () => {
    setImage(null);
    setPreview(null);
    onImageSelect(null); // 通知父组件图片已清除
    
    // 清除文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 监听粘贴事件
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      const items = e.clipboardData?.items;
      if (!items) return;

      for (const item of Array.from(items)) {
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) handleFile(file);
          break;
        }
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, []);

  return (
    <div className="w-full">
      {preview ? (
        <div className="relative">
          <div className="relative border rounded-lg overflow-hidden">
            <img 
              src={preview} 
              alt="Preview" 
              className="w-full h-auto max-h-[200px] object-contain"
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-2 right-2 bg-black/30 hover:bg-black/50 text-white rounded-full h-6 w-6 p-1"
              onClick={handleClearImage}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-2 text-center">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleUploadClick}
              className="text-xs"
            >
              更换图片
            </Button>
          </div>
        </div>
      ) : (
        <div
          className={`flex flex-col items-center justify-center w-full h-[150px] border-2 border-dashed rounded-lg cursor-pointer 
            ${isDragging 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
              : 'border-gray-300 bg-gray-50 dark:bg-gray-700'} 
            hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600
            relative overflow-hidden`}
          onDragEnter={(e) => {
            handleDrag(e);
            setIsDragging(true);
          }}
          onDragLeave={(e) => {
            handleDrag(e);
            setIsDragging(false);
          }}
          onDragOver={handleDrag}
          onDrop={handleDrop}
          onClick={handleUploadClick}
        >
          <div className="flex flex-col items-center justify-center pt-5 pb-6">
            <UploadCloud className="w-8 h-8 mb-2 text-gray-500 dark:text-gray-400" />
            <p className="mb-1 text-sm text-gray-500 dark:text-gray-400">
              <span className="font-semibold">点击上传</span> 或拖拽图片
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              支持 PNG、JPG、JPEG、GIF 格式
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              可直接粘贴剪贴板中的图片 (Ctrl+V)
            </p>
          </div>
        </div>
      )}
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        accept="image/*"
        multiple={allowMultiple}
        onChange={handleFileInputChange}
      />
    </div>
  );
}
