import { ArrowUp, Image, X } from "lucide-react"
import { PostCard } from "./PostCard"
import { ChannelSelectDialog } from "./ChannelSelectDialog"
import { toast } from "sonner"
import { useEffect, useState } from "react"
import API from '@/config/api'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>Header, <PERSON>alogTit<PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"

interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}

interface ChannelInfo {
  id: string;
  name: string;
  display_name: string;
  color?: string;
  icon?: string;
}

interface Post {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
  };
  images: string[];
  owner?: UserInfo;
  channel?: ChannelInfo;
}

interface ContentTabsProps {
  posts: Post[];
  loading: boolean;
  onPostClick?: (post: Post) => void;
  onChatOpen?: (post: Post) => void;
  onImageClick?: (src: string, alt?: string) => void;
}

export function ContentTabs({
  posts: initialPosts = [],
  loading = false,
  onPostClick,
  onChatOpen,
  onImageClick
}: ContentTabsProps) {
  // 基础状态
  const [posts, setPosts] = useState(initialPosts);
  const [user, setUser] = useState<{ id: string, email: string, username: string, role?: string } | null>(null);
  const [showBackToTop, setShowBackToTop] = useState(false);

  // 帖子相关状态
  const [likeCounts, setLikeCounts] = useState<Record<string, number>>({});
  const [commentCounts] = useState<Record<string, number>>({});

  // Channel选择对话框状态
  const [channelSelectDialog, setChannelSelectDialog] = useState<{
    isOpen: boolean;
    postId: string;
    currentChannelId?: string;
  }>({
    isOpen: false,
    postId: '',
    currentChannelId: undefined
  });

  // 初始化用户信息
  useEffect(() => {
    const userJson = localStorage.getItem('user');
    if (userJson) {
      try {
        const userData = JSON.parse(userJson);
        setUser(userData);
      } catch (error) {
        console.error('解析用户数据失败:', error);
      }
    }
  }, []);

  // 更新帖子和点赞数
  useEffect(() => {
    setPosts(initialPosts);
    const newLikeCounts: Record<string, number> = {};
    initialPosts.forEach(post => {
      newLikeCounts[post.post.id] = Math.floor(Math.random() * 999) + 1;
    });
    setLikeCounts(newLikeCounts);
  }, [initialPosts]);

  // 滚动监听
  useEffect(() => {
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement;
      setShowBackToTop(target.scrollTop > 300);
    };

    // 查找滚动容器 - 优先查找RedditStyleLayout内的滚动容器
    const scrollContainer = document.querySelector('.flex-1.overflow-y-auto') ||
      document.querySelector('.overflow-y-auto') ||
      document.querySelector('[class*="overflow-y-auto"]');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => scrollContainer.removeEventListener('scroll', handleScroll);
    }
  }, []);

  // 回到顶部函数
  const scrollToTop = () => {
    const scrollContainer = document.querySelector('.flex-1.overflow-y-auto') ||
      document.querySelector('.overflow-y-auto') ||
      document.querySelector('[class*="overflow-y-auto"]');
    if (scrollContainer) {
      scrollContainer.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // 编辑帖子状态
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingPost, setEditingPost] = useState({
    id: '',
    content: '',
    originalImages: [] as string[] // 原始图片URLs
  });
  const [currentImages, setCurrentImages] = useState<string[]>([]); // 当前显示的图片URLs
  const [newImages, setNewImages] = useState<File[]>([]); // 新添加的图片文件
  const [deletedImages, setDeletedImages] = useState<string[]>([]); // 被删除的图片URLs
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 转换图片URL为浏览器可访问的地址
  const convertImageUrl = (url: string) => {
    if (!url) return url;

    // 将 host.docker.internal:54321 替换为 localhost:54321
    // 并清理末尾的多余字符
    return url
      .replace('host.docker.internal:54321', 'localhost:54321')
      .replace(/\?$/, ''); // 移除末尾的 ?
  };

  // 将浏览器URL转换回服务器存储的格式
  const convertUrlToServerFormat = (url: string) => {
    if (!url) return url;
  
    // 如果是代理URL，转换回完整的Supabase URL
    if (url.startsWith('/storage-proxy')) {
      return url.replace('/storage-proxy', 'http://host.docker.internal:54321/storage');
    }
    
    // 如果是localhost URL，转换为host.docker.internal
    if (url.includes('localhost:54321')) {
      return url.replace('localhost:54321', 'host.docker.internal:54321');
    }
    
    // 如果是127.0.0.1 URL，转换为host.docker.internal
    if (url.includes('127.0.0.1:54321')) {
      return url.replace('127.0.0.1:54321', 'host.docker.internal:54321');
    }
    
    return url;
  };



  // 编辑帖子
  const handleEditPost = (postId: string, content: string) => {
    const post = posts.find(p => p.post.id === postId);
    const originalImages = post?.images || [];

    // 转换图片URL
    const convertedImages = originalImages.map(convertImageUrl);



    setEditingPost({
      id: postId,
      content: content,
      originalImages: convertedImages
    });

    // 重置编辑图片状态
    setCurrentImages([...convertedImages]);
    setNewImages([]);
    setDeletedImages([]);
    setEditDialogOpen(true);
  };







  // 处理编辑时的图片选择
  const handleEditImageSelect = (file: File | null) => {
    if (!file) return;

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      toast.error('请选择图片文件');
      return;
    }

    // 检查文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('图片大小不能超过 10MB');
      return;
    }

    // 检查图片数量限制
    const totalImages = currentImages.length + newImages.length;
    if (totalImages >= 9) {
      toast.error('最多只能上传 9 张图片');
      return;
    }

    // 添加新图片文件
    setNewImages(prev => [...prev, file]);

    // 创建预览URL并添加到当前图片列表
    const reader = new FileReader();
    reader.onloadend = () => {
      setCurrentImages(prev => [...prev, reader.result as string]);
    };
    reader.readAsDataURL(file);
  };

  // 移除图片
  const removeImage = (index: number) => {
    const imageUrl = currentImages[index];

    // 判断是原始图片还是新添加的图片
    const isOriginalImage = editingPost.originalImages.includes(imageUrl);

    if (isOriginalImage) {
      // 如果是原始图片，添加到删除列表
      setDeletedImages(prev => [...prev, imageUrl]);
    } else {
      // 如果是新添加的图片，从新图片列表中移除
      const newImageIndex = currentImages.slice(editingPost.originalImages.length - deletedImages.length).indexOf(imageUrl);
      if (newImageIndex >= 0) {
        setNewImages(prev => prev.filter((_, i) => i !== newImageIndex));
      }
    }

    // 从当前显示列表中移除
    setCurrentImages(prev => prev.filter((_, i) => i !== index));
  };

  // 处理粘贴事件
  const handlePaste = (e: ClipboardEvent) => {
    if (!editDialogOpen) return;

    const items = e.clipboardData?.items;
    if (!items) return;

    for (const item of Array.from(items)) {
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file) {
          handleEditImageSelect(file);
          e.preventDefault();
        }
        break;
      }
    }
  };

  // 监听粘贴事件
  useEffect(() => {
    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, [editDialogOpen]);

  // 删除图片
  const deleteImage = async (imageUrl: string) => {
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) {
        throw new Error('未登录，无法删除图片');
      }

      // 将URL转换回服务器存储的格式
      const serverUrl = convertUrlToServerFormat(imageUrl);
      const apiUrl = `${API.API_PATH}/posts/${editingPost.id}/images`;

      // 发送删除请求
      const response = await fetch(apiUrl, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ image_url: serverUrl })
      });

      if (!response.ok) {
        let errorMessage = `删除图片失败: ${response.status} ${response.statusText}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorMessage;
        } catch (jsonError) {
          // 无法解析错误响应
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
    } catch (error) {
      console.error('删除图片失败:', error);
      throw error;
    }
  };

  // 上传新图片
  const uploadNewImages = async () => {
    if (newImages.length === 0) return [];

    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const formData = new FormData();

      newImages.forEach(image => {
        formData.append('images', image);
      });

      const response = await fetch(`${API.API_PATH}/posts/${editingPost.id}/images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `上传图片失败: ${response.status}`);
      }

      const result = await response.json();
      return result.image_urls || [];
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  };



  // 保存编辑后的帖子
  const handleSaveEdit = async () => {
    if (!editingPost.content.trim()) {
      toast.error('内容不能为空');
      return;
    }

    if (editingPost.content.length > 5000) {
      toast.error('内容不能超过 5000 字符');
      return;
    }

    setIsSubmitting(true);
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');

      // 1. 先删除被标记删除的图片
      for (const imageUrl of deletedImages) {
        await deleteImage(imageUrl);
      }

      // 2. 上传新图片
      const newImageUrls = await uploadNewImages();

      // 3. 更新帖子内容
      const response = await fetch(`${API.API_PATH}/posts/${editingPost.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'text/plain'
        },
        body: editingPost.content.trim()
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API错误响应:', errorData);
        throw new Error(`更新失败: ${response.status}`);
      }

      // 4. 计算最终的图片列表
      const finalImages = [
        ...editingPost.originalImages.filter(img => !deletedImages.includes(img)),
        ...newImageUrls
      ];

      // 5. 更新本地状态
      setPosts(prev => prev.map(post =>
        post.post.id === editingPost.id
          ? {
            ...post,
            post: { ...post.post, content: editingPost.content },
            images: finalImages
          }
          : post
      ));

      toast.success('微博已更新');
      setEditDialogOpen(false);
    } catch (error) {
      console.error('更新微博失败:', error);
      toast.error(`更新失败：${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 删除帖子
  const handleDeletePost = (postId: string) => {
    toast.custom((t: any) => (
      <div className={`${t.visible ? 'animate-enter' : 'animate-leave'} max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto flex flex-col`}>
        <div className="p-4">
          <h3 className="text-sm font-medium mb-2">确认删除</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">确定要删除这条微博吗？此操作无法撤销。</p>
        </div>
        <div className="flex justify-end gap-2 p-4 pt-0">
          <button
            className="px-3 py-1.5 text-sm bg-gray-200 dark:bg-gray-700 rounded-md"
            onClick={() => toast.dismiss(t.id)}
          >
            取消
          </button>
          <button
            className="px-3 py-1.5 text-sm bg-red-500 text-white rounded-md"
            onClick={async () => {
              try {
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                const response = await fetch(`${API.API_PATH}/posts/${postId}`, {
                  method: 'DELETE',
                  headers: { 'Authorization': `Bearer ${token}` }
                });

                if (!response.ok) throw new Error(`删除失败: ${response.status}`);

                setPosts(prev => prev.filter(item => item.post.id !== postId));
                toast.success('微博已删除');
                toast.dismiss(t.id);
              } catch (error) {
                console.error('删除微博失败:', error);
                toast.error('删除失败，请重试');
              }
            }}
          >
            删除
          </button>
        </div>
      </div>
    ), { duration: Infinity });
  };

  // 处理修改帖子板块
  const handleChannelChange = (postId: string, currentChannelId?: string) => {
    setChannelSelectDialog({
      isOpen: true,
      postId,
      currentChannelId
    });
  };

  // 关闭Channel选择对话框
  const handleCloseChannelDialog = () => {
    setChannelSelectDialog({
      isOpen: false,
      postId: '',
      currentChannelId: undefined
    });
  };

  // 处理Channel变更完成
  const handleChannelChanged = () => {
    // 重新获取帖子数据或者更新本地状态
    // 这里可以触发父组件重新获取数据
    toast.success('帖子板块已更新');
    // 可以在这里触发数据刷新
    window.location.reload(); // 简单的刷新方式，实际项目中可以更优雅地处理
  };



  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (posts.length === 0) {
    return <div className="text-center py-8 text-muted-foreground">暂无微博内容</div>;
  }

  return (
    <>
      {/* 回到顶部按钮 */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-orange-500 hover:bg-orange-600 text-white rounded-full shadow-lg transition-all duration-300 z-50"
          aria-label="回到顶部"
        >
          <ArrowUp className="h-5 w-5" />
        </button>
      )}

      {/* 帖子列表 */}
      <div className="space-y-4 overflow-y-auto hide-scrollbar">
        {posts.map((post) => (
          <PostCard
            key={post.post.id}
            post={post}
            user={user}
            likeCount={likeCounts[post.post.id] || 0}
            commentCount={commentCounts[post.post.id] || 0}
            onEdit={handleEditPost}
            onDelete={handleDeletePost}
            onImageClick={onImageClick || (() => { })}
            onPostClick={onPostClick || (() => { })}
            onChatOpen={onChatOpen || (() => { })}
            onChannelChange={handleChannelChange}
          />
        ))}
      </div>

      {/* 编辑微博对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>编辑微博</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* 内容编辑 */}
            <div>
              <Textarea
                value={editingPost.content}
                onChange={(e) => setEditingPost({ ...editingPost, content: e.target.value })}
                placeholder="编辑微博内容..."
                className="min-h-[120px]"
              />
              <div className="text-right text-sm text-muted-foreground mt-1">
                {editingPost.content.length}/5000
              </div>
            </div>

            {/* 图片编辑 */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Image className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">图片</span>
                <span className="text-xs text-muted-foreground">
                  ({currentImages.length}/9)
                </span>
                {(deletedImages.length > 0 || newImages.length > 0) && (
                  <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {deletedImages.length > 0 && `删除${deletedImages.length}张`}
                    {deletedImages.length > 0 && newImages.length > 0 && ' · '}
                    {newImages.length > 0 && `新增${newImages.length}张`}
                  </span>
                )}
              </div>

              {/* 图片预览网格 */}
              {currentImages.length > 0 && (
                <div className="grid grid-cols-3 gap-2 mb-3">
                  {currentImages.map((imageUrl, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={imageUrl}
                        alt={`图片 ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg border"
                        onClick={() => onImageClick?.(imageUrl, `图片 ${index + 1}`)}

                      />
                      <button
                        onClick={() => removeImage(index)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                        disabled={isSubmitting}
                      >
                        <X className="h-3 w-3" />
                      </button>
                      {/* 标记新图片 */}
                      {!editingPost.originalImages.includes(imageUrl) && (
                        <div className="absolute top-1 left-1 bg-green-500 text-white text-xs px-1 rounded">
                          新
                        </div>
                      )}
                    </div>
                  ))}

                  {/* 添加图片按钮 */}
                  {currentImages.length < 9 && (
                    <div className="relative">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleEditImageSelect(file);
                          }
                          e.target.value = '';
                        }}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        disabled={isSubmitting}
                      />
                      <div className="w-full h-24 border-2 border-dashed border-muted-foreground/30 rounded-lg flex items-center justify-center hover:border-primary/50 hover:bg-muted/30 transition-colors cursor-pointer">
                        <div className="text-center">
                          <Image className="h-6 w-6 mx-auto mb-1 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">添加图片</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 初始添加图片按钮 */}
              {currentImages.length === 0 && (
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        handleEditImageSelect(file);
                      }
                      e.target.value = '';
                    }}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    disabled={isSubmitting}
                  />
                  <div className="w-full h-32 border-2 border-dashed border-muted-foreground/30 rounded-lg flex items-center justify-center hover:border-primary/50 hover:bg-muted/30 transition-colors cursor-pointer">
                    <div className="text-center">
                      <Image className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">点击添加图片</span>
                      <div className="text-xs text-muted-foreground mt-1">
                        支持 PNG、JPG、JPEG、GIF 格式，最多9张
                      </div>
                      <div className="text-xs text-blue-600 mt-1">
                        也可以直接粘贴图片 (Ctrl+V)
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 操作提示 */}
              {currentImages.length > 0 && (
                <div className="text-xs text-muted-foreground mt-2 space-y-1">
                  <div>• 点击图片右上角的 ✕ 删除图片</div>
                  <div>• 支持直接粘贴图片 (Ctrl+V)</div>
                  <div>• 绿色"新"标记表示新添加的图片</div>
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              取消
            </Button>

            <Button
              onClick={handleSaveEdit}
              disabled={isSubmitting || editingPost.content.length > 5000}
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {deletedImages.length > 0 || newImages.length > 0
                    ? `保存中 (${deletedImages.length > 0 ? `删除${deletedImages.length}张` : ''}${deletedImages.length > 0 && newImages.length > 0 ? '，' : ''}${newImages.length > 0 ? `上传${newImages.length}张` : ''})`
                    : '保存中...'
                  }
                </div>
              ) : (
                '保存'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Channel选择对话框 */}
      <ChannelSelectDialog
        isOpen={channelSelectDialog.isOpen}
        onClose={handleCloseChannelDialog}
        postId={channelSelectDialog.postId}
        currentChannelId={channelSelectDialog.currentChannelId}
        onChannelChanged={handleChannelChanged}
      />
    </>
  );
}