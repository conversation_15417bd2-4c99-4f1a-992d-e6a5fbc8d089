import { Plus, Bell, MoreHorizontal } from 'lucide-react'
import { Button } from '@/components/ui/button'
import EseaPng from '../../assets/esea.png'

interface Channel {
  id: string
  name: string
  description: string
  member_count: number
  post_count: number
  is_member: boolean
  avatar_url?: string
  banner_url?: string
  created_at: string
  moderators?: string[]
  // 新增：自定义样式属性
  // 修改custom_style接口
  custom_style?: {
    background_color?: string
    icon_url?: string
    icon_emoji?: string // 新增emoji字段
    text_color?: string
  }
}

interface ChannelBannerProps {
  channel: Channel
  isJoining: boolean
  onCreatePost?: () => void
  onJoinChannel: () => void
}

// 修改组件中的图标显示逻辑
export function ChannelBanner({ 
  channel, 
  isJoining, 
  onCreatePost, 
  onJoinChannel 
}: ChannelBannerProps) {
  // 获取自定义样式
  const customStyle = channel.custom_style || {}
  const backgroundColor = customStyle.background_color || channel.color || '#f8fafc'
  const iconUrl = customStyle.icon_url
  const iconEmoji = customStyle.icon_emoji
  const textColor = customStyle.text_color || '#1f2937'

  return (
    <div className="relative flex-shrink-0">
      {/* 上半部分：横幅背景 */}
      {channel.banner_url ? (
        <div
          className="h-32 bg-cover bg-center rounded-t-lg"
          style={{ backgroundImage: `url(${channel.banner_url})` }}
        />
      ) : (
        <div 
          className="h-32 rounded-t-lg"
          style={{ backgroundColor }}
        />
      )}
      
      {/* 下半部分：空白栏 */}
      <div className="h-16 bg-card rounded-b-lg" />
      
      {/* 头像和名称区 */}
      <div className="absolute left-8 bottom-8 flex items-center space-x-4">
        {iconEmoji ? (
          // 显示emoji图标
          <div className="h-20 w-20 rounded-full border-4 border-muted shadow-lg bg-white flex items-center justify-center text-5xl">
            {iconEmoji}
          </div>
        ) : iconUrl ? (
          // 显示图片图标
          <img
            src={iconUrl}
            alt={channel.name}
            className="h-20 w-20 rounded-full border-4 border-muted shadow-lg bg-white"
          />
        ) : (
          // 默认图标
          <img
            src={EseaPng}
            alt="default avatar"
            className="h-20 w-20 rounded-full border-4 border-muted shadow-lg bg-white"
          />
        )}
        
        {/* 频道名称和描述 */}
        <div className="flex flex-col justify-center min-w-0">
          <h1 
            className="text-2xl font-bold truncate"
            style={{ color: textColor }}
          >
            r/{channel.name}
          </h1>
          <p className="text-muted-foreground text-sm truncate max-w-xs">
            {channel.description}
          </p>
        </div>
      </div>
      
      {/* 横幅右侧按钮区 */}
      <div className="absolute right-8 bottom-4 flex items-center space-x-2">
        {/* 只有在非热门页面且有onCreatePost回调时显示新建帖子按钮 */}
        {onCreatePost && channel.id !== 'hot' && (
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={onCreatePost}
          >
            <Plus className="h-4 w-4" />
            新建帖子
          </Button>
        )}
        
        {/* 只有在非热门页面时显示加入按钮 */}
        {channel.id !== 'hot' && (
          <Button
            onClick={onJoinChannel}
            disabled={isJoining}
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
          >
            {isJoining ? (
              "处理中..."
            ) : channel.is_member ? (
              <span className="px-3 py-1 rounded text-white bg-green-500">已加入</span>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-1" />
                加入
              </>
            )}
          </Button>
        )}
        
        {/* 只有在非热门页面时显示通知和更多按钮 */}
        {channel.id !== 'hot' && (
          <>
            <Button variant="outline" size="sm">
              <Bell className="h-4 w-4" />
            </Button>
            
            <Button variant="outline" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </>
        )}
      </div>
    </div>
  )
}