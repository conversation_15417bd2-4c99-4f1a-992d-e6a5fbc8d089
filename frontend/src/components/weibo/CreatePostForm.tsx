import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { MentionInput } from "./MentionInput"
import { ImageUploader } from "./ImageUploader"
import { ChannelSelector } from "./ChannelSelector"
import {
  SmilePlus,
  Image,
  Send,
  X,
  Hash
} from "lucide-react"
import { useState, useEffect } from "react"
import { toast } from "sonner"
import API from '@/config/api'

interface CreatePostFormProps {
  selectedChannelId?: string;
  onPostSuccess?: () => void;
  onCancel?: () => void;
}

export function CreatePostForm({
  selectedChannelId,
  onPostSuccess,
  onCancel
}: CreatePostFormProps) {
  const [content, setContent] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [showImageUploader, setShowImageUploader] = useState(false);
  const [channelId, setChannelId] = useState<string | undefined>(selectedChannelId);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string>('');
  const [isDragging, setIsDragging] = useState(false);
  const [user, setUser] = useState<{ id: string, email: string, username: string, thumbnail?: string } | null>(null);

  // 获取用户信息
  useEffect(() => {
    const userJson = localStorage.getItem('user');
    if (userJson) {
      try {
        const userData = JSON.parse(userJson);
        setUser(userData);
      } catch (error) {
        // console.error('解析用户数据失败:', error);
      }
    }
  }, []);

  // 处理拖拽事件
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    files.forEach(file => {
      if (file.type.startsWith('image/') && selectedImages.length < 9) {
        handleImageSelect(file);
      }
    });

    if (files.length > 0) {
      toast.success(`已添加 ${Math.min(files.length, 9 - selectedImages.length)} 张图片`);
    }
  };

  // 处理粘贴事件
  useEffect(() => {
    const handlePaste = (e: ClipboardEvent) => {
      // 检查是否在输入框中粘贴
      const target = e.target as HTMLElement;
      if (target.tagName === 'TEXTAREA' || target.tagName === 'INPUT') {
        return; // 让文本输入框正常处理粘贴
      }

      const items = e.clipboardData?.items;
      if (!items) return;

      for (const item of Array.from(items)) {
        if (item.type.indexOf('image') !== -1) {
          const file = item.getAsFile();
          if (file) {
            handleImageSelect(file);
            e.preventDefault();
            toast.success('已粘贴图片');
          }
          break;
        }
      }
    };

    document.addEventListener('paste', handlePaste);
    return () => document.removeEventListener('paste', handlePaste);
  }, [selectedImages.length]); // 依赖selectedImages.length以便在图片数量变化时重新绑定

  // 重置表单
  useEffect(() => {
    setContent('');
    setSelectedImages([]);
    setImagePreviews([]);
    setShowImageUploader(false);
    setChannelId(selectedChannelId);
  }, [selectedChannelId]);

  // 处理图片选择
  const handleImageSelect = (file: File | null) => {
    if (!file) return;

    // 检查文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('图片大小不能超过 10MB');
      return;
    }

    // 检查图片数量限制
    if (selectedImages.length >= 9) {
      toast.error('最多只能上传 9 张图片');
      return;
    }

    setSelectedImages(prev => [...prev, file]);

    // 创建预览
    const reader = new FileReader();
    reader.onloadend = () => {
      setImagePreviews(prev => [...prev, reader.result as string]);
    };
    reader.readAsDataURL(file);

    setShowImageUploader(false);
  };

  // 移除图片
  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  // 提交帖子
  const handleSubmit = async () => {
    if (!content.trim()) {
      toast.error('请输入帖子内容');
      return;
    }

    if (content.length > 5000) {
      toast.error('帖子内容不能超过 5000 字符');
      return;
    }

    setIsSubmitting(true);

    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) {
        throw new Error('未登录，无法发布帖子');
      }

      // 发布帖子 - 直接发送图片文件，让后端处理上传
      const formData = new FormData();
      formData.append('content', content.trim());
      if (channelId) formData.append('channel_id', channelId);

      // 添加图片文件
      // 在第178-189行附近，优化图片验证逻辑
      if (selectedImages.length > 0) {
        // console.log(`准备上传 ${selectedImages.length} 张图片`);
        setUploadProgress(`正在处理 ${selectedImages.length} 张图片...`);
      
        // 验证每个图片文件的完整性
        for (let index = 0; index < selectedImages.length; index++) {
          const img = selectedImages[index];
          // console.log(`添加图片 ${index + 1}: ${img.name}, 大小: ${img.size} bytes, 类型: ${img.type}`);
      
          // 验证文件大小
          if (img.size === 0) {
            throw new Error(`图片 ${img.name} 文件大小为0，请重新选择`);
          }
      
          // 验证文件大小限制（10MB）
          if (img.size > 10 * 1024 * 1024) {
            throw new Error(`图片 ${img.name} 文件大小超过10MB限制`);
          }
      
          // 验证文件类型
          if (!img.type.startsWith('image/')) {
            throw new Error(`文件 ${img.name} 不是有效的图片格式`);
          }
      
          // 验证文件是否损坏（尝试创建URL）
          try {
            const url = URL.createObjectURL(img);
            URL.revokeObjectURL(url); // 立即释放
          } catch (urlError) {
            throw new Error(`图片 ${img.name} 文件可能已损坏`);
          }
      
          formData.append('images', img);
        }
        
        // 根据图片数量动态调整进度提示
        if (selectedImages.length > 3) {
          setUploadProgress(`正在上传 ${selectedImages.length} 张图片，请耐心等待...`);
        } else {
          setUploadProgress(`正在上传 ${selectedImages.length} 张图片...`);
        }
      }
      
      // 增加超时时间，根据图片数量动态调整
      const controller = new AbortController();
      const baseTimeout = 30000; // 基础30秒
      const perImageTimeout = 20000; // 每张图片额外20秒
      const timeoutDuration = selectedImages.length > 0 
        ? baseTimeout + (selectedImages.length * perImageTimeout)
        : baseTimeout;
      
      const timeoutId = setTimeout(() => {
        // console.log('请求超时，正在取消...');
        controller.abort();
      }, timeoutDuration);

      // console.log(`发送请求到: ${API.API_PATH}/posts/`);
      // console.log(`超时时间: ${timeoutDuration / 1000}秒`);

      const response = await fetch(`${API.API_PATH}/posts/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      // console.log(`收到响应，状态码: ${response.status}`);

      if (!response.ok) {
        let errorMessage = '发布失败';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.detail || '发布失败';
        } catch (jsonError) {
          // 如果响应不是JSON格式，使用状态码信息
          errorMessage = `发布失败 (${response.status}: ${response.statusText})`;
        }
        throw new Error(errorMessage);
      }

      // 尝试解析成功响应
      let responseData;
      try {
        responseData = await response.json();
      } catch (jsonError) {
        // 如果响应不是JSON但状态码是成功的，认为发布成功
        // console.warn('响应不是有效的JSON，但状态码表示成功');
      }

      toast.success('帖子发布成功！');

      // 重置表单
      setContent('');
      setSelectedImages([]);
      setImagePreviews([]);
      setShowImageUploader(false);
      setUploadProgress('');

      if (onPostSuccess) {
        onPostSuccess();
      }

    } catch (error) {
      // console.error('发布帖子失败:', error);
      if (error instanceof Error && error.name === 'AbortError') {
        toast.error('上传超时，请检查网络连接或减少图片数量');
      } else {
        toast.error(`发布失败：${error instanceof Error ? error.message : '未知错误'}`);
      }
    } finally {
      setIsSubmitting(false);
      setUploadProgress('');
    }
  };

  return (
    <div
      className={`p-4 space-y-4 relative ${isDragging ? 'bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg' : ''}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* 拖拽提示覆盖层 */}
      {isDragging && (
        <div className="absolute inset-0 bg-blue-100/80 rounded-lg flex items-center justify-center z-10">
          <div className="text-center">
            <Image className="h-12 w-12 mx-auto mb-2 text-blue-600" />
            <p className="text-lg font-medium text-blue-800">释放以添加图片</p>
            <p className="text-sm text-blue-600">最多可添加 {9 - selectedImages.length} 张图片</p>
          </div>
        </div>
      )}
      {/* 用户信息 */}
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={user?.thumbnail || "/placeholder.svg?height=40&width=40"} />
          <AvatarFallback>
            {user?.username?.slice(0, 2) || user?.email?.slice(0, 2) || 'U'}
          </AvatarFallback>
        </Avatar>
        <div>
          <div className="font-medium">
            {user?.username || user?.email || '用户'}
          </div>
          <div className="text-sm text-muted-foreground">
            发布到 {channelId ? '选定板块' : '全站'}
          </div>
        </div>
      </div>

      {/* 板块选择 */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <Hash className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">选择板块</span>
        </div>
        <ChannelSelector
          selectedChannelId={channelId}
          onChannelChange={setChannelId}
          showAllOption={true}
        />
      </div>

      {/* 内容输入 */}
      <div>
        <MentionInput
          value={content}
          onChange={setContent}
          placeholder="分享你的想法..."
          className="min-h-[200px] resize-none border-0 focus-visible:ring-0 text-base"
        />
        <div className="text-right text-sm text-muted-foreground mt-1">
          {content.length}/5000
        </div>
      </div>

      {/* 图片预览 */}
      {imagePreviews.length > 0 && (
        <div>
          <div className="grid grid-cols-3 gap-2">
            {imagePreviews.map((preview, index) => (
              <div key={index} className="relative group">
                <img
                  src={preview}
                  alt={`预览 ${index + 1}`}
                  className="w-full h-24 object-cover rounded-lg border"
                />
                <button
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                  disabled={isSubmitting}
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}

            {/* 添加图片按钮 - 只在未达到最大数量时显示 */}
            {selectedImages.length < 9 && (
              <div className="relative">
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => {
                    const files = Array.from(e.target.files || []);
                    files.forEach(file => {
                      if (selectedImages.length < 9) {
                        handleImageSelect(file);
                      }
                    });
                    // 重置input值，允许选择同一文件
                    e.target.value = '';
                  }}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  disabled={isSubmitting}
                />
                <div className="w-full h-24 border-2 border-dashed border-muted-foreground/30 rounded-lg flex items-center justify-center hover:border-primary/50 hover:bg-muted/30 transition-colors cursor-pointer">
                  <div className="text-center">
                    <Image className="h-6 w-6 mx-auto mb-1 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">添加图片</span>
                    <div className="text-xs text-blue-600 mt-1">Ctrl+V粘贴</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
            <span>支持拖拽、点击选择或直接粘贴图片 (Ctrl+V)</span>
            <span>{selectedImages.length}/9 张图片</span>
          </div>
        </div>
      )}

      {/* 初始图片上传 - 只在没有图片时显示 */}
      {imagePreviews.length === 0 && showImageUploader && (
        <div className="border rounded-lg p-3 bg-muted/30">
          <div className="relative">
            <input
              type="file"
              accept="image/*"
              multiple
              onChange={(e) => {
                const files = Array.from(e.target.files || []);
                files.forEach(file => {
                  if (selectedImages.length < 9) {
                    handleImageSelect(file);
                  }
                });
                e.target.value = '';
              }}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={isSubmitting}
            />
            <div className="w-full h-32 border-2 border-dashed border-muted-foreground/30 rounded-lg flex items-center justify-center hover:border-primary/50 hover:bg-muted/30 transition-colors cursor-pointer">
              <div className="text-center">
                <Image className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground mb-1">
                  <span className="font-semibold">点击上传</span> 或拖拽图片到此处
                </p>
                <p className="text-xs text-muted-foreground">
                  支持 PNG、JPG、JPEG、GIF 格式，最多9张
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  也可以直接粘贴图片 (Ctrl+V)
                </p>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* 操作栏 */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              if (selectedImages.length === 0) {
                setShowImageUploader(!showImageUploader);
              } else {
                // 如果已有图片，触发文件选择
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.onchange = (e) => {
                  const file = (e.target as HTMLInputElement).files?.[0];
                  if (file) {
                    handleImageSelect(file);
                  }
                };
                input.click();
              }
            }}
            disabled={isSubmitting || selectedImages.length >= 9}
            className="text-muted-foreground hover:text-primary"
          >
            <Image className="h-4 w-4 mr-1" />
            图片 ({selectedImages.length}/9)
          </Button>
          <Button
            variant="ghost"
            size="sm"
            disabled={isSubmitting}
            className="text-muted-foreground hover:text-primary"
          >
            <SmilePlus className="h-4 w-4 mr-1" />
            表情
          </Button>
        </div>
        <div className="flex gap-2">
          {onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              取消
            </Button>
          )}
          {/* 调试按钮 - 仅在开发环境显示 */}
          {process.env.NODE_ENV === 'development' && selectedImages.length > 0 && (
            <Button
              variant="outline"
              onClick={async () => {
                const formData = new FormData();
                selectedImages.forEach(img => formData.append('images', img));

                try {
                  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                  const response = await fetch(`${API.API_PATH}/posts/test-upload`, {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${token}` },
                    body: formData
                  });

                  const result = await response.json();
                  // console.log('测试上传结果:', result);
                  toast.success('测试上传完成，查看控制台');
                } catch (error) {
                  // console.error('测试上传失败:', error);
                  toast.error('测试上传失败');
                }
              }}
              disabled={isSubmitting}
            >
              测试上传
            </Button>
          )}
          <Button
            onClick={handleSubmit}
            disabled={!content.trim() || isSubmitting || content.length > 5000}
            className="min-w-[80px]"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                {uploadProgress || (selectedImages.length > 0 ? `上传中 (${selectedImages.length}张图片)` : '发布中')}
              </div>
            ) : (
              <>
                <Send className="h-4 w-4 mr-1" />
                发布
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}