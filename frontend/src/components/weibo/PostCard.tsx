import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card"
import { ChannelBadge } from "./ChannelSelector"
import { LikesUsersDialog } from "./LikesUsersDialog"
import {
  MessageSquare,
  ThumbsUp,
  BotMessageSquare,
  ChevronDown,
  ChevronUp,
  Pencil,
  Trash2,
  ZoomIn,
  Heart
} from "lucide-react"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents'
import { convertSupabaseUrl } from '@/utils/url-utils'
import { formatTimestamp } from '@/utils/timeUtils'
import { useState, useEffect } from 'react'
import API from '@/config/api'
import { toast } from 'sonner'
import 'katex/dist/katex.min.css'
import React from 'react'
import { PostActions } from "./PostActions"

interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}

interface ChannelInfo {
  id: string;
  name: string;
  display_name: string;
  color?: string;
  icon?: string;
}

interface Post {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
  };
  images: string[];
  owner?: UserInfo;
  channel?: ChannelInfo;
}

interface PostCardProps {
  post: Post;
  user: { id: string, email: string, username: string, role?: string } | null;
  likeCount: number;
  commentCount?: number;
  onEdit: (postId: string, content: string) => void;
  onDelete: (postId: string) => void;
  onImageClick: (src: string, alt?: string) => void;
  onPostClick: (post: Post) => void;
  onChatOpen: (post: Post) => void;
  showExploreButton?: boolean;
  onLikeCountChange?: (postId: string, newCount: number) => void;
  onChannelChange?: (postId: string, currentChannelId?: string) => void;
}

export function PostCard({
  post,
  user,
  likeCount: initialLikeCount,
  commentCount = 0,
  onEdit,
  onDelete,
  onImageClick,
  onPostClick,
  onChatOpen,
  showExploreButton = false,
  onLikeCountChange,
  onChannelChange
}: PostCardProps) {
  const [likeCount, setLikeCount] = useState(initialLikeCount);
  const [isLiked, setIsLiked] = useState(false);
  const [isLiking, setIsLiking] = useState(false);
  const [showLikesDialog, setShowLikesDialog] = useState(false);
  const [likeUsers, setLikeUsers] = useState<Array<{ user: UserInfo, liked_at: string }>>([]);

  // 检查用户是否已点赞
  useEffect(() => {
    const checkLikeStatus = async () => {
      if (!user) return;

      try {
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');
        const response = await fetch(API.POSTS.LIKES.CHECK(post.post.id), {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
          }
        });

        if (response.status === 404) {
          // 帖子不存在，设置为未点赞状态
          setIsLiked(false);
          return;
        }

        if (response.ok) {
          const data = await response.json();
          setIsLiked(data.is_liked);
        }
      } catch (error) {
        // 静默处理错误，设置默认状态
        setIsLiked(false);
      }
    };

    checkLikeStatus();
  }, [post.post.id, user]);

  // useEffect合并，获取真实点赞数和用户列表
  const fetchLikeData = async () => {
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const [countRes, usersRes] = await Promise.all([
        fetch(API.POSTS.LIKES.COUNT(post.post.id), {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch(API.POSTS.LIKES.USERS(post.post.id), {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ]);

      // 检查响应状态，如果是404则说明帖子不存在，静默处理
      if (countRes.status === 404 || usersRes.status === 404) {
        // 帖子不存在，设置默认值
        setLikeCount(0);
        setLikeUsers([]);
        return;
      }

      if (countRes.ok && usersRes.ok) {
        const countData = await countRes.json();
        const usersData = await usersRes.json();
        setLikeCount(countData.like_count);
        setLikeUsers(usersData);
      }
    } catch (e) {
      // 静默处理错误，避免控制台噪音
      setLikeCount(0);
      setLikeUsers([]);
    }
  };

  useEffect(() => {
    fetchLikeData();
  }, [post.post.id]);

  const handleLike = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (!user) {
      toast.error('请先登录');
      return;
    }

    if (isLiking) return;

    setIsLiking(true);
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const method = isLiked ? 'DELETE' : 'POST';
      const response = await fetch(API.POSTS.LIKES.LIKE(post.post.id), {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        }
      });

      if (response.ok) {
        const newIsLiked = !isLiked;
        setIsLiked(newIsLiked);
        // 点赞/取消点赞后，强制刷新名单和数量
        await fetchLikeData();
        toast.success(newIsLiked ? '点赞成功' : '取消点赞');

        // 通知父组件点赞数变化
        onLikeCountChange?.(post.post.id, newIsLiked ? likeCount + 1 : Math.max(0, likeCount - 1));
      } else {
        const errorData = await response.json().catch(() => ({}));
        toast.error(errorData.detail || '操作失败');
      }
    } catch (error) {
      // console.error('点赞操作失败:', error);
      toast.error('网络错误，请重试');
    } finally {
      setIsLiking(false);
    }
  };


  // 使用导入的formatTimestamp函数，已修复时区问题

  return (
    <>
      <Card
        className="rounded-none cursor-pointer hover:bg-muted/30 transition-colors border-0 border-b border-border"
        onClick={() => onPostClick(post)}
      >
        <CardHeader className="p-4 pb-0 flex flex-row items-start gap-3">
          <Avatar className="h-10 w-10 border-1 border-orange-100">
            <AvatarImage src={post.owner?.thumbnail || "/placeholder.svg?height=40&width=40"} alt="@user" />
            <AvatarFallback>
              {post.owner?.display_name?.slice(0, 2) ||
                post.owner?.username?.slice(0, 2) ||
                post.post.owner_id.slice(0, 2)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center gap-1">
              <span className="font-bold">
                {post.owner?.display_name ||
                  post.owner?.username ||
                  (post.post.owner_id.includes('@')
                    ? post.post.owner_id.split('@')[0]
                    : post.post.owner_id.slice(0, 8))}
              </span>
              <Badge className="bg-yellow-500 text-xs">V</Badge>

              {post.channel && (
                <ChannelBadge
                  channel={post.channel}
                  size="sm"
                  clickable={false}
                />
              )}
              <div className="flex items-center gap-2 ml-auto">
                {user && user.id === post.post.owner_id && (
                  <>
                    <button
                      className="text-gray-400 hover:text-blue-500 transition-colors p-1 rounded-full"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onEdit(post.post.id, post.post.content);
                      }}
                      title="编辑"
                    >
                      <Pencil className="h-4 w-4" />
                    </button>
                    <button
                      className="text-gray-400 hover:text-red-500 transition-colors p-1 rounded-full"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onDelete(post.post.id);
                      }}
                      title="删除"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </>
                )}
                {user && user.role === 'admin' && (
                  <button
                    className="text-gray-400 hover:text-green-500 transition-colors p-1 rounded-full"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onChannelChange?.(post.post.id, post.channel?.id);
                    }}
                    title="修改板块"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </button>
                )}
              </div>
            </div>
            <span className="text-sm text-muted-foreground">
              {formatTimestamp(post.post.timestamp)}
            </span>
          </div>
        </CardHeader>

        <CardContent className="p-4 pt-2">
          {/* 条件显示：无图片时显示文字内容 */}
          {/* {(!post.images || post.images.length === 0) && ( */}
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <div className="line-clamp-4 text-gray-700 dark:text-gray-300 leading-relaxed">
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkMath]}
                rehypePlugins={[rehypeKatex]}
                components={{
                  ...MarkdownComponents,
                  // 简化组件以适应多行显示
                  h1: ({ children }) => <span className="font-semibold">{children}</span>,
                  h2: ({ children }) => <span className="font-semibold">{children}</span>,
                  h3: ({ children }) => <span className="font-medium">{children}</span>,
                  p: ({ children }) => <span className="block">{children}</span>,
                  code: ({ children }) => <span className="bg-gray-100 dark:bg-gray-800 px-1 rounded text-sm">{children}</span>,
                  pre: ({ children }) => <span>{children}</span>,
                  blockquote: ({ children }) => <span className="italic">{children}</span>,
                  ul: ({ children }) => <span>{children}</span>,
                  ol: ({ children }) => <span>{children}</span>,
                  li: ({ children }) => <span className="block">{children}</span>
                }}
              >
                {post.post.content}
              </ReactMarkdown>
            </div>
          </div>
          {/* )} */}

          {/* 有图片时显示图片（不显示文字） */}
          {post.images && post.images.length > 0 && (
            <div className="mt-3 prose prose-sm max-w-none dark:prose-invert">
              <div className="grid grid-cols-2 gap-2">
                {post.images.slice(0, 4).map((imageUrl, index) => {
                  const convertedUrl = convertSupabaseUrl(imageUrl);
                  return (
                    <div key={index} className="relative group">
                      <img
                        src={convertedUrl}
                        alt={`图片 ${index + 1}`}
                        className="w-full h-60 object-contain bg-gray-50 dark:bg-gray-800 rounded-lg hover:opacity-90 transition-opacity"
                        onClick={(e) => {
                          e.stopPropagation();
                          onImageClick(convertedUrl, `图片 ${index + 1}`);
                        }}
                        onError={(e) => {
                          // console.error('图片加载失败:', convertedUrl);
                          (e.target as HTMLImageElement).style.display = 'none';
                        }}
                      />
                      {post.images.length > 4 && index === 3 && (
                        <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                          <span className="text-white font-medium">+{post.images.length - 4}</span>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg flex items-center justify-center pointer-events-none">
                        <ZoomIn className="h-5 w-5 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="p-4 pt-0 pb-0 flex justify-between items-center">
          <PostActions
            likeCount={likeCount}
            commentCount={commentCount}
            isLiked={isLiked}
            isLiking={isLiking}
            onLike={handleLike}
            onComment={e => {
              e.stopPropagation();
              onPostClick(post);
            }}
            onExplore={showExploreButton ? (e => {
              e.stopPropagation();
              onChatOpen(post);
            }) : undefined}
            showExploreButton={showExploreButton}
          />
          {/* 点赞用户列表 - 优化为 ❤ 用户1、用户2、用户3 等N人 */}
          {likeCount > 0 && likeUsers.length > 0 && (
            <div className="flex items-center gap-1.5 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20 px-3 py-1.5 rounded-full border border-red-100 dark:border-red-900/30">
              <Heart className="h-3.5 w-3.5 text-red-500 fill-current" />
              <span className="text-xs text-gray-700 dark:text-gray-300 font-medium">
                {likeUsers.slice(0, 3).map((likeUser, i) => (
                  <React.Fragment key={likeUser.user.id}>
                    {i > 0 && <span className="text-gray-400 mx-1">·</span>}
                    <span className="hover:text-red-600 dark:hover:text-red-400 transition-colors cursor-pointer">
                      {likeUser.user.display_name || likeUser.user.username}
                    </span>
                  </React.Fragment>
                ))}
                {likeCount > 3 && (
                  <span
                    className="ml-1 text-red-500 hover:text-red-600 dark:hover:text-red-400 transition-colors cursor-pointer font-semibold"
                    onClick={e => {
                      e.stopPropagation();
                      setShowLikesDialog(true);
                    }}
                  >
                    等{likeCount}人
                  </span>
                )}
              </span>
            </div>
          )}
        </CardFooter>
      </Card >

      <LikesUsersDialog
        postId={post.post.id}
        isOpen={showLikesDialog}
        onClose={() => setShowLikesDialog(false)}
      />
    </>
  );
}