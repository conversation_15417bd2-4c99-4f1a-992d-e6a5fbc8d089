import { useState } from "react";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Trash2, MessageSquare, X, ChevronDown, ChevronRight, ArrowUp, ArrowDown, Image, SmilePlus, Send } from "lucide-react";
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents';
import { convertSupabaseUrl } from '@/utils/url-utils';
import { formatTimestamp } from '@/utils/timeUtils';
import { MentionInput } from "./MentionInput";
import { ImageUploader } from "./ImageUploader";
import 'katex/dist/katex.min.css';

// 类型定义
interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}
interface ChannelInfo {
  id: string;
  name: string;
  display_name: string;
  color?: string;
  icon?: string;
}
interface Comment {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
  };
  images: string[];
  owner?: UserInfo;
  channel?: ChannelInfo;
}
type PostReplyTreeResponse = {
  post: Comment;
  replies: PostReplyTreeResponse[];
};

interface CommentTreeProps {
  node: PostReplyTreeResponse;
  user: { id: string, email: string, username: string } | null;
  onDeleteComment: (commentId: string) => void;
  onImageClick: (src: string, alt?: string) => void;
  onReplySubmit: (parentId: string, content: string, image?: File | null) => void;
}

export function CommentTree({ node, user, onDeleteComment, onImageClick, onReplySubmit }: CommentTreeProps) {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [replyContent, setReplyContent] = useState("");
  const [replyImage, setReplyImage] = useState<File | null>(null);
  const [replyImagePreview, setReplyImagePreview] = useState<string | null>(null);
  const [showReplyImageUploader, setShowReplyImageUploader] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [voteCount, setVoteCount] = useState(Math.floor(Math.random() * 50) + 1); // 模拟投票数

  // 使用导入的formatTimestamp函数，已修复时区问题

  async function handleReply() {
    if (!replyContent.trim()) return;
    
    try {
      // 等待回复提交完成
      await onReplySubmit(node.post.post.id, replyContent, replyImage);
      
      // 提交成功后清空输入状态
      setReplyContent("");
      setReplyImage(null);
      setReplyImagePreview(null);
      setShowReplyImageUploader(false);
      setShowReplyInput(false);
    } catch (error) {
      // 如果提交失败，不关闭输入框，让用户可以重试
      console.error('回复提交失败:', error);
    }
  }

  function handleImageSelect(file: File | null) {
    setReplyImage(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => setReplyImagePreview(reader.result as string);
      reader.readAsDataURL(file);
    } else {
      setReplyImagePreview(null);
    }
    setShowReplyImageUploader(false);
  }

  return (
    <div className="relative">
      {/* 垂直线条 - Reddit 风格的线程线 */}
      <div className="absolute left-4 top-8 bottom-0 w-px bg-gray-200 dark:bg-gray-700" />
      
      <div className="flex gap-2">
        {/* 左侧投票区域 */}
        {/* <div className="flex flex-col items-center gap-1 pt-2">
          <button className="w-6 h-6 rounded hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center justify-center transition-colors">
            <ArrowUp className="h-4 w-4 text-gray-400 hover:text-orange-500" />
          </button>
          <span className="text-xs font-medium text-gray-600 dark:text-gray-400 min-w-[1.5rem] text-center">
            {voteCount}
          </span>
          <button className="w-6 h-6 rounded hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center justify-center transition-colors">
            <ArrowDown className="h-4 w-4 text-gray-400 hover:text-blue-500" />
          </button>
        </div> */}

        {/* 折叠/展开按钮 */}
        <div className="flex flex-col items-center pt-2">
          {node.replies && node.replies.length > 0 && (
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-center transition-colors"
              title={isCollapsed ? "展开回复" : "折叠回复"}
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4 text-gray-500" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-500" />
              )}
            </button>
          )}
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 min-w-0">
          <div className="p-2 mb-2">
            {/* 用户信息行 */}
            <div className="flex items-center gap-2 mb-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={node.post.owner?.thumbnail || "/placeholder.svg?height=24&width=24"} />
                <AvatarFallback className="text-xs">
                  {node.post.owner?.display_name?.slice(0, 2) || node.post.owner?.username?.slice(0, 2) || node.post.post.owner_id.slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <span className="font-medium text-sm text-gray-900 dark:text-gray-100">
                {node.post.owner?.display_name || node.post.owner?.username || (node.post.post.owner_id.includes('@') ? node.post.post.owner_id.split('@')[0] : node.post.post.owner_id.slice(0, 8))}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">{formatTimestamp(node.post.post.timestamp)}</span>
              {user && user.id === node.post.post.owner_id && (
                <button className="text-gray-400 hover:text-red-500 transition-colors ml-auto" onClick={() => onDeleteComment(node.post.post.id)} title="删除跟帖">
                  <Trash2 className="h-3 w-3" />
                </button>
              )}
            </div>

            {/* 评论内容 */}
            <div className="prose prose-sm max-w-none dark:prose-invert mb-3 ml-1">
              <ReactMarkdown remarkPlugins={[remarkGfm, remarkMath]} rehypePlugins={[rehypeKatex]} components={MarkdownComponents}>
                {node.post.post.content}
              </ReactMarkdown>
            </div>

            {/* 图片内容 */}
            {node.post.images && node.post.images.length > 0 && (
              <div className="mb-3 flex flex-col items-center space-y-2">
                {node.post.images.map((imageUrl, index) => {
                  const convertedUrl = convertSupabaseUrl(imageUrl);
                  return (
                    <img
                      key={index}
                      src={convertedUrl}
                      alt={`跟帖图片 ${index + 1}`}
                      className="h-80 w-auto object-contain rounded cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => onImageClick(convertedUrl, `跟帖图片 ${index + 1}`)}
                      onError={e => { (e.target as HTMLImageElement).style.display = 'none'; }}
                    />
                  );
                })}
              </div>
            )}

            {/* 操作按钮行 */}
            <div className="flex justify-end items-center gap-2 mt-1">
              {node.replies && node.replies.length > 0 && (
                <span className="text-xs text-muted-foreground">{node.replies.length} 条回复</span>
              )}
              <Button variant="ghost" size="sm" className="px-2 py-1 text-xs text-gray-500 hover:text-blue-500" onClick={() => setShowReplyInput(v => !v)}>
                <MessageSquare className="h-3 w-3 mr-1" />
                回复
              </Button>
            </div>

            {/* 回复输入框 - 与 CommentSection 保持一致 */}
            {showReplyInput && (
              <div className="mt-3 border-t pt-3">
                <div className="flex gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={(user as any)?.thumbnail || "/placeholder.svg?height=32&width=32"} />
                    <AvatarFallback>
                      {user?.username?.slice(0, 2) || user?.email?.slice(0, 2) || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-2">
                    <MentionInput
                      value={replyContent}
                      onChange={setReplyContent}
                      placeholder="回复内容..."
                      className="min-h-[80px]"
                    />
                    
                    {/* 回复图片预览 */}
                    {replyImagePreview && (
                      <div className="relative inline-block">
                        <img
                          src={replyImagePreview}
                          alt="回复图片预览"
                          className="max-w-xs max-h-32 object-cover rounded-lg border"
                        />
                        <button
                          onClick={() => handleImageSelect(null)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowReplyImageUploader(!showReplyImageUploader)}
                          className="text-muted-foreground hover:text-primary"
                        >
                          <Image className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-muted-foreground hover:text-primary"
                        >
                          <SmilePlus className="h-4 w-4" />
                        </Button>
                      </div>
                      <Button
                        size="sm"
                        onClick={handleReply}
                        disabled={!replyContent.trim()}
                      >
                        <Send className="h-4 w-4 mr-1" />
                        回复
                      </Button>
                    </div>

                    {/* 图片上传器 */}
                    {showReplyImageUploader && (
                      <div className="border rounded-lg p-3 bg-background">
                        <ImageUploader
                          onImageSelect={handleImageSelect}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 子评论递归 - 根据折叠状态显示 */}
          {node.replies && node.replies.length > 0 && !isCollapsed && (
            <div className="ml-8">
              {node.replies.map(child => (
                <CommentTree
                  key={child.post.post.id}
                  node={child}
                  user={user}
                  onDeleteComment={onDeleteComment}
                  onImageClick={onImageClick}
                  onReplySubmit={onReplySubmit}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}