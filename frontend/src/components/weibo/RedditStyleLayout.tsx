import { useState, useEffect } from 'react'
import { Crown, Star, UserCheck, Users } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ChannelBanner } from './ChannelBanner'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import axios from 'axios';
import { toast } from 'sonner';
import { API } from '@/config/api';

interface Channel {
  id: string
  name: string
  description: string
  member_count: number
  post_count: number
  is_member: boolean // 必需字段
  avatar_url?: string
  banner_url?: string
  created_at: string
  moderator_id?: string
  moderator_name?: string  // 版主显示名称
  moderator_username?: string  // 版主用户名
  // 新增：自定义样式属性
  custom_style?: {
    background_color?: string
    icon_url?: string
    text_color?: string
  }
}

interface RedditStyleLayoutProps {
  channelId: string
  children: React.ReactNode
  onCreatePost?: () => void
  hideBanner?: boolean // 新增：可选隐藏横幅
}

export function RedditStyleLayout({ channelId, children, onCreatePost, hideBanner }: RedditStyleLayoutProps) {
  const [channel, setChannel] = useState<Channel | null>(null)
  const [loading, setLoading] = useState(true)
  const [isJoining, setIsJoining] = useState(false)

  useEffect(() => {
    if (channelId && channelId !== 'hot') {
      fetchChannelInfo()
    } else if (channelId === 'hot') {
      // 设置热门帖子的虚拟频道信息
      setChannel({
        id: 'hot',
        name: '7日热门',
        description: '7天内最热门的帖子，根据回复数和点赞数排序',
        member_count: 0,
        post_count: 0,
        is_member: true,
        created_at: new Date().toISOString(),
        custom_style: {
          background_color: '#ddf7fb',
          text_color: '#4f3fc0'
        }
      })
      setLoading(false)
    }
  }, [channelId])

  // 在文件顶部添加辅助函数
  const isEmoji = (str: string): boolean => {
    // 更完整的emoji检测：包含更多Unicode范围
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F780}-\u{1F7FF}]|[\u{1F800}-\u{1F8FF}]/u;
    return emojiRegex.test(str) && !str.startsWith('http');
  };
  
  // 修改fetchChannelInfo函数中的custom_style部分
  const fetchChannelInfo = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(API.CHANNELS.DETAIL.replace(':id', channelId), {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      // console.log('Channel data received:', response.data);
  
      // 如果有版主ID但没有版主名称，获取版主信息
      if (response.data.moderator_id && !response.data.moderator_name) {
        try {
          const userResponse = await axios.get(API.USER.DETAIL.replace(':id', response.data.moderator_id), {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
  
          // 添加版主信息到频道数据
          response.data.moderator_name = userResponse.data.display_name || userResponse.data.username;
          response.data.moderator_username = userResponse.data.username;
        } catch (userError) {
          console.error('获取版主信息失败:', userError);
        }
      }
  
// 添加color字段到custom_style的映射
const iconValue = response.data.icon || response.data.avatar_url;
const channelData = {
  ...response.data,
  custom_style: {
    background_color: response.data.color || '#f8fafc',
    // 根据icon内容判断是emoji还是URL
    ...(iconValue && isEmoji(iconValue) 
      ? { icon_emoji: iconValue } 
      : { icon_url: iconValue }
    ),
    text_color: '#1f2937',
    ...response.data.custom_style // 保留已有的custom_style设置
  }
};
  
      setChannel(channelData);
    } catch (error) {
      console.error('获取板块信息失败:', error);
      toast.error('获取板块信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinChannel = async () => {
    if (!channel) return;

    setIsJoining(true);
    try {
      const token = localStorage.getItem('token');
      const endpoint = channel.is_member
        ? API.CHANNELS.LEAVE.replace(':id', channelId)
        : API.CHANNELS.JOIN.replace(':id', channelId);

      await axios.post(endpoint, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const wasJoining = !channel.is_member;
      setChannel(prev => prev ? {
        ...prev,
        is_member: !prev.is_member,
        member_count: prev.is_member ? prev.member_count - 1 : prev.member_count + 1
      } : null);

      // 触发自定义事件通知左侧边栏刷新
      window.dispatchEvent(new CustomEvent('channelMembershipChanged', {
        detail: {
          channelId,
          joined: wasJoining,
          channelData: channel
        }
      }));
      toast.success(wasJoining ? '成功加入板块' : '已退出板块');

    } catch (error) {
      console.error('加入/退出板块失败:', error);
      toast.error('操作失败，请重试');
    } finally {
      setIsJoining(false);
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-32 bg-muted rounded-lg animate-pulse" />
        <div className="h-16 bg-muted rounded-lg animate-pulse" />
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded-lg animate-pulse" />
            ))}
          </div>
          <div className="h-64 bg-muted rounded-lg animate-pulse" />
        </div>
      </div>
    )
  }

  if (!channel) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">板块不存在或加载失败</p>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 横幅区：使用独立的ChannelBanner组件 */}
      {!hideBanner && (
        <div className="px-8 py-6">
          <ChannelBanner
            channel={channel}
            isJoining={isJoining}
            onCreatePost={onCreatePost}
            onJoinChannel={handleJoinChannel}
          />
        </div>
      )}

      {/* 主要内容区域：占据剩余高度 */}
      <div className={`flex flex-1 overflow-hidden px-8 pb-6${hideBanner ? ' pt-8' : ''}`}>
        {/* 帖子内容区域：可滚动但隐藏滚动条 */}
        <div className="flex-1 overflow-y-auto pr-6 hide-scrollbar">
          {children}
        </div>

        {/* 右侧板块信息：固定宽度，不可滚动 */}
        <div className="w-80 flex-shrink-0 space-y-4">
          {/* 关于板块 */}
          <div className="bg-card border rounded-lg p-4">
            <div className="flex items-center justify-between mb-3 w-full">
              <div className="flex items-center">
                <Star className="h-4 w-4 mr-2" />
                <span>关于 r/{channel.name}</span>
              </div>
              <div>
                {channel.is_member ? (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" disabled className="text-green-600 cursor-default">
                          <UserCheck className="h-5 w-5" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>已加入</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                ) : (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={handleJoinChannel} disabled={isJoining} className="text-primary">
                          <Users className="h-5 w-5" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{isJoining ? '加入中...' : '加入板块'}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            </div>
            <div className="space-y-3 text-sm">
              <p className="text-muted-foreground">
                {channel.description}
              </p>

              <div className="flex justify-between py-2 border-t border-border">
                <span className="text-muted-foreground">成员</span>
                <span className="font-medium">{channel.member_count.toLocaleString()}</span>
              </div>

              <div className="flex justify-between py-2 border-t border-border">
                <span className="text-muted-foreground">帖子</span>
                <span className="font-medium">{channel.post_count.toLocaleString()}</span>
              </div>

              <div className="flex justify-between py-2 border-t border-border">
                <span className="text-muted-foreground">创建时间</span>
                <span className="font-medium">
                  {new Date(channel.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>

            {/* 原有的加入按钮已移除 */}
          </div>

          {/* 版主信息 */}
          <div className="bg-card border rounded-lg p-4">
            <h3 className="font-semibold mb-3 flex items-center">
              <Crown className="h-4 w-4 mr-2" />
              版主
            </h3>
            {channel.moderator_name ? (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="h-6 w-6 rounded-full bg-primary/20 flex items-center justify-center">
                    <span className="text-xs font-medium">
                      {channel.moderator_name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm font-medium">{channel.moderator_name}</span>
                    {channel.moderator_username && channel.moderator_username !== channel.moderator_name && (
                      <div className="text-xs text-muted-foreground">@{channel.moderator_username}</div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">暂无版主信息</div>
            )}
          </div>

          {/* 板块规则 */}
          <div className="bg-card border rounded-lg p-4">
            <h3 className="font-semibold mb-3">板块规则</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-start space-x-2">
                <span className="font-medium text-foreground">1.</span>
                <span>保持友善和尊重</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-medium text-foreground">2.</span>
                <span>发布相关内容</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-medium text-foreground">3.</span>
                <span>禁止垃圾信息</span>
              </div>
              <div className="flex items-start space-x-2">
                <span className="font-medium text-foreground">4.</span>
                <span>遵守社区准则</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}