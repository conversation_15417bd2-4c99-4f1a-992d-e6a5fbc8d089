import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Heart, AtSign, UserPlus, FileText, TrendingUp } from 'lucide-react';
import { NotificationType } from '@/types/notification';
import { NotificationService } from '@/utils/notificationService';

interface NotificationStats {
  total: number;
  unread: number;
  byType: {
    [key in NotificationType]: number;
  };
  recentActivity: {
    today: number;
    thisWeek: number;
    thisMonth: number;
  };
}

export function NotificationStats() {
  const [stats, setStats] = useState<NotificationStats>({
    total: 0,
    unread: 0,
    byType: {
      [NotificationType.MENTION]: 0,
      [NotificationType.COMMENT]: 0,
      [NotificationType.LIKE]: 0,
      [NotificationType.FOLLOW]: 0,
      [NotificationType.CHANNEL_POST]: 0,
    },
    recentActivity: {
      today: 0,
      thisWeek: 0,
      thisMonth: 0,
    }
  });
  const [loading, setLoading] = useState(true);

  // 获取通知统计数据
  const fetchStats = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/notifications/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        // 如果API不存在，使用模拟数据
        const mockStats: NotificationStats = {
          total: 156,
          unread: 12,
          byType: {
            [NotificationType.MENTION]: 23,
            [NotificationType.COMMENT]: 45,
            [NotificationType.LIKE]: 67,
            [NotificationType.FOLLOW]: 15,
            [NotificationType.CHANNEL_POST]: 6,
          },
          recentActivity: {
            today: 8,
            thisWeek: 32,
            thisMonth: 89,
          }
        };
        setStats(mockStats);
      }
    } catch (error) {
      console.error('获取通知统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const typeIcons = {
    [NotificationType.MENTION]: <AtSign className="h-4 w-4 text-blue-500" />,
    [NotificationType.COMMENT]: <MessageCircle className="h-4 w-4 text-green-500" />,
    [NotificationType.LIKE]: <Heart className="h-4 w-4 text-red-500" />,
    [NotificationType.FOLLOW]: <UserPlus className="h-4 w-4 text-purple-500" />,
    [NotificationType.CHANNEL_POST]: <FileText className="h-4 w-4 text-orange-500" />,
  };

  const typeLabels = {
    [NotificationType.MENTION]: '@提醒',
    [NotificationType.COMMENT]: '评论',
    [NotificationType.LIKE]: '点赞',
    [NotificationType.FOLLOW]: '关注',
    [NotificationType.CHANNEL_POST]: '频道新帖',
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">加载统计数据中...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* 总体统计 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">总通知数</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
          <p className="text-xs text-muted-foreground">
            {stats.unread > 0 ? `${stats.unread} 条未读` : '全部已读'}
          </p>
        </CardContent>
      </Card>

      {/* 今日活动 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">今日通知</CardTitle>
          <Badge variant="secondary">{stats.recentActivity.today}</Badge>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.recentActivity.today}</div>
          <p className="text-xs text-muted-foreground">
            本周 {stats.recentActivity.thisWeek} 条
          </p>
        </CardContent>
      </Card>

      {/* 本月统计 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">本月通知</CardTitle>
          <Badge variant="outline">{stats.recentActivity.thisMonth}</Badge>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.recentActivity.thisMonth}</div>
          <p className="text-xs text-muted-foreground">
            较上月活跃
          </p>
        </CardContent>
      </Card>

      {/* 最活跃类型 */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">最活跃类型</CardTitle>
          {(() => {
            const maxType = Object.entries(stats.byType).reduce((a, b) => 
              stats.byType[a[0] as NotificationType] > stats.byType[b[0] as NotificationType] ? a : b
            );
            return typeIcons[maxType[0] as NotificationType];
          })()}
        </CardHeader>
        <CardContent>
          {(() => {
            const maxType = Object.entries(stats.byType).reduce((a, b) => 
              stats.byType[a[0] as NotificationType] > stats.byType[b[0] as NotificationType] ? a : b
            );
            return (
              <>
                <div className="text-2xl font-bold">{maxType[1]}</div>
                <p className="text-xs text-muted-foreground">
                  {typeLabels[maxType[0] as NotificationType]}
                </p>
              </>
            );
          })()}
        </CardContent>
      </Card>

      {/* 通知类型分布 */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle className="text-sm font-medium">通知类型分布</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-5 gap-4">
            {Object.entries(stats.byType).map(([type, count]) => (
              <div key={type} className="text-center">
                <div className="flex justify-center mb-2">
                  {typeIcons[type as NotificationType]}
                </div>
                <div className="text-lg font-semibold">{count}</div>
                <div className="text-xs text-muted-foreground">
                  {typeLabels[type as NotificationType]}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}