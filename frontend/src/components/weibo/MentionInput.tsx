import { useState, useRef, useCallback, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import API from '@/config/api';

interface User {
  id: string;
  username: string;
  display_name?: string;
  thumbnail?: string;
}

interface MentionInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function MentionInput({
  value,
  onChange,
  placeholder = "写下你的评论... (使用 @ 提醒其他用户)",
  className = "",
  disabled = false
}: MentionInputProps) {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<User[]>([]);
  const [mentionStart, setMentionStart] = useState(-1);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // 搜索用户
  const searchUsers = async (query: string): Promise<User[]> => {
    if (query.length < 1) return [];

    try {
      const response = await fetch(`${API.API_PATH}/notifications/users/search?q=${encodeURIComponent(query)}`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
    }
    return [];
  };

  // 处理输入变化
  const handleInputChange = useCallback(async (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    const cursorPos = e.target.selectionStart;

    onChange(text);

    // 查找最近的 @ 符号
    let atIndex = -1;
    for (let i = cursorPos - 1; i >= 0; i--) {
      if (text[i] === '@') {
        // 检查 @ 前面是否是空格或行首
        if (i === 0 || text[i - 1] === ' ' || text[i - 1] === '\n') {
          atIndex = i;
          break;
        }
      } else if (text[i] === ' ' || text[i] === '\n') {
        // 遇到空格或换行，停止搜索
        break;
      }
    }

    if (atIndex !== -1) {
      const searchTerm = text.slice(atIndex + 1, cursorPos);

      // 检查搜索词是否包含空格（如果包含空格，说明不是在输入用户名）
      if (!searchTerm.includes(' ') && !searchTerm.includes('\n')) {
        setSearchTerm(searchTerm);
        setMentionStart(atIndex);

        if (searchTerm.length > 0) {
          const users = await searchUsers(searchTerm);
          setSuggestions(users);
          setShowSuggestions(users.length > 0);
          setSelectedIndex(0);
        } else {
          setShowSuggestions(false);
        }
      } else {
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
    }
  }, [onChange]);

  // 选择用户
  const selectUser = useCallback((user: User) => {
    if (!inputRef.current) return;

    const beforeMention = value.slice(0, mentionStart);
    const afterCursor = value.slice(inputRef.current.selectionStart);
    const newValue = `${beforeMention}@${user.username} ${afterCursor}`;

    onChange(newValue);
    setShowSuggestions(false);

    // 设置光标位置到用户名后面
    setTimeout(() => {
      if (inputRef.current) {
        const newCursorPos = mentionStart + user.username.length + 2; // +2 for @ and space
        inputRef.current.setSelectionRange(newCursorPos, newCursorPos);
        inputRef.current.focus();
      }
    }, 0);
  }, [value, mentionStart, onChange]);

  // 处理键盘事件
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev + 1) % suggestions.length);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => (prev - 1 + suggestions.length) % suggestions.length);
        break;
      case 'Enter':
      case 'Tab':
        e.preventDefault();
        selectUser(suggestions[selectedIndex]);
        break;
      case 'Escape':
        setShowSuggestions(false);
        break;
    }
  }, [showSuggestions, suggestions, selectedIndex, selectUser]);

  // 点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    if (showSuggestions) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showSuggestions]);

  return (
    <div className="relative">
      <Textarea
        ref={inputRef}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={`resize-none ${className}`}
        disabled={disabled}
      />

      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full bg-white dark:bg-gray-800 border rounded-lg shadow-lg max-h-40 overflow-y-auto mt-1"
        >
          {suggestions.map((user, index) => (
            <div
              key={user.id}
              className={`p-2 cursor-pointer flex items-center space-x-2 ${index === selectedIndex
                ? 'bg-orange-100 dark:bg-orange-900/20'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              onClick={() => selectUser(user)}
            >
              <Avatar className="h-6 w-6">
                <AvatarImage src={user.thumbnail} />
                <AvatarFallback className="text-xs">
                  {user.display_name?.slice(0, 2) || user.username?.slice(0, 2) || '?'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">
                  {user.display_name || user.username}
                </div>
                {user.display_name && user.username && (
                  <div className="text-xs text-muted-foreground truncate">
                    @{user.username}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}