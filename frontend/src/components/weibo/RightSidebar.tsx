import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { RefreshCw, Search, X } from "lucide-react"
import { useEffect, useState } from "react"
import API from "@/config/api"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents'

export function RightSidebar() {
  const [users, setUsers] = useState<any[]>([])
  const [posts, setPosts] = useState<any[]>([])
  const [allPosts, setAllPosts] = useState<any[]>([]) // 存储所有帖子（最多200条）
  const [searchResults, setSearchResults] = useState<any[]>([]) // 存储搜索结果
  const [myPosts, setMyPosts] = useState<any[]>([])
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [isLoadingMyPosts, setIsLoadingMyPosts] = useState(false)
  const [isLoadingSearchResults, setIsLoadingSearchResults] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("hot") // 当前激活的标签

  // 获取当前用户信息
  useEffect(() => {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (token) {
      fetch(API.AUTH.ME, {
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      })
        .then(response => response.json())
        .then(data => {
          setCurrentUser(data);
          // 获取用户发布的微博
          fetchMyPosts(data.id);
        })
        .catch(error => console.error('Error fetching current user:', error));
    }
  }, []);

  // 获取随机用户
  useEffect(() => {
    fetch(API.AUTH.RANDOM_USERS + '?limit=10', {
      headers: {
        'accept': 'application/json'
      }
    })
      .then(response => response.json())
      .then(data => setUsers(data))
      .catch(error => console.error('Error fetching users:', error))
  }, [])

  // 获取用于搜索的帖子数据
  useEffect(() => {
    fetchRecentPosts();
  }, [])

  // 获取热门微博 - 第60行附近
  useEffect(() => {
    fetch(API.POSTS.LIST + '?skip=0&limit=20', {
      headers: {
        'accept': 'application/json'
      }
    })
      .then(response => response.json())
      .then(data => {
        // 适配新的API响应格式
        const posts = data.items || [];
        // 过滤出类型为 NORMAL 的帖子，并根据用户ID过滤，最多5条
        const filteredPosts = posts
          .filter((post: any) => post.post.type === "NORMAL")
          .filter((post: any) => users.some((user: any) => user.id === post.post.owner_id))
          .slice(0, 5);
        setPosts(filteredPosts)
      })
      .catch(error => console.error('Error fetching posts:', error))
  }, [users])

  // 获取用户发布的微博
  const fetchMyPosts = (userId: string) => {
    if (!userId) return;

    setIsLoadingMyPosts(true);
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    fetch(API.POSTS.LIST + '?skip=0&limit=20', {
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    })
      .then(response => response.json())
      .then(data => {
        // 适配新的API响应格式
        const posts = data.items || [];
        // 过滤出用户自己发布的微博，且类型为 NORMAL（不包括评论）
        const userPosts = posts.filter((post: any) =>
          post.post.owner_id === userId && post.post.type === "NORMAL");
        setMyPosts(userPosts);
      })
      .catch(error => console.error('Error fetching user posts:', error))
      .finally(() => setIsLoadingMyPosts(false));
  }

  // 获取热门微博（按评论数排序）
  const fetchHotPosts = () => {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    // 显示加载状态
    setPosts([]);

    fetch(API.POSTS.LIST + '?skip=0&limit=20', {
      headers: {
        'accept': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {})
      }
    })
      .then(response => response.json())
      .then(data => {
        // 适配新的API响应格式
        const posts = data.items || [];
        // 过滤出类型为 NORMAL 的帖子（不包括评论）
        const normalPosts = posts.filter((post: any) => post.post.type === "NORMAL");

        // 获取每个帖子的评论数
        const fetchCommentsPromises = normalPosts.map((post: any) =>
          fetch(`${API.POSTS.DETAIL(post.post.id)}/comments`, {
            headers: {
              'accept': 'application/json',
              ...(token ? { 'Authorization': `Bearer ${token}` } : {})
            }
          })
          .then(res => res.json())
          .then(comments => ({
            ...post,
            commentCount: comments.length
          }))
          .catch(() => ({
            ...post,
            commentCount: 0
          }))
        );

        // 等待所有评论数获取完成
        return Promise.all(fetchCommentsPromises);
      })
      .then(postsWithComments => {
        // 按评论数排序，评论多的排在前面
        const sortedPosts = postsWithComments.sort((a: any, b: any) =>
          b.commentCount - a.commentCount
        );

        // 最多显示5条
        setPosts(sortedPosts.slice(0, 5));
      })
      .catch(error => {
        console.error('Error fetching hot posts:', error);
        // 如果获取失败，回退到普通排序
        fetch(API.POSTS.LIST + '?skip=0&limit=5', {
          headers: {
            'accept': 'application/json'
          }
        })
          .then(response => response.json())
          .then(data => {
            // 适配新的API响应格式
            const posts = data.items || [];
            // 过滤出类型为 NORMAL 的帖子（不包括评论）
            const normalPosts = posts.filter((post: any) => post.post.type === "NORMAL");
            setPosts(normalPosts);
          })
          .catch(err => console.error('Error fetching fallback posts:', err));
      });
  }

  // 获取最近的200条帖子（用于搜索）
  const fetchRecentPosts = () => {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    fetch(API.POSTS.LIST + '?skip=0&limit=200', {
      headers: {
        'accept': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {})
      }
    })
      .then(response => response.json())
      .then(data => {
        // 适配新的API响应格式
        const posts = data.items || [];
        // 过滤出类型为 NORMAL 的帖子（不包括评论）
        const normalPosts = posts.filter((post: any) => post.post.type === "NORMAL");
        setAllPosts(normalPosts);
      })
      .catch(error => console.error('Error fetching recent posts:', error));
  }

  // 处理微博内容，去掉 Markdown 标题标签并截短内容
  const processPostContent = (content: string) => {
    // 去掉 Markdown 标题标签（# 标题、## 标题等）
    const processedContent = content.replace(/^#+\s+(.*)$/m, '$1');
    // 截短内容，只保留前20个字符
    return processedContent.length > 20 ? processedContent.substring(0, 20) + '...' : processedContent;
  }

  // 根据搜索词过滤帖子
  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      setActiveTab("hot"); // 如果搜索词为空，切换到热搜标签
    } else {
      setIsLoadingSearchResults(true);
      // 使用 allPosts 进行搜索，这样可以搜索更多的帖子
      const filtered = allPosts.filter(post =>
        post.post.content.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSearchResults(filtered);
      setActiveTab("search"); // 搜索时自动切换到搜索结果标签
      setIsLoadingSearchResults(false);
    }
  }, [searchTerm, allPosts]);

  return (
    <aside className="md:col-span-3 lg:col-span-3 space-y-4">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <h3 className="font-bold">微博热搜</h3>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2"
              onClick={() => fetchHotPosts()}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              点击刷新
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {/* 搜索框 */}
          <div className="px-4 py-2">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="搜索微博内容..."
                className="pl-8 h-8 text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  onClick={() => setSearchTerm("")}
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full grid grid-cols-3 rounded-none bg-transparent h-auto">
              <TabsTrigger
                value="mine"
                className="rounded-none data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                onClick={() => currentUser && fetchMyPosts(currentUser.id)}
              >
                我的
              </TabsTrigger>
              <TabsTrigger
                value="hot"
                className="rounded-none data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                onClick={() => fetchHotPosts()}
              >
                热搜
              </TabsTrigger>
              <TabsTrigger
                value="search"
                className="rounded-none data-[state=active]:bg-transparent data-[state=active]:shadow-none"
                disabled={!searchTerm}
              >
                搜索结果
              </TabsTrigger>
            </TabsList>
            <TabsContent value="hot" className="m-0 p-0">
              <ScrollArea className="h-[400px]">
                <div className="p-2 space-y-2">
                  {posts.map((item, i) => (
                    <div
                      key={item.post.id}
                      className="flex items-center gap-2 px-2 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                      onClick={() => {
                        // 在当前页面中定位到对应的微博
                        const postElement = document.getElementById(`post-${item.post.id}`);
                        if (postElement) {
                          // 如果元素存在，滚动到该元素
                          postElement.scrollIntoView({ behavior: 'smooth' });
                          // 添加高亮效果
                          postElement.classList.add('bg-orange-50', 'dark:bg-orange-900/20');
                          // 3秒后移除高亮效果
                          setTimeout(() => {
                            postElement.classList.remove('bg-orange-50', 'dark:bg-orange-900/20');
                          }, 3000);
                        } else {
                          // 如果元素不存在，跳转到微博页面并显示该微博
                          const currentPath = window.location.pathname;

                          // 检查当前是否已经在微博页面
                          if (currentPath === '/weibo') {
                            // 如果已经在微博页面，使用 history.pushState 更新 URL 参数
                            const newUrl = `/weibo?post=${item.post.id}`;
                            window.history.pushState({ path: newUrl }, '', newUrl);

                            // 触发一个自定义事件，通知 WeiboPage 组件加载特定帖子
                            const event = new CustomEvent('loadPost', { detail: { postId: item.post.id } });
                            window.dispatchEvent(event);
                          } else {
                            // 如果不在微博页面，直接跳转
                            window.location.href = `/weibo?post=${item.post.id}`;
                          }
                        }
                      }}
                    >
                      <span className={`font-bold min-w-[16px] text-xs ${i < 3 ? "text-red-500" : "text-gray-400"}`}>{i + 1}</span>
                      <span className="flex-1 text-xs truncate">
                        <span className="text-blue-600 dark:text-blue-400 font-medium">
                          {getUserDisplayName(item)}：
                        </span>
                        <span className="text-gray-700 dark:text-gray-300">
                          {extractPostTitle(item.post.content)}
                        </span>
                      </span>
                      <div className="flex items-center gap-1 ml-2">
                        <span className="text-xs text-blue-500 whitespace-nowrap">{item.commentCount || 0}评</span>
                        <span className="text-xs text-muted-foreground whitespace-nowrap">{new Date(item.post.timestamp).toLocaleString('zh-CN', {month: '2-digit', day: '2-digit'})}</span>
                      </div>
                    </div>
                  ))}
                  {/* 查看更多按钮 */}
                  <Button variant="ghost" className="w-full text-sm justify-center">
                    查看完整热搜榜单
                    <span className="ml-1">→</span>
                  </Button>
                </div>
              </ScrollArea>
            </TabsContent>

            {/* 搜索结果标签页 */}
            <TabsContent value="search" className="m-0 p-0">
              <ScrollArea className="h-[400px]">
                <div className="p-2 space-y-2">
                  {isLoadingSearchResults ? (
                    <div className="flex justify-center items-center h-20">
                      <div className="animate-spin h-5 w-5 border-2 border-orange-500 border-t-transparent rounded-full"></div>
                    </div>
                  ) : searchResults.length > 0 ? (
                    <>
                      {searchResults.map((item, i) => (
                        <div
                          key={item.post.id}
                          className="flex items-center gap-2 px-2 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                          onClick={() => {
                            // 在当前页面中定位到对应的微博
                            const postElement = document.getElementById(`post-${item.post.id}`);
                            if (postElement) {
                              // 如果元素存在，滚动到该元素
                              postElement.scrollIntoView({ behavior: 'smooth' });
                              // 添加高亮效果
                              postElement.classList.add('bg-orange-50', 'dark:bg-orange-900/20');
                              // 3秒后移除高亮效果
                              setTimeout(() => {
                                postElement.classList.remove('bg-orange-50', 'dark:bg-orange-900/20');
                              }, 3000);
                            } else {
                              // 如果元素不存在，跳转到微博页面并显示该微博
                              const currentPath = window.location.pathname;

                              // 检查当前是否已经在微博页面
                              if (currentPath === '/weibo') {
                                // 如果已经在微博页面，使用 history.pushState 更新 URL 参数
                                const newUrl = `/weibo?post=${item.post.id}`;
                                window.history.pushState({ path: newUrl }, '', newUrl);

                                // 触发一个自定义事件，通知 WeiboPage 组件加载特定帖子
                                const event = new CustomEvent('loadPost', { detail: { postId: item.post.id } });
                                window.dispatchEvent(event);
                              } else {
                                // 如果不在微博页面，直接跳转
                                window.location.href = `/weibo?post=${item.post.id}`;
                              }
                            }
                          }}
                        >
                          <span className="font-medium text-gray-600 min-w-[16px] text-xs">{i + 1}</span>
                          <span className="flex-1 text-xs truncate">
                            <ReactMarkdown
                              remarkPlugins={[remarkGfm]}
                              components={MarkdownComponents}
                            >
                              {processPostContent(item.post.content)}
                            </ReactMarkdown>
                          </span>
                          <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">{new Date(item.post.timestamp).toLocaleString('zh-CN', {month: '2-digit', day: '2-digit'})}</span>
                        </div>
                      ))}
                    </>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <p>没有找到匹配"{searchTerm}"的内容</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="mt-2"
                        onClick={() => setSearchTerm("")}
                      >
                        清除搜索
                      </Button>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
            <TabsContent value="mine" className="m-0 p-0">
              <ScrollArea className="h-[400px]">
                <div className="p-2 space-y-2">
                  {isLoadingMyPosts ? (
                    <div className="flex justify-center items-center h-20">
                      <div className="animate-spin h-5 w-5 border-2 border-orange-500 border-t-transparent rounded-full"></div>
                    </div>
                  ) : myPosts.length > 0 ? (
                    <>
                      {myPosts.map((item, i) => (
                        <div
                          key={item.post.id}
                          className="flex items-center gap-2 px-2 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                          onClick={() => {
                            // 在当前页面中定位到对应的微博
                            const postElement = document.getElementById(`post-${item.post.id}`);
                            if (postElement) {
                              // 如果元素存在，滚动到该元素
                              postElement.scrollIntoView({ behavior: 'smooth' });
                              // 添加高亮效果
                              postElement.classList.add('bg-orange-50', 'dark:bg-orange-900/20');
                              // 3秒后移除高亮效果
                              setTimeout(() => {
                                postElement.classList.remove('bg-orange-50', 'dark:bg-orange-900/20');
                              }, 3000);
                            } else {
                              // 如果元素不存在，跳转到微博页面并显示该微博
                              const currentPath = window.location.pathname;

                              // 检查当前是否已经在微博页面
                              if (currentPath === '/weibo') {
                                // 如果已经在微博页面，使用 history.pushState 更新 URL 参数
                                const newUrl = `/weibo?post=${item.post.id}`;
                                window.history.pushState({ path: newUrl }, '', newUrl);

                                // 触发一个自定义事件，通知 WeiboPage 组件加载特定帖子
                                const event = new CustomEvent('loadPost', { detail: { postId: item.post.id } });
                                window.dispatchEvent(event);
                              } else {
                                // 如果不在微博页面，直接跳转
                                window.location.href = `/weibo?post=${item.post.id}`;
                              }
                            }
                          }}
                        >
                          <span className="font-medium text-gray-600 min-w-[16px] text-xs">{i + 1}</span>
                          <span className="flex-1 text-xs truncate">
                            <ReactMarkdown
                              remarkPlugins={[remarkGfm]}
                              components={MarkdownComponents}
                            >
                              {processPostContent(item.post.content)}
                            </ReactMarkdown>
                          </span>
                          <span className="text-xs text-muted-foreground whitespace-nowrap ml-2">{
                            (() => {
                              const utcTimestamp = item.post.timestamp.includes('Z') || item.post.timestamp.includes('+') || item.post.timestamp.includes('-')
                                ? item.post.timestamp
                                : item.post.timestamp + 'Z';
                              return new Date(utcTimestamp).toLocaleString('zh-CN', {month: '2-digit', day: '2-digit'});
                            })()
                          }</span>
                        </div>
                      ))}
                      <Button
                        variant="ghost"
                        className="w-full text-sm justify-center"
                        onClick={() => window.location.href = '/weibo'}
                      >
                        查看我的全部微博
                        <span className="ml-1">→</span>
                      </Button>
                    </>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      {currentUser ? (
                        <>
                          <p>您还没有发布任何微博</p>
                          <Button
                            variant="outline"
                            className="mt-4"
                            onClick={() => window.location.href = '/weibo'}
                          >
                            去发布微博
                          </Button>
                        </>
                      ) : (
                        <>
                          <p>请先登录查看您的微博</p>
                          <Button
                            variant="outline"
                            className="mt-4"
                            onClick={() => window.location.href = '/login'}
                          >
                            去登录
                          </Button>
                        </>
                      )}
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <h3 className="font-bold">你可能感兴趣的人</h3>
        </CardHeader>
        <CardContent className="space-y-4">
          {users.map((user) => (
            <div key={user.id} className="flex items-start gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={user.avatar_url || "/placeholder.svg?height=40&width=40"} alt={`@${user.username}`} />
                <AvatarFallback>{user.display_name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="font-medium">{user.display_name}</div>
                <div className="text-xs text-muted-foreground">{user.username}</div>
              </div>
              <Button variant="outline" className="rounded-full text-orange-500 border-orange-500">
                + 关注
              </Button>
            </div>
          ))}
        </CardContent>
      </Card>
    </aside>
  )
}

  // 提取微博主题（第一个Markdown标题）
  const extractPostTitle = (content: string) => {
    if (!content) return '无标题';
    
    // 匹配第一个Markdown标题（# 或 ## 等）
    const titleMatch = content.match(/^#+\s+(.+)$/m);
    if (titleMatch) {
      return titleMatch[1].trim();
    }
    
    // 如果没有标题，取前20个字符作为标题
    const plainText = content.replace(/[#*`\[\]()]/g, '').trim();
    return plainText.length > 20 ? plainText.substring(0, 20) + '...' : plainText || '无标题';
  };

  // 获取用户显示名称
  const getUserDisplayName = (post: any) => {
    if (post.owner?.display_name) {
      return post.owner.display_name;
    }
    if (post.owner?.username) {
      return post.owner.username;
    }
    return '未知用户';
  };