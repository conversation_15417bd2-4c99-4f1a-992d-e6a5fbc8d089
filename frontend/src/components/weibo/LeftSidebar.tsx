import { useState, useEffect } from 'react'
import { Home, Plus, TrendingUp, Users, Hash, X } from 'lucide-react'
import API from '@/config/api'
import { Button } from '@/components/ui/button'

interface Channel {
  id: string
  name: string
  display_name: string
  description: string
  member_count: number
  post_count: number
  is_member?: boolean
  icon?: string
  color?: string
  is_default?: boolean
}

interface LeftSidebarProps {
  selectedChannelId?: string
  onChannelSelect: (channelId: string | undefined) => void
}

export function LeftSidebar({ selectedChannelId, onChannelSelect }: LeftSidebarProps) {
  const [channels, setChannels] = useState<Channel[]>([])
  const [loading, setLoading] = useState(true)
  const [allChannels, setAllChannels] = useState<Channel[]>([])
  const [loadingAll, setLoadingAll] = useState(true)

  useEffect(() => {
    fetchChannels()
    fetchAllChannels()

    // 监听频道成员状态变化事件
    const handleChannelMembershipChanged = (event: CustomEvent) => {
      const { channelId, joined, channelData } = event.detail

      if (joined) {
        // 用户加入了频道，重新获取用户频道列表
        fetchChannels()
      } else {
        // 用户离开了频道，从本地状态中移除
        setChannels(prev => prev.filter(channel => channel.id !== channelId))
      }

      // 更新所有频道列表中的成员状态
      setAllChannels(prev => prev.map(channel =>
        channel.id === channelId
          ? { ...channel, is_member: joined }
          : channel
      ))
    }

    window.addEventListener('channelMembershipChanged', handleChannelMembershipChanged as EventListener)

    return () => {
      window.removeEventListener('channelMembershipChanged', handleChannelMembershipChanged as EventListener)
    }
  }, [])

  const fetchChannels = async () => {
    try {
      const token = localStorage.getItem('token')
      // console.log('获取用户板块列表 - API URL:', API.CHANNELS.USER_JOINED)
      // console.log('获取用户板块列表 - Token存在:', !!token)

      const response = await fetch(`${API.CHANNELS.USER_JOINED}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      })

      // console.log('用户板块列表API响应状态:', response.status, response.statusText)

      if (response.ok) {
        const data = await response.json()
        // console.log('用户板块列表API返回数据:', data)
        setChannels(data.items || data || [])
      } else {
        const errorText = await response.text()
        console.error('获取用户板块列表失败:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        })
      }
    } catch (error) {
      console.error('获取用户板块列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchAllChannels = async () => {
    setLoadingAll(true)
    try {
      const token = localStorage.getItem('token')
      // console.log('获取所有板块列表 - API URL:', API.CHANNELS.LIST)

      const response = await fetch(`${API.CHANNELS.LIST}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      })

      // console.log('所有板块列表API响应状态:', response.status, response.statusText)

      if (response.ok) {
        const data = await response.json()
        // console.log('所有板块列表API返回数据:', data)

        // 为每个板块添加is_member状态
        const channelsWithMemberStatus = (data.items || data || []).map((channel: Channel) => ({
          ...channel,
          is_member: channels.some(userChannel => userChannel.id === channel.id)
        }))

        setAllChannels(channelsWithMemberStatus)
      } else {
        const errorText = await response.text()
        console.error('获取所有板块列表失败:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        })
      }
    } catch (error) {
      console.error('获取所有板块列表失败:', error)
    } finally {
      setLoadingAll(false)
    }
  }

  // 更新所有板块的成员状态
  const updateAllChannelsWithMemberStatus = () => {
    setAllChannels(prev => prev.map(channel => ({
      ...channel,
      is_member: channels.some(userChannel => userChannel.id === channel.id)
    })))
  }

  // 当用户板块列表更新时，更新所有板块的成员状态
  useEffect(() => {
    if (channels.length > 0 && allChannels.length > 0) {
      updateAllChannelsWithMemberStatus()
    }
  }, [channels])

  const handleJoinChannel = async (channelId: string) => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${API.CHANNELS.JOIN.replace(':id', channelId)}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      })

      if (response.ok) {
        // 重新获取用户板块列表
        fetchChannels()
        // 更新所有板块列表中的状态
        setAllChannels(prev => prev.map(channel =>
          channel.id === channelId
            ? { ...channel, is_member: true, member_count: channel.member_count + 1 }
            : channel
        ))
      }
    } catch (error) {
      console.error('加入板块失败:', error)
    }
  }

  return (
    <div className="h-full overflow-y-auto text-base">
      <div className="p-4">
        {/* 顶部导航 */}
        <div className="space-y-2 mb-6">
          <button
            onClick={() => {
              onChannelSelect(undefined)
            }}
            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${!selectedChannelId
              ? 'bg-primary/10 text-primary font-medium'
              : 'hover:bg-muted text-muted-foreground hover:text-foreground'
              }`}
            title="查看所有微博"
          >
            <Home className="h-4 w-4" />
            <span>首页</span>
          </button>

          <button
            onClick={() => {
              onChannelSelect('hot')
            }}
            className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${selectedChannelId === 'hot'
              ? 'bg-primary/10 text-primary font-medium'
              : 'hover:bg-muted text-muted-foreground hover:text-foreground'
              }`}
            title="查看热门微博"
          >
            <TrendingUp className="h-4 w-4" />
            <span>热门</span>
          </button>
        </div>

        {/* 板块列表 */}
        <div className="space-y-1">
          <div className="flex items-center justify-between px-3 py-2">
            <h3 className="font-semibold text-muted-foreground uppercase tracking-wider">
              我的板块
            </h3>
            <button className="p-1 hover:bg-muted rounded">
              <Plus className="h-3 w-3" />
            </button>
          </div>

          {loading ? (
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="px-3 py-2">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                </div>
              ))}
            </div>
          ) : channels.length > 0 ? (
            <div className="space-y-1">
              {channels.map((channel) => (
                <button
                  key={channel.id}
                  onClick={() => onChannelSelect(channel.id)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${selectedChannelId === channel.id
                    ? 'bg-muted text-foreground'
                    : 'hover:bg-muted text-foreground'
                    }`}
                >
                  <div className="flex-shrink-0">
                    {channel.icon ? (
                      <div className="h-5 w-5 rounded-full flex items-center justify-center text-sm">
                        {channel.icon}
                      </div>
                    ) : (
                      <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center">
                        <Hash className="h-3 w-3" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 text-left truncate">
                    <div className="">r/{channel.name}</div>
                  </div>
                  {channel.is_member && (
                    <div className="flex-shrink-0">
                      <div className="h-2 w-2 bg-green-500 rounded-full" />
                    </div>
                  )}
                </button>
              ))}
            </div>
          ) : (
            <div className="px-3 py-4 text-center">
              <div className="text-sm text-muted-foreground mb-2">
                暂无板块
              </div>
              <div className="text-xs text-muted-foreground">
                点击 + 创建或加入板块
              </div>
            </div>
          )}
        </div>

        {/* 所有板块部分 */}
        <div className="space-y-1 mt-6">
          <div className="flex items-center justify-between px-3 py-2">
            <h3 className="font-semibold text-muted-foreground uppercase tracking-wider">
              所有板块
            </h3>
          </div>

          {loadingAll ? (
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="px-3 py-2">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-1">
              {allChannels
                .filter(channel => !channels.some(userChannel => userChannel.id === channel.id))
                .map((channel) => (
                  <button
                    key={channel.id}
                    onClick={() => onChannelSelect(channel.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${selectedChannelId === channel.id
                      ? 'bg-muted text-foreground'
                      : 'hover:bg-muted text-foreground'
                      }`}
                  >
                    <div className="flex-shrink-0">
                      {channel.icon ? (
                        <div className="h-5 w-5 rounded-full flex items-center justify-center text-sm">
                          {channel.icon}
                        </div>
                      ) : (
                        <div className="h-5 w-5 rounded-full bg-primary/20 flex items-center justify-center">
                          <Hash className="h-3 w-3" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 text-left truncate">
                      <div className="">r/{channel.name}</div>
                    </div>
                    <div className="flex-shrink-0">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-6 px-2 text-xs"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleJoinChannel(channel.id)
                        }}
                      >
                        +
                      </Button>
                    </div>
                  </button>
                ))}
            </div>
          )}
        </div>

        {/* 底部信息 */}
        {/* <div className="mt-8 pt-4 border-t border-border">
          <div className="text-muted-foreground space-y-1">
            <div className="flex items-center space-x-2">
              <Users className="h-3 w-3" />
              <span>社区</span>
            </div>
            <div className="pl-5">
              {channels.length} 个我的板块 · {allChannels.length} 个总板块
            </div>
          </div>
        </div> */}
      </div>
    </div>
  )
}