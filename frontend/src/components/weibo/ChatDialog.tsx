import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { ChannelBadge } from "./ChannelSelector"
import {
  Send,
  Copy,
  Check,
  ChevronDown,
  ChevronRight,
  Brain
} from "lucide-react"
import { useState, useRef, useEffect } from "react"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents'
import { formatTimestamp } from '@/utils/timeUtils'
import { useLLMStore } from '@/store/llmStore'
import API from '@/config/api'
import { toast } from "sonner"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import 'katex/dist/katex.min.css'

import { ModelManager } from '@/utils/modelUtils';

interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}

interface ChannelInfo {
  id: string;
  name: string;
  display_name: string;
  color?: string;
  icon?: string;
}

interface Post {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
  };
  images: string[];
  owner?: UserInfo;
  channel?: ChannelInfo;
}

interface ChatDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  post: Post | null;
}

export function ChatDialog({ open, onOpenChange, post }: ChatDialogProps) {
  const [userInput, setUserInput] = useState("");
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [copiedMessageIndex, setCopiedMessageIndex] = useState<number | null>(null);
  const [expandedThink, setExpandedThink] = useState<Record<number, boolean>>({});
  const [messageComplete, setMessageComplete] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 第82行，修改 useLLMStore 的解构
  const { selectedModel, providers, defaultModels, setSelectedModel: setModelInStore, fetchProviders } = useLLMStore();

  // 当对话框打开时，设置初始输入
  useEffect(() => {
    if (open && post) {
      const filteredContent = post.post.content
        .replace(/!\[.*?\]\(.*?\)/g, '')
        .replace(/\[.*?\]\(.*?\)/g, '')
        .replace(/https?:\/\/[^\s]+/g, '')
        .replace(/http:\/\/127\.0\.0\.1:[0-9]+\/storage\/.*?\.pdf\??[^\s]*/g, '')
        .replace(/http:\/\/127\.0\.0\.1:[0-9]+\/storage\/.*?\.docx?\??[^\s]*/g, '')
        .trim();

      setUserInput(`请探究这条微博，内容是：${filteredContent}`);
      setChatMessages([]);
    }
  }, [open, post]);

  useEffect(() => {
    fetchProviders();
  }, [fetchProviders]);

  // 处理 <think> 标签，提取内容
  const extractThinkContent = (content: string): { mainContent: string, thinkContent: string | null } => {
    const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
    const matches = content.match(thinkRegex);

    let thinkContent: string | null = null;
    if (matches && matches.length > 0) {
      thinkContent = matches[0].replace(/<think>|<\/think>/g, '').trim();
    }

    const mainContent = content.replace(thinkRegex, '');
    return { mainContent, thinkContent };
  };

  // 处理模型选择
  const handleModelChange = (value: string) => {
    const [providerId, modelId] = value.split(':::');

    if (!providerId || !modelId) {
      console.error('Invalid model value format:', value);
      return;
    }

    const provider = providers?.find(p => p.id === providerId);
    const model = provider?.models.find(m => m.id === modelId);

    if (model && provider) {
      setModelInStore(model.id);
    }
  };

  // 处理提交对话
  const handleSubmit = async () => {
    if (!userInput.trim() || isLoading) return;

    const userMessage: ChatMessage = { role: 'user', content: userInput };
    setChatMessages(prev => [...prev, userMessage]);
    setUserInput("");
    setIsLoading(true);

    try {
      const requestConfig = ModelManager.createRequestConfig([...chatMessages, userMessage], {
        temperature: 0.6,
        stream: true
      });
      
      if (!requestConfig) {
        toast.error('模型配置错误，请检查模型设置');
        return;
      }

      const response = await fetch(requestConfig.url, {
        method: 'POST',
        headers: requestConfig.headers,
        body: JSON.stringify(requestConfig.requestBody)
      });

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder('utf-8');
      let responseText = "";
      let buffer = "";
      let done = false;

      const assistantMessage: ChatMessage = { role: 'assistant', content: '' };
      setChatMessages(prev => [...prev, assistantMessage]);

      while (!done) {
        const { value, done: streamDone } = await reader.read();
        done = streamDone;

        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          const lines = buffer.split('\n');
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(5);
              if (data.trim() === '[DONE]') {
                continue;
              }

              try {
                const json = JSON.parse(data);
                const content = json.choices?.[0]?.delta?.content || '';
                if (content) {
                  responseText += content;
                  setChatMessages(prev => {
                    const newMessages = [...prev];
                    newMessages[newMessages.length - 1].content = responseText;
                    return newMessages;
                  });
                }
              } catch (e) {
                console.error('解析JSON失败:', e, data);
              }
            }
          }
        }
      }
      
      setMessageComplete(true);
      
    } catch (error) {
      console.error('对话请求失败:', error);
      setChatMessages(prev => [
        ...prev,
        { role: 'assistant', content: "对话请求失败，请检查网络连接或模型配置" }
      ]);
      setMessageComplete(true);
    } finally {
      setIsLoading(false);
    }
  };

  // 滚动到底部
  useEffect(() => {
    if (messageComplete) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      setMessageComplete(false);
    }
  }, [messageComplete]);

  // 复制消息内容
  const copyMessageToClipboard = async (text: string, index: number) => {
    try {
      let contentToCopy = text;
      if (chatMessages[index]?.role === 'assistant') {
        const { mainContent } = extractThinkContent(text);
        contentToCopy = mainContent;
      }

      await navigator.clipboard.writeText(contentToCopy);
      setCopiedMessageIndex(index);

      setTimeout(() => {
        setCopiedMessageIndex(null);
      }, 2000);

      toast.success('内容已复制到剪贴板');
    } catch (err) {
      console.error('复制失败:', err);
      toast.error('复制失败，请手动复制');
    }
  };

  // 使用导入的formatTimestamp函数，已修复时区问题

  if (!post) return null;

  return (
    <div className="flex flex-col h-full w-full">
      {/* 标题栏 */}
      <div className="flex items-center gap-2 px-4 py-2 border-b bg-muted/50">
        <Brain className="h-5 w-5 text-orange-500" />
        <span className="font-bold text-lg">AI 探究微博</span>
        <div className="ml-auto">
          <Select
            value={selectedModel && (providers || defaultModels) ? 
              `${(providers?.find(p => p.models.some(m => m.id === selectedModel.id)) || 
                 defaultModels?.find(p => p.models.some(m => m.id === selectedModel.id)))?.id}:::${selectedModel.id}` : ''}
            onValueChange={handleModelChange}
          >
            <SelectTrigger className="w-44 h-8 text-sm">
              <SelectValue placeholder="选择大模型" />
            </SelectTrigger>
            <SelectContent>
              {/* 显示默认模型 */}
              {defaultModels?.map(provider => (
                provider.models.map(model => (
                  <SelectItem key={`${provider.id}:::${model.id}`} value={`${provider.id}:::${model.id}`}>
                     {provider.name || provider.id} - {model.name}
                  </SelectItem>
                ))
              ))}
              {/* 显示用户模型 */}
              {providers?.map(provider => (
                provider.models.map(model => (
                  <SelectItem key={`${provider.id}:::${model.id}`} value={`${provider.id}:::${model.id}`}>
                    {provider.name || provider.id} - {model.name}
                  </SelectItem>
                ))
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="flex-1 flex flex-col min-h-0 p-4 overflow-y-auto">
        {/* 对话区域 */}
        <div className="flex-1 rounded-xs flex flex-col min-h-0">
          {/* 对话消息列表 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-white dark:bg-neutral-950">
            {chatMessages.map((message, index) => {
              const { mainContent, thinkContent } = message.role === 'assistant' 
                ? extractThinkContent(message.content)
                : { mainContent: message.content, thinkContent: null };

              return (
                <div key={index} className="w-full flex flex-row items-start">
                  <div className="flex-1">
                    <div className={`w-full rounded-none px-0 py-3 mb-0 ${message.role === 'assistant'
                      ? 'bg-grey-50 dark:bg-blue-900/40'
                      : 'bg-slate-50 dark:bg-orange-900/30'} text-base text-gray-900 dark:text-gray-100`}
                    >
                      {message.role === 'assistant' && thinkContent && (
                        <div className="mb-3 border-b border-border pb-3">
                          <button
                            onClick={() => setExpandedThink(prev => ({ ...prev, [index]: !prev[index] }))}
                            className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
                          >
                            {expandedThink[index] ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                            思考过程
                          </button>
                          {expandedThink[index] && (
                            <div className="mt-2 text-sm text-muted-foreground bg-background/50 rounded p-2">
                              <ReactMarkdown
                                remarkPlugins={[remarkGfm, remarkMath]}
                                rehypePlugins={[rehypeKatex]}
                                components={MarkdownComponents}
                              >
                                {thinkContent}
                              </ReactMarkdown>
                            </div>
                          )}
                        </div>
                      )}
                      <div>
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm, remarkMath]}
                          rehypePlugins={[rehypeKatex]}
                          components={MarkdownComponents}
                        >
                          {mainContent}
                        </ReactMarkdown>
                      </div>
                    </div>
                    {/* 复制按钮 */}
                    {message.role === 'assistant' && (
                      <div className="flex justify-end mt-1">
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6"
                          onClick={() => copyMessageToClipboard(message.content, index)}
                        >
                          {copiedMessageIndex === index ? <Check className="h-4 w-4 text-green-500" /> : <Copy className="h-4 w-4" />}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </div>
          {/* 输入区 */}
          <div className="border-t p-3 flex items-end gap-2 bg-background">
            <Textarea
              value={userInput}
              onChange={e => setUserInput(e.target.value)}
              placeholder="请输入你的问题..."
              className="flex-1 min-h-[40px] max-h-32 resize-none"
              onKeyDown={e => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSubmit();
                }
              }}
              disabled={isLoading}
            />
            <Button
              onClick={handleSubmit}
              disabled={isLoading || !userInput.trim()}
              className="shrink-0"
            >
              {isLoading ? <span className="animate-spin">⏳</span> : <Send className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

