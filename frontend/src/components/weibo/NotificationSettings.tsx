import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { NotificationType } from '@/types/notification';

interface NotificationPreferences {
  [NotificationType.MENTION]: boolean;
  [NotificationType.COMMENT]: boolean;
  [NotificationType.LIKE]: boolean;
  [NotificationType.FOLLOW]: boolean;
  [NotificationType.CHANNEL_POST]: boolean;
  email_notifications: boolean;
  push_notifications: boolean;
}

export function NotificationSettings() {
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    [NotificationType.MENTION]: true,
    [NotificationType.COMMENT]: true,
    [NotificationType.LIKE]: true,
    [NotificationType.FOLLOW]: true,
    [NotificationType.CHANNEL_POST]: true,
    email_notifications: false,
    push_notifications: true,
  });
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 加载用户的通知偏好设置
  const loadPreferences = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/users/notification-preferences', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setPreferences(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('加载通知设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存通知偏好设置
  const savePreferences = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/users/notification-preferences', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(preferences)
      });

      if (response.ok) {
        toast.success('通知设置已保存');
      } else {
        toast.error('保存失败，请稍后重试');
      }
    } catch (error) {
      console.error('保存通知设置失败:', error);
      toast.error('保存失败，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  // 更新单个偏好设置
  const updatePreference = (key: keyof NotificationPreferences, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 组件加载时获取设置
  useEffect(() => {
    loadPreferences();
  }, []);

  const notificationTypes = [
    {
      key: NotificationType.MENTION as keyof NotificationPreferences,
      label: '@提醒通知',
      description: '当有人在帖子中提到你时接收通知'
    },
    {
      key: NotificationType.COMMENT as keyof NotificationPreferences,
      label: '评论通知',
      description: '当有人评论你的帖子时接收通知'
    },
    {
      key: NotificationType.LIKE as keyof NotificationPreferences,
      label: '点赞通知',
      description: '当有人点赞你的帖子时接收通知'
    },
    {
      key: NotificationType.FOLLOW as keyof NotificationPreferences,
      label: '关注通知',
      description: '当有人关注你时接收通知'
    },
    {
      key: NotificationType.CHANNEL_POST as keyof NotificationPreferences,
      label: '频道新帖通知',
      description: '当你关注的频道有新帖子时接收通知'
    }
  ];

  if (loading) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">加载设置中...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>通知设置</CardTitle>
        <p className="text-sm text-muted-foreground">
          管理你希望接收的通知类型
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 通知类型设置 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">通知类型</h3>
          {notificationTypes.map((type) => (
            <div key={type.key} className="flex items-center justify-between space-x-4">
              <div className="flex-1">
                <Label htmlFor={type.key} className="text-sm font-medium">
                  {type.label}
                </Label>
                <p className="text-xs text-muted-foreground mt-1">
                  {type.description}
                </p>
              </div>
              <Switch
                id={type.key}
                checked={preferences[type.key]}
                onCheckedChange={(checked) => updatePreference(type.key, checked)}
              />
            </div>
          ))}
        </div>

        <Separator />

        {/* 通知方式设置 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">通知方式</h3>
          
          <div className="flex items-center justify-between space-x-4">
            <div className="flex-1">
              <Label htmlFor="push_notifications" className="text-sm font-medium">
                浏览器推送通知
              </Label>
              <p className="text-xs text-muted-foreground mt-1">
                在浏览器中接收实时推送通知
              </p>
            </div>
            <Switch
              id="push_notifications"
              checked={preferences.push_notifications}
              onCheckedChange={(checked) => updatePreference('push_notifications', checked)}
            />
          </div>

          <div className="flex items-center justify-between space-x-4">
            <div className="flex-1">
              <Label htmlFor="email_notifications" className="text-sm font-medium">
                邮件通知
              </Label>
              <p className="text-xs text-muted-foreground mt-1">
                通过邮件接收重要通知
              </p>
            </div>
            <Switch
              id="email_notifications"
              checked={preferences.email_notifications}
              onCheckedChange={(checked) => updatePreference('email_notifications', checked)}
            />
          </div>
        </div>

        <Separator />

        {/* 保存按钮 */}
        <div className="flex justify-end">
          <Button onClick={savePreferences} disabled={saving}>
            {saving ? '保存中...' : '保存设置'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}