import { useState, useEffect } from 'react';
import logoImage from '@/assets/3Stooges.jpg';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useSloganStore } from "@/store/sloganStore";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { useAuth } from "@/utils/AuthContext";
import { useLLMStore } from '@/store/llmStore';
// import { useUserStore } from '@/store/userStore';
import { useAgentStore } from '@/store/agentStore';
import { useSearchStore } from '@/store/searchStore';
import { RegisterDialog } from './RegisterDialog';

interface LoginDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function LoginDialog({ open, onOpenChange }: LoginDialogProps) {
  const { slogan, fetchSlogan } = useSloganStore();
  const { login, isAuthenticated } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [remember, setRemember] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showRegister, setShowRegister] = useState(false);

  // 初始化数据
  useEffect(() => {
    fetchSlogan();
    useLLMStore.getState().fetchProviders();
    // useUserStore.getState().fetchUsers();
    useAgentStore.getState().setCurrentAgent('deep');
    useSearchStore.getState().setResults([]);
    useSearchStore.getState().clearSelected();
  }, [fetchSlogan]);

  // 当认证状态变化时，自动关闭登录对话框
  useEffect(() => {
    if (isAuthenticated && open) {
      onOpenChange(false);
    }
  }, [isAuthenticated, open, onOpenChange]);

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      // console.log('开始登录流程...');
      // console.log('登录信息:', { email, passwordLength: password.length });
      setLoading(true);
      setError(null);

      // 显示一个弹窗，显示登录信息
      // alert(`正在尝试登录，邮箱: ${email}, 密码长度: ${password.length}`);

      // console.log('调用 login 函数，参数:', { email });
      await login(email, password);
      // console.log('login 函数调用成功');

      // 登录成功后初始化各个 store
      // console.log('开始初始化各个 store...');
      const { fetchProviders } = useLLMStore.getState();
      // const { fetchUsers } = useUserStore.getState();
      const { setCurrentAgent } = useAgentStore.getState();
      // const { setResults, clearSelected } = useSearchStore.getState();

      // 初始化 LLM store 并打印日志
      // console.log('开始初始化 LLM store...');
      await fetchProviders();
      // console.log('LLM Providers:', useLLMStore.getState().providers);
      // console.log('Current Selected Model:', useLLMStore.getState().selectedModel);

      // 初始化 agent store 并打印日志
      // console.log('开始初始化 agent store...');
      setCurrentAgent('deep');
      // console.log('Current Agent:', useAgentStore.getState().currentAgent);

      // 初始化 search store 并打印日志
      // setResults([]);
      // clearSelected();
      // console.log('Search Results:', useSearchStore.getState().results);
      // console.log('Selected Items:', useSearchStore.getState().selected);

      // console.log('登录流程完成，关闭登录对话框');
      onOpenChange(false);
    } catch (err: any) {
      console.error('登录失败:', err);
      setError(err.message || '登录失败，请检查邮箱和密码');
    } finally {
      setLoading(false);
      // console.log('登录流程结束，loading 状态重置');
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[380px]">
          <DialogHeader className="text-center">
            <img
              src={logoImage}
              alt="3Stooges Logo"
              className="mx-auto mb-4 h-16 w-auto"
            />
            <DialogTitle className="text-sm text-slate-400 text-center">{slogan}</DialogTitle>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <form onSubmit={handleEmailLogin} className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  邮箱
                </label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <label htmlFor="password" className="text-sm font-medium">
                    密码
                  </label>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={remember}
                  onCheckedChange={(checked) => setRemember(checked as boolean)}
                  disabled={loading}
                />
                <label htmlFor="remember" className="text-sm">
                  记住我
                </label>
              </div>

              {error && (
                <div className="text-sm text-destructive">{error}</div>
              )}

              <Button type="submit" className="w-full" disabled={loading}>
                {loading ? '登录中...' : '登录'}
              </Button>
            </form>

            <DialogFooter className="flex justify-center mt-4">
              <div className="text-sm text-muted-foreground">
                还没有账号？{' '}
                <Button
                  variant="link"
                  className="p-0 h-auto text-sm"
                  onClick={() => setShowRegister(true)}
                >
                  立即注册
                </Button>
              </div>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>

      <RegisterDialog
        open={showRegister}
        onClose={() => setShowRegister(false)}
      />
    </>
  );
}

export default LoginDialog;