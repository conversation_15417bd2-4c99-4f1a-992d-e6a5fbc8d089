import { useState, useEffect } from "react";
import {
  RefreshCw,
  Globe,
  Send,
  Search,
  ChevronDown,
  LibraryBig,
  Lightbulb,
  Bird,
  Loader2,
  Wand2
} from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";

import { useSearchEngineStore } from "@/store/searchEngineStore";
import { useThoughtStore } from "@/store/thoughtsStore";
import { useDigestStore } from "@/store/digestStore";
import { useDocumentStore } from "@/store/documentStore";
import { CustomModel, useModelConfigStore } from "@/store/modelConfigStore";
import { checkLLMHealth } from "@/utils/healthCheck";
import { useLLMStore } from '@/store/llmStore';  // Add this import
import { useAgentStore } from '@/store/agentStore';  // 添加 AgentStore
import { AgentPopover } from './chat/AgentPopover';  // 导入 AgentPopover 组件
import { useSearchStore } from '@/store/searchStore';
import weiboIcon from '@/assets/weibo.svg';
import { PromptOptimizationDialog } from './PromptOptimizationDialog';
import { useLocation } from 'wouter';

// 在组件的props中添加defaultModelInfo
interface ChatInputProps {
  onSubmit: (message: string, modelInfo?: { baseUrl: string; model: string }) => void;
  placeholder?: string;
  disabled?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  onSearchModeChange?: (isSearch: boolean) => void; // 新增
  onSearchEngineChange?: (engine: string) => void; // 添加搜索引擎切换回调
  hideSearchToggle?: boolean; // 添加新的属性
  isRequesting?: boolean; // 添加请求状态属性
  onStopRequest?: () => void; // 添加停止请求回调
  defaultModelInfo?: {
    model: string;
    baseUrl: string;
  } | null;
}

// 微博类型定义（与ContentTabs一致）
interface WeiboPost {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
    type: string;
  };
  images: string[];
  owner?: {
    id: string;
    username?: string;
    display_name?: string;
    email?: string;
    thumbnail?: string;
  };
}

export function ChatInput({
  onSubmit,
  placeholder = "输入消息...",
  disabled = false,
  isRequesting = false,
  onStopRequest,
  value,
  onChange,
  onSearchModeChange,
  hideSearchToggle = false,
  defaultModelInfo = null
}: ChatInputProps) {
  const [input, setInput] = useState(value || "");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { selectedEngine, setSelectedEngine } = useSearchEngineStore();
  const { customModels } = useModelConfigStore();
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const { isSearchMode, setSearchMode } = useSearchStore();  // 使用 store 中的状态

  // 添加提示词优化对话框状态
  const [isPromptOptOpen, setIsPromptOptOpen] = useState(false);

  // 添加Deep Search状态
  const [isDeepSearchActive, setIsDeepSearchActive] = useState(false);

  // 获取当前 agent
  const { currentAgent, setCurrentAgent, agents } = useAgentStore();

  // 用于页面导航
  const [, setLocation] = useLocation();

  const defaultModel: CustomModel = {
    id: "default",
    type: "language",
    name: "Deep Think",
    description: "系统默认模型",
    baseModel: import.meta.env.VITE_LLM_MODEL || "",
    baseService: import.meta.env.VITE_LLM_HOST || "",
    temperature: 0.6,
    maxTokens: 4096,
    agentId: "999",
    status: "ready",
    createdAt: new Date().toISOString(),
  };

  const allModels = [defaultModel, ...customModels];

  const [isDocsOpen, setIsDocsOpen] = useState(false);
  const { documents, isLoading, fetchDocuments } = useDocumentStore();

  // 对加入对话的文档进行过滤
  const [docFilter, setDocFilter] = useState("");
  const filteredDocuments = documents.filter(
    (doc) => {
      if (!doc) return false;

      const titleMatch = doc.title ?
        doc.title.toLowerCase().includes(docFilter.toLowerCase()) :
        false;

      const contentMatch = doc.markdown_url ?
        doc.markdown_url.toLowerCase().includes(docFilter.toLowerCase()) :
        false;

      return titleMatch || contentMatch;
    }
  );
  const [healthStatus, setHealthStatus] = useState<{
    status: "ok" | "error";
    latency: number;
  } | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  // 添加健康检查函数
  const checkHealth = async (baseUrl: string) => {
    if (isChecking) return;
    setIsChecking(true);
    try {
      const result = await checkLLMHealth({ serviceId: "chat", baseUrl });
      setHealthStatus(result);
    } catch (error) {
      // console.error("健康检查失败:", error);
    } finally {
      setIsChecking(false);
    }
  };

  // 从 LLM Store 获取提供商和默认模型信息
  const { providers, defaultModels, selectedProvider, fetchProviders } = useLLMStore();

  // 移除自动激活搜索模式的逻辑，让用户主动选择
  // useEffect(() => {
  //   const { selectedEngine } = useSearchEngineStore.getState();
  //   // 如果已经选择了搜索引擎，则自动开启搜索模式
  //   if (selectedEngine && selectedEngine !== 'default') {
  //     setSearchMode(true);
  //     onSearchModeChange?.(true);
  //   }
  // }, []);

  // 在组件初始化时，如果有默认的模型信息，则设置为当前选择的模型
  useEffect(() => {
    if (defaultModelInfo && defaultModelInfo.model && defaultModelInfo.baseUrl && providers.length > 0) {
      // console.log('ChatInput 接收到默认模型信息:', defaultModelInfo);

      // 查找是否有匹配的提供商和模型
      const provider = providers.find(p =>
        p.models.some(m => m.id === defaultModelInfo.model)
      );

      if (provider) {
        // console.log('找到匹配的提供商:', provider.name);
        // 设置选中的模型
        setSelectedModel(defaultModelInfo.model);

        // 更新全局状态
        useLLMStore.getState().setSelectedProvider(provider);
        useLLMStore.getState().setSelectedModel(defaultModelInfo.model);
      } else {
        // console.log('未找到匹配的提供商，使用自定义模型');
        // 如果没有找到匹配的提供商，直接设置自定义模型
        useLLMStore.setState({
          selectedModel: {
            id: defaultModelInfo.model,
            providerName: "Custom Provider",
            baseUrl: defaultModelInfo.baseUrl
          }
        });
      }
    }
  }, [defaultModelInfo?.model, defaultModelInfo?.baseUrl, providers.length]);

  // 在组件挂载时获取模型列表
  useEffect(() => {
    fetchProviders();
  }, []);

  // 获取所有已启用的提供商的模型列表（不包括默认模型）
  const availableModels = providers
    .filter(provider => provider.enabled && !provider.isDefault)
    .flatMap(provider => provider.models.map(model => ({
      ...model,
      providerName: provider.name,
      providerColor: provider.color,
      baseUrl: provider.baseUrl
    })));

  // 获取默认模型列表
  const availableDefaultModels = defaultModels
    .filter(provider => provider.enabled)
    .flatMap(provider => provider.models.map(model => ({
      ...model,
      providerName: provider.name,
      providerColor: provider.color,
      baseUrl: provider.baseUrl,
      isDefault: true
    })));

  // 初始化时从 store 获取选中的模型
  useEffect(() => {
    const { selectedModel: storeSelectedModel } = useLLMStore.getState();
    if (storeSelectedModel && !selectedModel) {
      setSelectedModel(storeSelectedModel.id);
    }
  }, []);

  // 在获取默认模型列表后添加自动选择逻辑
  useEffect(() => {
    // 如果当前没有选中任何模型，且有可用的默认模型，则自动选择第一个默认模型
    if (!selectedModel && defaultModels.length > 0) {
      const availableDefaults = defaultModels
        .filter(provider => provider.enabled)
        .flatMap(provider => provider.models.map(model => ({
          ...model,
          providerName: provider.name,
          providerColor: provider.color,
          baseUrl: provider.baseUrl,
          isDefault: true
        })));

      if (availableDefaults.length > 0) {
        const firstDefaultModel = availableDefaults[0];
        // console.log('自动选择默认模型:', firstDefaultModel.name);

        setSelectedModel(firstDefaultModel.id);

        // 同时更新全局状态
        const defaultProvider = defaultModels.find(p =>
          p.models.some(m => m.id === firstDefaultModel.id)
        );

        if (defaultProvider) {
          useLLMStore.getState().setSelectedProvider(defaultProvider);
          useLLMStore.getState().setSelectedModel(firstDefaultModel.id);
        }
      }
    }
  }, [selectedModel, defaultModels]);

  // 当用户选择模型时，更新 store 中的选中模型
  const handleModelSelect = (modelId: string) => {
    setSelectedModel(modelId);

    // 首先在默认模型中查找
    const defaultProvider = defaultModels.find(p =>
      p.models.some(m => m.id === modelId)
    );

    if (defaultProvider) {
      // console.log('选择默认模型:', modelId, '提供商:', defaultProvider.name);
      useLLMStore.getState().setSelectedProvider(defaultProvider);
      useLLMStore.getState().setSelectedModel(modelId);
      return;
    }

    // 然后在用户模型中查找
    const userProvider = providers.find(p =>
      p.models.some(m => m.id === modelId)
    );

    if (userProvider) {
      // console.log('选择用户模型:', modelId, '提供商:', userProvider.name);
      useLLMStore.getState().setSelectedProvider(userProvider);
      useLLMStore.getState().setSelectedModel(modelId);
    } else {
      // console.warn('未找到模型对应的提供商:', modelId);
    }
  };
  // 添加定期检查
  useEffect(() => {
    // 获取当前选中模型的信息
    const modelInfo = getSelectedModelInfo();
    const baseUrl = modelInfo?.baseUrl || import.meta.env.VITE_LLM_HOST || "";

    if (baseUrl) {
      checkHealth(baseUrl);
      const interval = setInterval(() => {
        // 重新获取最新的模型信息进行检查
        const currentModelInfo = getSelectedModelInfo();
        const currentBaseUrl = currentModelInfo?.baseUrl || import.meta.env.VITE_LLM_HOST || "";
        checkHealth(currentBaseUrl);
      }, 30000); // 每30秒检查一次

      return () => clearInterval(interval);
    }
  }, [selectedModel]); // 添加 selectedModel 作为依赖项

  useEffect(() => {
    if (isDocsOpen) {
      // console.log('文档选择器已打开，正在获取文档列表...');
      fetchDocuments().then(() => {
        // console.log('文档获取成功，共获取到', documents.length, '个文档');
      }).catch(error => {
        // console.error('文档获取失败:', error);
      });
    }
  }, [isDocsOpen]);

  const handleDocSelect = (markdown: string | null) => {
    if (!markdown) return;

    const prefix =
      "请阅读我提交的补充材料，放入到上下文中，继续我们的对话。\n\n";

    const sections = markdown.split(/(?=^# )/m);
    // 获取前三个段落（如果存在的话）
    const content = sections
      .slice(0, 4) // 取4个是因为split可能会在开头产生一个空字符串
      .filter((section) => section.trim()) // 过滤掉空段落
      .slice(0, 2) // 只3个段落（摘要）
      .join("\n\n")
      .trim();
    const newInput = prefix + content;

    setInput(newInput);
    onChange?.(newInput);
    setIsDocsOpen(false);
  };

  useEffect(() => {
    if (value !== undefined) {
      setInput(value);
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    onChange?.(e.target.value);
  };

  // 添加键盘事件处理函数，支持 Shift+Enter 和 Ctrl+Enter 提交
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // 检测 Shift+Enter 或 Ctrl+Enter
    if (e.key === 'Enter' && (e.ctrlKey)) {
      e.preventDefault(); // 阻止默认行为（换行）

      // 如果输入框不为空且未禁用且当前没有请求正在进行，则提交
      if (input.trim() && !disabled && !isRequesting) {
        // 如果 Deep Search 激活，导航到 Deep Search 页面
        if (isDeepSearchActive) {
          // 使用 sessionStorage 存储查询，避免在URL中显示
          sessionStorage.setItem('deepSearchQuery', input.trim());
          setLocation('/deep-search');
          setInput("");
          setIsDeepSearchActive(false);
          return;
        }

        const modelInfo = getSelectedModelInfo();
        if (!modelInfo) {
          // console.error('未找到选中的模型信息');
          return;
        }

        // 获取当前选择的 agent 信息
        // const { currentAgent, agents } = useAgentStore.getState();
        // console.log('键盘提交对话，当前 Agent:', currentAgent, agents[currentAgent]?.name);

        // 将 agent 信息添加到消息中
        let message = input.trim();

        // 如果需要在消息中添加 agent 标记，可以在这里添加
        // 例如：message = `[Agent: ${currentAgent}] ${message}`;

        onSubmit(message, modelInfo);
        setInput("");
      }
    }
  };

  // 获取当前选中模型的信息
  const getSelectedModelInfo = () => {
    // console.group('获取模型信息');
    // console.log('当前选中模型:', selectedModel);

    if (!selectedModel || selectedModel === "Deep Think") {
      const defaultInfo = {
        baseUrl: import.meta.env.VITE_LLM_HOST || "",
        model: import.meta.env.VITE_LLM_MODEL || "",
      };
      // console.log('使用默认模型配置:', defaultInfo);
      // console.groupEnd();
      return defaultInfo;
    }

    // 首先在默认模型中查找选中的模型
    const defaultProvider = defaultModels.find(p =>
      p.enabled && p.models.some(m => m.id === selectedModel)
    );

    if (defaultProvider) {
      const modelInfo = {
        baseUrl: defaultProvider.baseUrl || "",
        model: selectedModel,
      };
      // console.log('找到默认模型配置:', modelInfo);
      // console.groupEnd();
      return modelInfo;
    }

    // 然后在用户提供商中查找选中的模型
    const provider = providers.find(p =>
      p.enabled && p.models.some(m => m.id === selectedModel)
    );

    // console.log('找到的提供商:', provider);

    if (!provider) {
      // console.warn('未找到对应的提供商配置');
      // console.groupEnd();
      return null;
    }

    const modelInfo = {
      baseUrl: provider.baseUrl || "",
      model: selectedModel,
    };

    // console.log('返回模型配置:', modelInfo);
    // console.groupEnd();
    return modelInfo;
  };

  // 修改提交函数
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !disabled && !isRequesting) {
      // 如果 Deep Search 激活，导航到 Deep Search 页面
      if (isDeepSearchActive) {
        // 使用 sessionStorage 存储查询，避免在URL中显示
        sessionStorage.setItem('deepSearchQuery', input.trim());
        setLocation('/deep-search');
        setInput("");
        setIsDeepSearchActive(false);
        return;
      }

      const modelInfo = getSelectedModelInfo();
      if (!modelInfo) {
        // console.error('未找到选中的模型信息');
        return;
      }

      // 获取当前选择的 agent 信息
      // const { currentAgent, agents } = useAgentStore.getState();
      // console.log('提交对话，当前 Agent:', currentAgent, agents[currentAgent]?.name);

      // 记录当前搜索模式状态
      // console.log('提交对话，当前搜索模式:', isSearchMode ? '开启' : '关闭');

      // 将 agent 信息添加到消息中
      let message = input.trim();

      // TODO: 提交一个对话，就添加一个Memory

      onSubmit(message, modelInfo);
      setInput("");
    }
  };

  const handleStopRequest = () => {
    if (onStopRequest) {
      onStopRequest();
    }
  };

  const [isThoughtsOpen, setIsThoughtsOpen] = useState(false);
  const [thoughtsFilter, setThoughtsFilter] = useState("");

  const { thoughts } = useThoughtStore();
  const { digests } = useDigestStore();

  // 添加 useEffect 来在 Popover 打开时获取数据
  useEffect(() => {
    if (isThoughtsOpen) {
      useThoughtStore.getState().fetchThoughts();
    }
  }, [isThoughtsOpen]);

  // 合并 thoughts 和 digests
  const allItems = [
    ...thoughts.map((t) => ({
      id: t.id,
      title: (t.desc || '').slice(0, 20) + ((t.desc || '').length > 20 ? "..." : ""),
      content: t.desc,
      type: "thought" as const,
    })),
    ...digests.map((d) => ({
      id: d.id,
      title: d.title || "摘要",
      content: d.content,
      type: "digest" as const,
    })),
  ];

  const filteredThoughts = allItems.filter((item) =>
    item.title.toLowerCase().includes(thoughtsFilter.toLowerCase())
  );

  const handleThoughtSelect = (content: string | null, type: "thought" | "digest") => {
    if (!content) return;

    const prefix =
      type === "thought"
        ? "让我们继续讨论这个话题，请添加以下内容到上下文中：\n\n"
        : "请参考以下内容，继续我们的对话：\n\n";

    const newInput = prefix + content;
    setInput(newInput);
    onChange?.(newInput);
    setIsThoughtsOpen(false);
  };

  // 微博上下文相关状态
  const [isWeiboOpen, setIsWeiboOpen] = useState(false);
  const [weiboFilter, setWeiboFilter] = useState("");
  const [isWeiboLoading, setIsWeiboLoading] = useState(false);
  const [weibos, setWeibos] = useState<WeiboPost[]>([]);

  // 获取微博列表（最近200条）
  useEffect(() => {
    if (!isWeiboOpen || weibos.length > 0) return;
    setIsWeiboLoading(true);
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    // 修正API端点为 /api/posts
    fetch('/api/posts?limit=200', {
      headers: {
        'accept': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {})
      }
    })
      .then(res => res.json())
      .then(data => {
        // 只保留类型为 NORMAL 的帖子（保险起见）
        setWeibos(Array.isArray(data.items) ? data.items.filter((item: any) => item.post?.type === 'NORMAL') : []);
      })
      .catch((error) => {
        // console.error('Failed to fetch weibo posts:', error);
        setWeibos([]);
      })
      .finally(() => setIsWeiboLoading(false));
  }, [isWeiboOpen, weibos.length]);

  const filteredWeibos = weibos.filter(post =>
    post.post.content.toLowerCase().includes(weiboFilter.toLowerCase())
  );

  const handleWeiboSelect = (post: WeiboPost) => {
    const prefix = "请参考以下微博内容，继续我们的对话：\n\n";
    const newInput = prefix + post.post.content;
    setInput(newInput);
    onChange?.(newInput);
    setIsWeiboOpen(false);
  };

  // 处理Deep Search - 只切换状态，不立即导航
  const handleDeepSearch = () => {
    setIsDeepSearchActive(!isDeepSearchActive);
  };

  // 处理提示词优化
  const handlePromptOptimization = () => {
    if (!input.trim()) {
      alert("请先输入要优化的提示词");
      return;
    }
    setIsPromptOptOpen(true);
  };

  // 应用优化后的提示词
  const handleApplyOptimized = (optimizedPrompt: string) => {
    setInput(optimizedPrompt);
    onChange?.(optimizedPrompt);
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="relative">

        {/* agent display - hide when textarea has content */}
        {!input.trim() && (
          <div className="absolute top-2 right-2 flex items-center gap-2 z-10">
            {/* 显示当前选择的 Agent */}
            <div className="flex items-center gap-1 bg-gray-100/70 dark:bg-gray-800/50 px-2 py-0.5 rounded-full text-xs border border-gray-200/50 dark:border-gray-700/50">
              <span className="text-gray-500 dark:text-gray-400">Agent:</span>
              <span className="font-medium">
                {agents[currentAgent]?.name || currentAgent}
              </span>

            </div>
            {/* Agent Popover */}
            <AgentPopover
              onAgentSelect={(agentId) => {
                // console.log('选择 Agent:', agentId);
                setCurrentAgent(agentId);
              }}
            />

            {healthStatus && (
              <div className="flex items-center gap-2">
                <span
                  className={`inline-block w-2 h-2 rounded-full ${healthStatus.status === "ok" ? "bg-green-500" : "bg-red-500"
                    }`}
                />
              </div>
            )}
          </div>
        )}

        <textarea
          value={input}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          className="p-4 pb-12 block w-full border border-gray-200 rounded-lg text-sm
                  focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50
                  disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700
                  dark:text-neutral-400 dark:placeholder-neutral-500
                  dark:focus:ring-neutral-600 resize-none min-h-[80px]"
          placeholder={placeholder || "输入消息，按 Shift+Enter 或 Ctrl+Enter 发送"}
          disabled={disabled}
        />

        <div className="absolute bottom-px inset-x-px p-2 rounded-b-lg bg-white dark:bg-neutral-900">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">

              {/* Search - 极简版本 */}
              {!hideSearchToggle && (
                <button
                  type="button"
                  onClick={() => {
                    const newMode = !isSearchMode;
                    setSearchMode(newMode);
                    onSearchModeChange?.(newMode);

                    // 如果启用搜索模式，随机选择一个可用的搜索引擎
                    if (newMode) {
                      const { engines } = useSearchEngineStore.getState();
                      const availableEngines = engines.filter(engine => engine.is_active);
                      if (availableEngines.length > 0) {
                        const randomEngine = availableEngines[Math.floor(Math.random() * availableEngines.length)];
                        setSelectedEngine(randomEngine.id);
                      }
                    }
                  }}
                  className={`inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                    transition-colors duration-200 focus:z-10 focus:outline-none
                    ${isSearchMode
                      ? 'bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50'
                      : 'text-gray-500 hover:bg-gray-100 focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700'
                    }`}
                  title={isSearchMode ? '搜索模式已启用' : '点击启用搜索模式'}
                >
                  <Globe className="size-4" />
                </button>
              )}

              {/* Context */}
              <div className="border ml-1 mr-1 rounded-lg bg-sky-50">

                {/* context: papers */}
                <Popover open={isDocsOpen} onOpenChange={setIsDocsOpen}>
                  <PopoverTrigger asChild>
                    <button
                      type="button"
                      className="inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                            text-gray-500 hover:bg-white focus:z-10 focus:outline-none
                            focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-700
                            dark:focus:bg-neutral-700"
                    >
                      <LibraryBig className="size-4 hover:text-purple-500" />
                      {/* <span  className="w-3 h-3 block">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 103.19 122.88"><path fill="#2563eb" d="M17.16 0h82.72a3.32 3.32 0 013.31 3.31v92.32c-.15 2.58-3.48 2.64-7.08 2.48H15.94c-4.98 0-9.05 4.07-9.05 9.05s4.07 9.05 9.05 9.05h80.17v-9.63h7.08v12.24c0 2.23-1.82 4.05-4.05 4.05H16.29C7.33 122.88 0 115.55 0 106.59V17.16C0 7.72 7.72 0 17.16 0zm3.19 13.4h2.86c1.46 0 2.66.97 2.66 2.15v67.47c0 1.18-1.2 2.15-2.66 2.15h-2.86c-1.46 0-2.66-.97-2.66-2.15V15.55c.01-1.19 1.2-2.15 2.66-2.15z" fill-rule="evenodd" clip-rule="evenodd"/></svg>
                  </span> */}
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-0">
                    {/* Header */}
                    <div className="border-b px-4 py-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 rounded-t-lg">
                      <h4 className="text-sm font-medium text-center">添加论文到对话</h4>
                    </div>

                    {/* Content */}
                    <div className="p-4 space-y-4">
                      <div className="flex flex-col gap-2">
                        <div className="relative">
                          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            value={docFilter}
                            onChange={(e) => setDocFilter(e.target.value)}
                            placeholder="搜索文档..."
                            className="w-full pl-8 pr-4 py-1 text-sm border rounded-md
                     focus:outline-none focus:ring-1 focus:ring-blue-500
                     dark:bg-neutral-800 dark:border-neutral-700
                     dark:text-neutral-200 dark:placeholder-neutral-400"
                          />
                        </div>
                      </div>
                      {isLoading ? (
                        <div className="flex justify-center p-4">
                          <Loader2 className="h-6 w-6 animate-spin" />
                        </div>
                      ) : (
                        <div className="max-h-[250px] overflow-y-auto space-y-2 pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                          {filteredDocuments.map((doc) => (
                            <button
                              key={doc.id}
                              onClick={() => handleDocSelect(doc.description)}
                              className="w-full text-left p-2 text-sm rounded-lg hover:bg-gray-100
                                   dark:hover:bg-neutral-800 flex items-center justify-between"
                            >
                              <span className="line-clamp-1">{doc.title}</span>
                              {/* <span className="text-xs text-gray-500">{doc.content_type}</span> */}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Footer */}
                    <div className="border-t px-4 py-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 rounded-b-lg">
                      <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                        在知识库中预处理相关论文
                      </p>
                    </div>
                  </PopoverContent>
                </Popover>

                {/* context: Thoughts  */}
                <Popover open={isThoughtsOpen} onOpenChange={setIsThoughtsOpen}>
                  <PopoverTrigger asChild>
                    <button
                      type="button"
                      className="inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                        text-gray-500 hover:bg-white focus:z-10 focus:outline-none
                        focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-700
                        dark:focus:bg-neutral-700"
                    >
                      {/* <span className="w-4 h-4 block">
                    <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 113.77 122.88"><title>idea</title><path fill="#f54900" d="M2.89,59.92a2.79,2.79,0,0,1-2-.75,2.84,2.84,0,0,1-.88-2,2.8,2.8,0,0,1,2.72-2.89l8.65-.3a2.83,2.83,0,0,1,2,.76,2.81,2.81,0,0,1-1.83,4.85l-8.66.29ZM68.1,40.42a6.12,6.12,0,0,1,2.14.35,8.27,8.27,0,0,1,3.61,2.5,8,8,0,0,1,1.89,4.31,7.84,7.84,0,0,1-.12,2.4,9.29,9.29,0,0,1,1.83,2.21,7.3,7.3,0,0,1,1,3.92,7,7,0,0,1-1.43,4,9.66,9.66,0,0,1-2.4,2.25,8.87,8.87,0,0,1-1.53,5.5A7.15,7.15,0,0,1,68.63,71a7,7,0,0,1-8.2,4.9,5.7,5.7,0,0,1-3.58-3A6.13,6.13,0,0,1,52.76,76a6.37,6.37,0,0,1-5.16-1.29A6.24,6.24,0,0,1,45.27,71a6.42,6.42,0,0,1-1.81-.34,8.33,8.33,0,0,1-3.54-2.43A8.13,8.13,0,0,1,38,64.1a7.56,7.56,0,0,1,.09-2.63,9.15,9.15,0,0,1-1.67-2,7.54,7.54,0,0,1-1.14-4,7.16,7.16,0,0,1,1.45-4.19A9.87,9.87,0,0,1,39,49.09c0-.27,0-.55,0-.82A8.77,8.77,0,0,1,41,43a6.9,6.9,0,0,1,4.28-2.55h0a5.07,5.07,0,0,1,.22-.76,6.6,6.6,0,0,1,2.87-3.34,6.22,6.22,0,0,1,4.39-.77,6,6,0,0,1,3.93,2.86,6.06,6.06,0,0,1,3.94-2.86,6.17,6.17,0,0,1,4,.58,6.79,6.79,0,0,1,2.88,2.76,6,6,0,0,1,.56,1.48Zm-9.83.89V68.82a1.38,1.38,0,0,1,.25.58c.39,2.4,1.43,3.56,2.61,3.88a4,4,0,0,0,3.31-.76,3.65,3.65,0,0,0,1.68-2.8c0-1.22-.88-2.61-3.28-3.83a1.33,1.33,0,1,1,1.21-2.36c2.66,1.35,4,3,4.52,4.71a4.72,4.72,0,0,0,2.37-1.87A6.29,6.29,0,0,0,72,62a1.33,1.33,0,0,1,.63-1.43,7.61,7.61,0,0,0,2.33-2,4.32,4.32,0,0,0,.91-2.5,4.73,4.73,0,0,0-.68-2.51,6.74,6.74,0,0,0-1.79-2A1.35,1.35,0,0,1,72.84,50a4.92,4.92,0,0,0,.25-2.18A5.44,5.44,0,0,0,71.82,45a5.63,5.63,0,0,0-2.43-1.7,3.51,3.51,0,0,0-1.37-.2,6.25,6.25,0,0,1-1.29,2.35,1.32,1.32,0,1,1-2-1.7,3,3,0,0,0,.49-3.54,4,4,0,0,0-1.73-1.65,3.53,3.53,0,0,0-2.29-.35c-1.13.23-2.22,1.17-2.9,3.11Zm6.78,9a1.33,1.33,0,0,1,1.56-2.15c.28.2.53.41.77.62a8.78,8.78,0,0,1,2.91,5.88,9.34,9.34,0,0,1-1.78,6.26c-.19.26-.41.52-.66.8a1.33,1.33,0,0,1-2-1.78,7.37,7.37,0,0,0,.47-.58,6.76,6.76,0,0,0,1.29-4.49,6.14,6.14,0,0,0-2-4.11,5.7,5.7,0,0,0-.55-.45ZM55.6,42.7h0l0,0h0l-.06-.1h0l0,0h0v0h0v0h0v0h0v0h0v0h0v0h0c-.64-2.59-1.89-3.78-3.18-4a3.63,3.63,0,0,0-2.52.47A3.92,3.92,0,0,0,48,40.62a3.28,3.28,0,0,0,1.18,3.62,1.33,1.33,0,0,1-1.81,1.95,6.75,6.75,0,0,1-2-3,4.36,4.36,0,0,0-2.33,1.56,6.11,6.11,0,0,0-1.36,3.66,7.25,7.25,0,0,0,.07,1.21,1.34,1.34,0,0,1-.62,1.31,8,8,0,0,0-2.27,2,4.52,4.52,0,0,0-.93,2.64,4.81,4.81,0,0,0,.75,2.56,6.43,6.43,0,0,0,1.67,1.79,1.34,1.34,0,0,1,.5,1.53,5.08,5.08,0,0,0-.23,2.32,5.52,5.52,0,0,0,1.3,2.78,5.74,5.74,0,0,0,2.38,1.65,3.93,3.93,0,0,0,1.09.2c.52-1.72,2-3.45,4.68-4.84a1.33,1.33,0,1,1,1.21,2.36c-2.52,1.29-3.47,2.77-3.47,4.07a3.42,3.42,0,0,0,1.41,2.61,3.77,3.77,0,0,0,3,.79c1.29-.27,2.54-1.46,3.18-4.06a1.35,1.35,0,0,1,.2-.44V42.7Zm-9.88,6.37A1.33,1.33,0,1,1,47.58,51a6.17,6.17,0,0,0-1.85,4.17,6.71,6.71,0,0,0,1.52,4.52,1.33,1.33,0,0,1-2.08,1.67A9.46,9.46,0,0,1,43.07,55a8.83,8.83,0,0,1,2.65-6Zm-1.14,53.11a2.26,2.26,0,0,1-.48-4.48c-1.29-7.33-4.82-11.36-8.55-15.63-2.9-3.32-5.91-6.77-8.29-11.85a35.77,35.77,0,0,1-3.58-15.54,36.57,36.57,0,0,1,4.38-16.62l.06-.1h0A31.55,31.55,0,0,1,43.5,24.51a34,34,0,0,1,16-2.29,36.7,36.7,0,0,1,15.28,4.91A31.55,31.55,0,0,1,88.13,42.25,33.57,33.57,0,0,1,90.65,54a34.15,34.15,0,0,1-2.59,13.8c-2.65,6.55-6.15,10.62-9.42,14.43-3,3.5-5.82,6.77-7.38,11.77a2.26,2.26,0,0,1-.21,4.43l-.76.11c-.14,1.09-.23,2.25-.27,3.5l.25,0a2.26,2.26,0,1,1,.62,4.48l-.77.11,0,.45h0a17.91,17.91,0,0,1,0,2.53l.13,0a2.27,2.27,0,0,1,.55,4.5l-1.68.2a13.51,13.51,0,0,1-5.83,6.91,12.73,12.73,0,0,1-5.58,1.7A11.59,11.59,0,0,1,52,121.73a12.11,12.11,0,0,1-5.26-5.11,2.25,2.25,0,0,1-1.22-1.74,2.2,2.2,0,0,1,.07-.89,19.64,19.64,0,0,1-.87-3.8h-.07a2.26,2.26,0,0,1-.62-4.48l.54-.07c0-1.23,0-2.38,0-3.47Zm3.73-5.08L66.7,94.49c1.58-6.67,5-10.67,8.74-15,3.06-3.56,6.33-7.36,8.71-13.26a29.94,29.94,0,0,0,2.3-12.12,29.2,29.2,0,0,0-2.23-10.28A27.26,27.26,0,0,0,72.64,30.76a32.33,32.33,0,0,0-13.51-4.35,29.89,29.89,0,0,0-14.05,2A27.43,27.43,0,0,0,31.76,40.07a32.35,32.35,0,0,0-3.87,14.64,31.57,31.57,0,0,0,3.18,13.73A44.91,44.91,0,0,0,38.72,79.3c4.21,4.82,8.19,9.38,9.59,17.8ZM66,99.16,48.78,101.6c0,1.09.07,2.24,0,3.45l17-2.41c0-1.23.07-2.39.17-3.48Zm-17,10.44a16.21,16.21,0,0,0,.53,2.52l16.45-2a13.67,13.67,0,0,0,.06-2.73h0v-.19l-17,2.42Zm2.87,6.79a7.56,7.56,0,0,0,2.09,1.55,7.4,7.4,0,0,0,3.66.72,8.53,8.53,0,0,0,3.71-1.14,9.07,9.07,0,0,0,2.86-2.64l-12.32,1.51ZM110.68,50a2.8,2.8,0,0,1,3.08,2.5,2.81,2.81,0,0,1-2.51,3.08l-8.61.91a2.78,2.78,0,0,1-2.06-.62,2.81,2.81,0,0,1,1.49-5l8.61-.9ZM94.48,15a2.85,2.85,0,0,1,1.79-1.19A2.8,2.8,0,0,1,99.57,16a2.78,2.78,0,0,1-.42,2.1l-4.81,7.2a2.8,2.8,0,0,1-1.79,1.2,2.84,2.84,0,0,1-2.1-.43,2.78,2.78,0,0,1-1.2-1.79,2.75,2.75,0,0,1,.43-2.1l4.8-7.2ZM57.3,2.74a2.85,2.85,0,0,1,.86-2,2.81,2.81,0,0,1,4.74,2.1l-.21,8.65a2.81,2.81,0,1,1-5.61-.12l.22-8.66ZM14,16.64l0,0a2.81,2.81,0,0,1-.09-3.92l0,0a2.78,2.78,0,0,1,1.9-.85,2.84,2.84,0,0,1,2,.75c2.09,2,4.23,3.95,6.33,5.91a2.83,2.83,0,0,1,.9,2,2.87,2.87,0,0,1-.75,2l0,0a2.81,2.81,0,0,1-3.92.09c-2-1.93-4.25-4.09-6.35-5.92Z"/></svg>
                  </span> */}
                      <Lightbulb className="size-4 hover:text-orange-500" />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="w-100">
                    <div className="space-y-4">
                      <div className="flex flex-col gap-2">
                        <div className="flex justify-between items-center">
                          <div className="text-sm font-medium">添加一点想法...</div>
                          <button
                            onClick={(e) => {
                              e.preventDefault();
                              useThoughtStore.getState().fetchThoughts(); // 修正为正确的刷新方法
                            }}
                            className="p-1 hover:bg-gray-100 rounded-full dark:hover:bg-neutral-700
                                     transition-colors duration-200"
                          >
                            <RefreshCw className="h-4 w-4 text-gray-500 dark:text-neutral-400" />
                          </button>
                        </div>
                        <div className="relative">
                          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            value={thoughtsFilter}
                            onChange={(e) => setThoughtsFilter(e.target.value)}
                            placeholder="搜索想法..."
                            className="w-full pl-8 pr-4 py-1 text-sm border rounded-md
                             focus:outline-none focus:ring-1 focus:ring-blue-500
                             dark:bg-neutral-800 dark:border-neutral-700
                             dark:text-neutral-200 dark:placeholder-neutral-400"
                          />
                        </div>
                      </div>
                      <div className="max-h-[300px] overflow-y-auto space-y-2 pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                        {filteredThoughts.map((thought) => (
                          <button
                            key={thought.id}
                            onClick={() =>
                              handleThoughtSelect(thought.content, thought.type)
                            }
                            className="w-full text-left p-2 text-sm rounded-lg hover:bg-gray-100
                              dark:hover:bg-neutral-800 flex items-center justify-between gap-2"
                          >
                            <span className="line-clamp-1">{thought.title.replace(/^#+\s*/g, '')}</span>
                            <span className="text-xs text-gray-500">
                              {thought.type === "thought" ? "想法" : "摘要"}
                            </span>
                          </button>
                        ))}
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>

                {/* context: weibo */}
                <Popover open={isWeiboOpen} onOpenChange={setIsWeiboOpen}>
                  <PopoverTrigger asChild>
                    <button
                      type="button"
                      className="inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                        text-gray-500 hover:bg-white focus:z-10 focus:outline-none
                        focus:bg-gray-100 dark:text-neutral-500 dark:hover:bg-neutral-700
                        dark:focus:bg-neutral-700"
                    >
                      <Bird className="size-4 hover:text-green-500" />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="w-96 p-0">
                    <div className="border-b px-4 py-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 rounded-t-lg">
                      <h4 className="text-sm font-medium text-center">添加微博到对话</h4>
                    </div>
                    <div className="p-4 space-y-4">
                      <div className="flex flex-col gap-2">
                        <div className="relative">
                          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            value={weiboFilter}
                            onChange={(e) => setWeiboFilter(e.target.value)}
                            placeholder="搜索微博内容..."
                            className="w-full pl-8 pr-4 py-1 text-sm border rounded-md
                          focus:outline-none focus:ring-1 focus:ring-blue-500
                          dark:bg-neutral-800 dark:border-neutral-700
                          dark:text-neutral-200 dark:placeholder-neutral-400"
                          />
                        </div>
                      </div>
                      {isWeiboLoading ? (
                        <div className="flex justify-center p-4">
                          <Loader2 className="h-6 w-6 animate-spin" />
                        </div>
                      ) : (
                        <div className="max-h-[250px] overflow-y-auto space-y-2 pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                          {filteredWeibos.map((post) => (
                            <button
                              key={post.post.id}
                              onClick={() => handleWeiboSelect(post)}
                              className="w-full text-left p-2 text-sm rounded-lg hover:bg-gray-100
                            dark:hover:bg-neutral-800 flex items-center justify-between"
                            >
                              <span className="line-clamp-1">{post.post.content.slice(0, 40)}{post.post.content.length > 40 ? '...' : ''}</span>
                              <span className="text-xs text-gray-500 ml-2">{
                                (() => {
                                  // 确保时间戳被正确解析为UTC时间
                                  const utcTimestamp = post.post.timestamp.includes('Z') || post.post.timestamp.includes('+') || post.post.timestamp.includes('-')
                                    ? post.post.timestamp
                                    : post.post.timestamp + 'Z';
                                  return new Date(utcTimestamp).toLocaleDateString();
                                })()
                              }</span>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                    <div className="border-t px-4 py-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 rounded-b-lg">
                      <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                        选择一条微博内容作为上下文
                      </p>
                    </div>
                  </PopoverContent>
                </Popover>

              </div>
            </div>

            <div className="flex items-center gap-x-1">
              {/* Deep Search图标按钮 */}
              <button
                type="button"
                onClick={handleDeepSearch}
                disabled={!input.trim() || disabled}
                className={`inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                         focus:z-10 focus:outline-none disabled:opacity-50 disabled:pointer-events-none
                         group/ds-toggle transition-colors duration-200 ${isDeepSearchActive
                    ? 'bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50'
                    : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50 dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20 dark:focus:text-blue-400 dark:focus:bg-blue-900/20'
                  }`}
                title={isDeepSearchActive ? 'Deep Search已激活，提交时将进行深度研究' : '点击激活Deep Search模式'}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="stroke-[2] text-fg-secondary group-hover/ds-toggle:text-fg-primary">
                  <path d="M19.2987 8.84667C15.3929 1.86808 5.44409 5.76837 7.08971 11.9099C8.01826 15.3753 12.8142 14.8641 13.2764 12.8592C13.6241 11.3504 10.2964 12.3528 10.644 10.844C11.1063 8.839 15.9022 8.32774 16.8307 11.793C18.5527 18.2196 7.86594 22.4049 4.71987 15.2225" strokeWidth="5" strokeLinecap="round" className="stroke-black/10 dark:stroke-white/20 transition-[opacity,transform] duration-200 origin-center opacity-0 scale-0"></path>
                  <path d="M2 13.8236C4.5 22.6927 18 21.3284 18 14.0536C18 9.94886 11.9426 9.0936 10.7153 11.1725C9.79198 12.737 14.208 12.6146 13.2847 14.1791C12.0574 16.2581 6 15.4029 6 11.2982C6 3.68585 20.5 2.2251 22 11.0945" stroke="currentColor" className="transition-transform duration-200 eas-out origin-center rotate-0"></path>
                </svg>
                {/* Deep Search */}
              </button>

              {/* 提示词优化按钮 */}
              <button
                type="button"
                onClick={handlePromptOptimization}
                disabled={!input.trim() || disabled}
                className="inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                         text-gray-600 hover:text-blue-600 hover:bg-blue-50
                         focus:z-10 focus:outline-none focus:text-blue-600 focus:bg-blue-50
                         disabled:opacity-50 disabled:pointer-events-none
                         dark:text-gray-400 dark:hover:text-blue-400 dark:hover:bg-blue-900/20
                         dark:focus:text-blue-400 dark:focus:bg-blue-900/20"
                title="提示词优化"
              >
                <Wand2 className="size-4" />
              </button>

              <div className="relative inline-flex">
                <DropdownMenu
                  open={isModelDropdownOpen}
                  onOpenChange={setIsModelDropdownOpen}
                >
                  <DropdownMenuTrigger asChild>
                    <button
                      type="button"
                      className="py-1.5 px-2 inline-flex items-center gap-x-1 text-sm font-medium
               rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm
               hover:bg-gray-500 focus:outline-none focus:bg-gray-50
               disabled:opacity-50 disabled:pointer-events-none
               dark:bg-neutral-800 dark:border-neutral-700 dark:text-white
               dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                      title={selectedModel ?
                        (() => {
                          const model = availableModels.find(m => m.id === selectedModel);
                          return model ? `${model.id}（${model.providerName}）` : "Deep Think";
                        })()
                        : "Deep Think"}
                    >
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="stroke-[2]"
                      >
                        <path
                          d="M19 9C19 12.866 15.866 17 12 17C8.13398 17 4.99997 12.866 4.99997 9C4.99997 5.13401 8.13398 3 12 3C15.866 3 19 5.13401 19 9Z"
                          className={`origin-center transition-all duration-100 ${selectedModel
                            ? `${allModels.find(m => m.name === selectedModel)?.status === "ready"
                              ? "fill-green-100 dark:fill-green-400/40"
                              : allModels.find(m => m.name === selectedModel)?.status === "training"
                                ? "fill-yellow-100 dark:fill-yellow-400/40"
                                : "fill-red-100 dark:fill-red-400/40"
                            } scale-100 opacity-100`
                            : "scale-0 opacity-0"
                            }`}
                        />
                        <path
                          d="M15 16.1378L14.487 15.2794L14 15.5705V16.1378H15ZM8.99997 16.1378H9.99997V15.5705L9.51293 15.2794L8.99997 16.1378ZM18 9C18 11.4496 16.5421 14.0513 14.487 15.2794L15.5129 16.9963C18.1877 15.3979 20 12.1352 20 9H18ZM12 4C13.7598 4 15.2728 4.48657 16.3238 5.33011C17.3509 6.15455 18 7.36618 18 9H20C20 6.76783 19.082 4.97946 17.5757 3.77039C16.0931 2.58044 14.1061 2 12 2V4ZM5.99997 9C5.99997 7.36618 6.64903 6.15455 7.67617 5.33011C8.72714 4.48657 10.2401 4 12 4V2C9.89382 2 7.90681 2.58044 6.42427 3.77039C4.91791 4.97946 3.99997 6.76783 3.99997 9H5.99997ZM9.51293 15.2794C7.4578 14.0513 5.99997 11.4496 5.99997 9H3.99997C3.99997 12.1352 5.81225 15.3979 8.48701 16.9963L9.51293 15.2794ZM9.99997 19.5001V16.1378H7.99997V19.5001H9.99997ZM10.5 20.0001C10.2238 20.0001 9.99997 19.7763 9.99997 19.5001H7.99997C7.99997 20.8808 9.11926 22.0001 10.5 22.0001V20.0001ZM13.5 20.0001H10.5V22.0001H13.5V20.0001ZM14 19.5001C14 19.7763 13.7761 20.0001 13.5 20.0001V22.0001C14.8807 22.0001 16 20.8808 16 19.5001H14ZM14 16.1378V19.5001H16V16.1378H14Z"
                          fill="currentColor"
                        />
                        <path d="M9 16.0001H15" stroke="currentColor" />
                        <path
                          d="M12 16V12"
                          stroke="currentColor"
                          strokeLinecap="square"
                        />
                        <g
                          className={`transition-all duration-100 ease-in-out ${selectedModel ? "opacity-100" : "opacity-0"
                            }`}
                        >
                          <path
                            d="M20 7L19 8"
                            stroke="currentColor"
                            strokeLinecap="round"
                            className="translate-x-[3px] -translate-y-[3px]"
                          />
                          <path
                            d="M20 9L19 8"
                            stroke="currentColor"
                            strokeLinecap="round"
                            className="translate-x-[3px] translate-y-[3px]"
                          />
                          <path
                            d="M4 7L5 8"
                            stroke="currentColor"
                            strokeLinecap="round"
                            className="-translate-x-[3px] -translate-y-[3px]"
                          />
                          <path
                            d="M4 9L5 8"
                            stroke="currentColor"
                            strokeLinecap="round"
                            className="-translate-x-[3px] translate-y-[3px]"
                          />
                        </g>
                      </svg>
                      <ChevronDown
                        className={`size-3 transition-transform ${isModelDropdownOpen ? "rotate-180" : ""
                          }`}
                      />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="min-w-48 max-h-80 overflow-y-auto">
                    {/* 系统默认模型 */}
                    {availableDefaultModels.length > 0 && (
                      <>
                        <div className="px-3 py-2 text-xs font-semibold text-emerald-600 dark:text-emerald-400 bg-emerald-50 dark:bg-emerald-950/30 border-b border-emerald-200 dark:border-emerald-800">
                          🤖 系统默认模型
                        </div>
                        {availableDefaultModels.map((model) => (
                          <DropdownMenuItem
                            key={`default-${model.id}`}
                            onClick={() => handleModelSelect(model.id)}
                            className={`mx-1 my-0.5 rounded-md ${selectedModel === model.id
                                ? 'bg-emerald-100 dark:bg-emerald-900/50 text-emerald-700 dark:text-emerald-300'
                                : 'hover:bg-emerald-50 dark:hover:bg-emerald-950/20'
                              }`}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span className="truncate font-medium text-emerald-600 dark:text-emerald-400">
                                {model.name}
                              </span>
                              <span className="text-xs text-emerald-500 dark:text-emerald-500 ml-2">
                                {/* 免费 */}
                              </span>
                            </div>
                            {selectedModel === model.id && (
                              <div className="w-2 h-2 rounded-full bg-emerald-500 ml-2 flex-shrink-0" />
                            )}
                          </DropdownMenuItem>
                        ))}

                        {/* 分隔线 */}
                        {availableModels.length > 0 && (
                          <div className="h-px my-2 bg-gray-200 dark:bg-neutral-700" />
                        )}
                      </>
                    )}

                    {/* 用户自定义模型 */}
                    {availableModels.length > 0 && (
                      <>
                        <div className="px-3 py-2 text-xs font-semibold text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-950/30 border-b border-blue-200 dark:border-blue-800">
                          👤 用户模型
                        </div>
                        {(() => {
                          // 按提供商名称对模型进行分组
                          const modelsByProvider: Record<string, typeof availableModels> = {};

                          availableModels.forEach(model => {
                            if (!modelsByProvider[model.providerName]) {
                              modelsByProvider[model.providerName] = [];
                            }
                            modelsByProvider[model.providerName].push(model);
                          });

                          // 渲染分组后的模型列表
                          return Object.entries(modelsByProvider).map(([providerName, models], index) => (
                            <div key={providerName}>
                              {/* 提供商分组标题 */}
                              <div className="px-3 py-1.5 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/50">
                                {providerName}
                              </div>

                              {/* 该提供商下的模型列表 */}
                              {models.map((model) => (
                                <DropdownMenuItem
                                  key={model.id}
                                  onClick={() => handleModelSelect(model.id)}
                                  className={`mx-1 my-0.5 rounded-md ${selectedModel === model.id
                                      ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                                      : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
                                    }`}
                                >
                                  <div className="flex items-center justify-between w-full">
                                    <span className="truncate">
                                      {model.name}
                                    </span>
                                    {selectedModel === model.id && (
                                      <div className="w-2 h-2 rounded-full bg-blue-500 ml-2 flex-shrink-0" />
                                    )}
                                  </div>
                                </DropdownMenuItem>
                              ))}

                              {/* 提供商之间的分隔线 */}
                              {index < Object.entries(modelsByProvider).length - 1 && (
                                <div className="h-px my-1 bg-gray-100 dark:bg-gray-800" />
                              )}
                            </div>
                          ));
                        })()
                        }
                      </>
                    )}

                    {/* 如果没有任何模型 */}
                    {availableDefaultModels.length === 0 && availableModels.length === 0 && (
                      <div className="px-3 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                        暂无可用模型
                      </div>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <button
                type="button" // 改为 button 类型，避免自动提交表单
                onClick={(e) => {
                  e.preventDefault(); // 阻止表单提交
                  if (isRequesting) {
                    handleStopRequest();
                  } else {
                    handleSubmit(e);
                  }
                }}
                disabled={(!input.trim() || disabled) && !isRequesting} // 修改禁用条件，允许在请求中点击停止
                className={`inline-flex shrink-0 justify-center items-center size-8 rounded-lg
                            text-white ${isRequesting ? 'bg-red-600 hover:bg-red-500' : 'bg-blue-600 hover:bg-blue-500'}
                            focus:z-10 focus:outline-none focus:bg-blue-500 disabled:opacity-50
                            disabled:hover:bg-blue-600 relative overflow-hidden`}
              >
                {isRequesting ? (
                  <>
                    <Loader2 className="size-3 animate-spin relative z-10" />
                    <span className="absolute inset-0 bg-red-700 animate-pulse opacity-30"></span>
                  </>
                ) : input.trim() && !disabled ? (
                  <>
                    <Send className="size-3 relative z-10" />
                    <span className="absolute inset-0 bg-blue-700 animate-pulse opacity-0 group-hover:opacity-30 transition-opacity"></span>
                  </>
                ) : (
                  <Send className="size-3" />
                )}
              </button>
            </div>
          </div>
        </div>
      </form>

      {/* 提示词优化对话框 */}
      <PromptOptimizationDialog
        open={isPromptOptOpen}
        onOpenChange={setIsPromptOptOpen}
        originalPrompt={input}
        modelInfo={{
          baseUrl: getSelectedModelInfo()?.baseUrl || "",
          model: getSelectedModelInfo()?.model || "",
          apiKey: (() => {
            // 获取当前选中模型的 API Key
            if (!selectedModel || selectedModel === "Deep Think") {
              return undefined;
            }

            const defaultProvider = defaultModels.find(p =>
              p.models.some(m => m.id === selectedModel)
            );

            if (defaultProvider) {
              return defaultProvider.apiKey;
            }

            const provider = providers.find(p =>
              p.models.some(m => m.id === selectedModel)
            );

            return provider?.apiKey;
          })(),
          displayName: (() => {
            if (!selectedModel || selectedModel === "Deep Think") {
              return "Deep Think";
            }

            const defaultProvider = defaultModels.find(p =>
              p.models.some(m => m.id === selectedModel)
            );

            if (defaultProvider) {
              return `${selectedModel}（${defaultProvider.name}）`;
            }

            const provider = providers.find(p =>
              p.models.some(m => m.id === selectedModel)
            );

            return provider ? `${selectedModel}（${provider.name}）` : selectedModel;
          })()
        }}
        onApplyOptimized={handleApplyOptimized}
      />
    </>
  );
}

