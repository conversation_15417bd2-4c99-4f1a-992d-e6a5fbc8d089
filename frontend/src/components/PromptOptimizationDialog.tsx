import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "./ui/dialog";
import { Button } from "./ui/button";
import { Textarea } from "./ui/textarea";
import { PROMPTOPT_EXPERT_PROMPT } from '@/lib/prompts/promptopt';

// 模型信息接口
interface ModelInfo {
  baseUrl: string;
  model: string;
  apiKey?: string;
  displayName: string;
}

interface PromptOptimizationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  originalPrompt: string;
  modelInfo: ModelInfo; // 直接接收模型信息，不再内部获取
  onApplyOptimized: (optimizedPrompt: string) => void;
}

export function PromptOptimizationDialog({
  open,
  onOpenChange,
  originalPrompt,
  modelInfo, // 使用传入的模型信息
  onApplyOptimized
}: PromptOptimizationDialogProps) {
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizedPrompt, setOptimizedPrompt] = useState("");

  // 构建API端点
  const buildEndpoint = (baseUrl: string): string => {
    const cleanUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    return baseUrl.endsWith('/') 
      ? `${baseUrl}chat/completions`
      : `${cleanUrl}/v1/chat/completions`;
  };

  // 处理流式响应
  const processStreamResponse = async (reader: ReadableStreamDefaultReader<Uint8Array>) => {
    const decoder = new TextDecoder();
    let result = "";
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = decoder.decode(value);
      const lines = chunk.split('\n').filter(line => line.startsWith('data: '));
      
      for (const line of lines) {
        const data = line.slice(6);
        if (data === '[DONE]') continue;
        
        try {
          const parsed = JSON.parse(data);
          const content = parsed.choices?.[0]?.delta?.content;
          if (content) {
            result += content;
            setOptimizedPrompt(result);
          }
        } catch {
          // 忽略解析错误
        }
      }
    }
  };

  // 执行提示词优化
  const handleOptimization = async () => {
    if (!originalPrompt.trim()) return;

    setIsOptimizing(true);
    setOptimizedPrompt("");

    try {
      if (!modelInfo.baseUrl) {
        throw new Error('LLM base URL is not configured');
      }
      
      if (!modelInfo.apiKey) {
        throw new Error('API密钥未配置，请检查模型配置');
      }

      const optimizationPrompt = `${PROMPTOPT_EXPERT_PROMPT}\n\n用户原始输入：${originalPrompt}\n\n注意：用户主要根据提出主题科学问题研究，尽可能从机理、机制和方法论层面讨论，不涉及应用和技术，直接返回优化后的提示词内容。`;
      
      const response = await fetch(buildEndpoint(modelInfo.baseUrl), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${modelInfo.apiKey}`
        },
        body: JSON.stringify({
          model: modelInfo.model,
          messages: [{ role: 'user', content: optimizationPrompt }],
          stream: true,
          temperature: 0.7,
          max_tokens: 4000
        })
      });
      
      if (!response.ok) {
        throw new Error(`优化请求失败: ${response.status} ${response.statusText}`);
      }
      
      const reader = response.body?.getReader();
      if (reader) {
        await processStreamResponse(reader);
      }
      
    } catch (error) {
      console.error('提示词优化失败:', error);
      setOptimizedPrompt('优化失败，请稍后重试。错误信息：' + (error as Error).message);
    } finally {
      setIsOptimizing(false);
    }
  };

  // 过滤优化后的提示词内容
  const filterOptimizedPrompt = (text: string): string => {
    let filtered = text.replace(/<think[^>]*>[\s\S]*?<\/think>/gi, '');

    // 匹配从 **Your Optimized Prompt:** 到 **Target AI:** 之间的内容
    const optimizedPromptMatch = filtered.match(/\*\*Your Optimized Prompt:\*\*([\s\S]*?)(?=\*\*Key Improvements:\*\*)/i);

    if (optimizedPromptMatch) {
      return optimizedPromptMatch[1].trim();
    }

    // 如果没有找到 Target AI 标记，则使用原来的逻辑作为后备
    const fallbackMatch = filtered.match(/\*\*Your Optimized Prompt:\*\*[\s\S]*?(?=\n\n|\*\*|$)/i);
    if (fallbackMatch) {
      return fallbackMatch[0]
        .replace(/\*\*Your Optimized Prompt:\*\*/i, '')
        .trim();
    }

    return filtered.trim();
  };

  // 应用优化后的提示词
  const handleApply = () => {
    if (optimizedPrompt.trim()) {
      const filteredPrompt = filterOptimizedPrompt(optimizedPrompt);
      onApplyOptimized(filteredPrompt);
      onOpenChange(false);
    }
  };

  // 当对话框打开时自动开始优化
  useEffect(() => {
    if (open && originalPrompt.trim() && !optimizedPrompt && !isOptimizing) {
      handleOptimization();
    }
  }, [open, originalPrompt]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>提示词优化</DialogTitle>
          <DialogDescription>
            使用AI专家系统优化您的提示词，让AI更好地理解您的需求。
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden flex flex-col gap-4">
          <div>
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              原始提示词：
            </label>
            <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border text-sm max-h-32 overflow-y-auto">
              {originalPrompt}
            </div>
          </div>
          
          <div className="flex-1 overflow-hidden flex flex-col">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              优化后的提示词：
            </label>
            <div className="flex-1 overflow-auto">
              <Textarea
                value={optimizedPrompt}
                onChange={(e) => setOptimizedPrompt(e.target.value)}
                placeholder={isOptimizing ? "正在优化中..." : "优化后的提示词将显示在这里"}
                className="min-h-[200px] resize-none"
                readOnly={isOptimizing}
              />
            </div>
          </div>
          
          {/* {isOptimizing && (
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <Loader2 className="size-4 animate-spin" />
              正在使用 {modelInfo.displayName} 优化提示词...
            </div>
          )} */}
        </div>
        
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isOptimizing}
          >
            取消
          </Button>
          <Button
            onClick={handleOptimization}
            variant="outline"
            disabled={isOptimizing || !originalPrompt.trim()}
          >
            重新优化
          </Button>
          <Button
            onClick={handleApply}
            disabled={isOptimizing || !optimizedPrompt.trim()}
          >
            应用优化
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}