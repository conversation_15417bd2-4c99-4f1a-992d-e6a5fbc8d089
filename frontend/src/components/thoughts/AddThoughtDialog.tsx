import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { useState } from "react";
import { useThoughtStore } from "@/store/thoughtsStore";
import { List } from "lucide-react";
import { useSelectedTextStore } from "@/store/selectedTextStore";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
// import { createClient } from '@supabase/supabase-js';

export function AddThoughtDialog({
  open,
  onOpenChange,
  initialContent = '',
  initialSource = 'Deep Thinker',
  onSuccess
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialContent?: string;
  initialSource?: string;
  onSuccess?: () => void;
}) {
  const [content, setContent] = useState(initialContent);
  const [source, setSource] = useState(initialSource);
  const [contentType, setContentType] = useState<string>('idea'); // 添加内容类型状态
  const { selectedTexts, clearSelectedTexts } = useSelectedTextStore();
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  const handleAddSelectedTexts = () => {
    const textsToAdd = selectedTexts
      .filter(text => selectedIds.includes(text.id))
      .map(text => text.content)
      .join('\n\n');

    setContent(prev => prev ? `${prev}\n\n${textsToAdd}` : textsToAdd);
    setSelectedIds([]); // 重置选择
  };

  const { addThought } = useThoughtStore();

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      try {
        // 获取 token
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('未登录，无法添加书签');
        }

        // 获取第一行作为标题并清理特殊字符
        const firstLine = content.split('\n')[0];
        const cleanTitle = firstLine.replace(/[#*`~_>]/g, '').trim();

        // 使用 store 中的 addThought 方法添加书签
        await addThought({
          title: cleanTitle, // 使用清理后的标题
          desc: content,
          url: source,
          bookmark_type: contentType, // 使用选择的内容类型作为书签类型
          content_type: contentType, // 使用选择的内容类型
          is_public: false
        });

        // 成功后关闭对话框并清空表单
        onOpenChange(false);
        setContent("");
        setSource("");
        clearSelectedTexts();
        if (onSuccess) onSuccess();
      } catch (error) {
        console.error('添加书签失败:', error);
      }
    };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>记录灵感</DialogTitle>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="icon" className="relative mr-8">
                  <List className="h-4 w-4" />
                  {selectedTexts.length > 0 && (
                    <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-blue-500 text-[10px] text-white flex items-center justify-center">
                      {selectedTexts.length}
                    </span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80">
                <div className="space-y-4">
                  <div className="text-sm font-medium">已选择的文本</div>
                  {selectedTexts.length > 0 ? (
                    <>
                      <div className="space-y-2 max-h-[200px] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
                        {selectedTexts.map((text) => (
                          <div key={text.id} className="flex items-start gap-2">
                            <Checkbox
                              id={text.id}
                              checked={selectedIds.includes(text.id)}
                              onCheckedChange={(checked) => {
                                setSelectedIds(prev =>
                                  checked
                                    ? [...prev, text.id]
                                    : prev.filter(id => id !== text.id)
                                );
                              }}
                            />
                            <label
                              htmlFor={text.id}
                              className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {text.content}
                            </label>
                          </div>
                        ))}
                      </div>
                      <Button
                        size="sm"
                        className="w-full"
                        onClick={handleAddSelectedTexts}
                      >
                        添加选中文本
                      </Button>
                    </>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      暂无选中的文本
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
          </div>
          <DialogDescription>
            在这里添加、编辑你的灵感，随时捕捉思维的火花。
          </DialogDescription>
        </DialogHeader>
        {/* 其他表单内容保持不变 */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="content">内容</Label>
              <Textarea
                id="content"
                placeholder="在这里记录你的想法..."
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="min-h-[150px] max-h-[300px] overflow-y-auto"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="source">来源</Label>
              <Input
                id="source"
                placeholder="来源网址或名称"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                onFocus={(e) => {
                  // 获得焦点时全选文本
                  e.target.select();
                }}
                onBlur={(e) => {
                  const value = e.target.value.trim();
                  // 检查是否为网址（简单的URL格式验证）
                  const urlPattern = /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/;
                  if (urlPattern.test(value)) {
                    // 如果是网址但没有http或https前缀，添加https://
                    if (!value.startsWith('http://') && !value.startsWith('https://')) {
                      setSource(`https://${value}`);
                    }
                  }
                }}
              />
            </div>

            {/* 添加内容类型下拉菜单 */}
            <div className="space-y-2">
              <Label htmlFor="contentType">想法来自</Label>
              <Select
                value={contentType}
                onValueChange={setContentType}
              >
                <SelectTrigger id="contentType">
                  <SelectValue placeholder="选择内容类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="idea">灵光一现</SelectItem>
                  <SelectItem value="weibo">微博</SelectItem>
                  <SelectItem value="wechat">微信</SelectItem>
                  <SelectItem value="paper">论文</SelectItem>
                  <SelectItem value="ppt">网页</SelectItem>
                  <SelectItem value="reddit">Reddit</SelectItem>
                  {/* <SelectItem value="github">Github</SelectItem> */}
                  <SelectItem value="x">X</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button type="submit">保存</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}