// 文档库中的文档预览组件

import { useState, useEffect } from "react";

import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import { convertSupabaseUrl, convertUrlForDocker } from "@/utils/url-utils";

// 辅助函数：估算文本的token数量
function estimateTokenCount(text: string): number {
  if (!text) return 0;

  // 简单估算：
  // - 英文单词约0.75个token
  // - 中文字符约1.5-2个token
  // - 标点符号和空格约0.25-0.5个token

  // 计算中文字符数量
  const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || [];
  const chineseCount = chineseChars.length;

  // 计算英文单词数量（粗略估计）
  const englishWords = text.match(/[a-zA-Z]+/g) || [];
  const englishCount = englishWords.length;

  // 计算数字、标点和空格
  const otherChars = text.length - chineseCount - englishWords.join('').length;

  // 估算token数量
  // 中文字符 * 1.5 + 英文单词 * 0.75 + 其他字符 * 0.25
  return Math.ceil(chineseCount * 1.5 + englishCount * 0.75 + otherChars * 0.25);
}
import { PDFViewer } from "@/components/ui/pdf-viewer";
import { MessageSquare, FileText, FolderTree, Bird, FileCode, Loader2, X } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { Document } from "@/store/documentStore";
import API from "@/config/api";
import { useLLMStore } from '@/store/llmStore';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import { MarkdownComponents, convertMathFormula } from "@/components/markdown/MarkdownComponents";


interface PreviewDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: Document | null;
  className?: string; // 添加这行
}

export function PreviewDocumentDialog({
  open,
  document: docData,
  className = '' // 添加默认值
}: PreviewDocumentDialogProps) {
  const [isLoadingPdf, setIsLoadingPdf] = useState(false);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [documentDetails, setDocumentDetails] = useState<Document | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [isExtractingTOC, setIsExtractingTOC] = useState(false);
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
  const [conversionStatus, setConversionStatus] = useState({
    extract: 'pending',
    summary: 'pending',
    save: 'pending'
  });
  const [activeTab, setActiveTab] = useState<string>("pdf");
  const [markdownContent, setMarkdownContent] = useState<string>("");
  const [isLoadingMarkdown, setIsLoadingMarkdown] = useState(false);
  const [hasMarkdown, setHasMarkdown] = useState<boolean>(false);
  const [tocData, setTocData] = useState<any[]>([]);

  // 获取LLM提供商和模型
  // 修改第78行，添加 defaultModels 的获取
  const { providers, defaultModels } = useLLMStore();

  // 添加自动选择默认模型的逻辑
  useEffect(() => {
    // 如果当前没有选择模型，且有默认模型可用，自动选择第一个
    if (!selectedModelId && defaultModels && defaultModels.length > 0) {
      const firstProvider = defaultModels[0];
      if (firstProvider.models && firstProvider.models.length > 0) {
        const firstModel = firstProvider.models[0];
        setSelectedModelId(firstModel.id);
        console.log('自动选择默认模型:', firstModel.name);
      }
    }
  }, [defaultModels, selectedModelId]);

  if (!document) return null;

  // 获取文档详细信息
  useEffect(() => {
    if (open && docData?.id) {
      const fetchDocumentDetails = async () => {
        try {
          // 获取文档的详细信息
          const token = localStorage.getItem('token') || sessionStorage.getItem('token');
          const response = await fetch(API.DOCUMENTS.DETAIL(docData.id), {
            headers: {
              'Authorization': `Bearer ${token}`,
              'accept': 'application/json'
            }
          });

          if (!response.ok) throw new Error('文档信息获取失败');

          const details = await response.json();
          console.log('获取到文档详情:', details);
          setDocumentDetails(details);

          // 检查是否有Markdown内容
          setHasMarkdown(!!details.markdown_url);

          // 重置Markdown内容
          setMarkdownContent("");

          // 如果是PDF文件，加载PDF
          if (details.content_type?.toLowerCase().includes('pdf') ||
              details.file_path?.toLowerCase().endsWith('.pdf')) {
            loadPdf();
          }
        } catch (error) {
          console.error('文档详情加载出错:', error);
          setDocumentDetails(null);
          setHasMarkdown(false);
        }
      };

      fetchDocumentDetails();
    } else {
      setDocumentDetails(null);
      setHasMarkdown(false);
    }

    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
        setPdfUrl(null);
      }
    };
  }, [open, docData?.id]);

  // 加载PDF文件
  const loadPdf = async () => {
    if (!docData) return;

    try {
      // 转换 URL 使其在浏览器中可访问
      // 对于 PDF 服务，我们需要使用完整的 Supabase Storage URL
      const supabaseUrl = docData.file_path.replace('/storage-proxy', 'http://host.docker.internal:54321/storage');
      console.log('原始文件路径:', docData.file_path);
      console.log('转换后的文件路径:', supabaseUrl);

      // 使用转换后的 URL 设置 PDF 查看器的源
      setPdfUrl(supabaseUrl);

      // 使用转换后的 URL 提取目录
      const tocResponse = await fetch(API.SERVICES.PDF.EXTRACT_TOC(supabaseUrl), {
        method: 'GET',
        headers: {
          'accept': 'application/json'
        }
      });

      if (!tocResponse.ok) {
        const errorData = await tocResponse.json();
        throw new Error(errorData.message || '目录抽取失败');
      }

      const tocResult = await tocResponse.json();
      setTocData(tocResult.toc || []);
    } catch (error) {
      console.error('加载PDF失败:', error);
      toast.error("加载PDF失败: " + (error instanceof Error ? error.message : "未知错误"));
    }
  };

  if (!docData) return null;

  const handleExtractTOC = async () => {
    if (!docData?.file_path) return;

    // 设置抽取状态为正在进行
    setIsExtractingTOC(true);

    try {
      const response = await fetch(API.SERVICES.PDF.EXTRACT_TOC(docData.file_path), {
        method: 'GET',
        headers: {
          'accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '目录抽取失败');
      }

      const result = await response.json();
      // toast("目录抽取成功.")

      console.log('目录抽取结果:', result);
      // alert(result.table_of_contents); // 暂时使用alert，后续替换为toast或其他更友好的通知方式

      // toast.success("目录抽取成功", {
      //   description: (
      //     <div className="max-h-60 overflow-y-auto p-2">
      //       {result.table_of_contents.map((item: any, index: number) => (
      //         <div key={index} className="mb-2">
      //           <span className="font-medium">层级 {item.level}: </span>
      //           <span>{item.title} (第 {item.page} 页)</span>
      //         </div>
      //       ))}
      //     </div>
      //   ),
      //   duration: 10000, // 10秒后自动关闭
      //   className: "w-[400px] z-999", // 设置固定宽度和z-index
      //   position: "top-left", // 设置位置为左上角
      // });
    } catch (error) {
      toast.error("目录抽取失败", {
        description: error instanceof Error ? error.message : "请稍后再试",
      });
    } finally {
      // 无论成功还是失败，都将抽取状态设置为已完成
      setIsExtractingTOC(false);
    }
  };

  // 添加文档转换功能
  const handleConvertToMarkdown = async () => {
    if (!docData?.file_path || !docData?.id) return;

    // 设置转换状态为正在进行
    setIsConverting(true);

    // 更新状态为全部进行中
    setConversionStatus({
      extract: 'pending',
      summary: 'pending',
      save: 'pending'
    });

    // 更新UI状态
    updateStatusUI('extract', 'pending');
    updateStatusUI('summary', 'pending');
    updateStatusUI('save', 'pending');

    try {
      // 第一阶段：文档抽取
      updateStatusUI('extract', 'processing');
      toast.info("正在抽取文档内容...", { duration: 3000 });

      // 转换 URL 使其在浏览器中可访问
      // 对于 PDF 服务，我们需要使用完整的 Supabase Storage URL
      const supabaseUrl = docData.file_path.replace('/storage-proxy', 'http://host.docker.internal:54321/storage');
      console.log('原始文件路径:', docData.file_path);
      console.log('转换后的文件路径:', supabaseUrl);

      const response = await fetch(API.SERVICES.PDF.CONVERT_TO_MARKDOWN(supabaseUrl), {
        method: 'GET',
        headers: {
          'accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        updateStatusUI('extract', 'error');
        throw new Error(errorData.message || '文档抽取失败');
      }

      const result = await response.json();
      const markdownContent = result.markdown_content;

      updateStatusUI('extract', 'success');
      toast.success("文档内容抽取完成", { duration: 3000 });

      // 第二阶段：文档总结
      updateStatusUI('summary', 'processing');
      toast.info("正在使用AI总结文档内容...", { duration: 3000 });

      let summary = "";

      try {
        // 检查内容长度，控制token数量不超过8192
        const MAX_TOKEN_LIMIT = 7000; // 为提示词和模型回复预留空间，设置为7000

        // 估算原始内容的token数量
        const estimatedTokens = estimateTokenCount(markdownContent);
        console.log(`估算文档token数量: ${estimatedTokens}`);

        // 如果内容超过限制，截断内容
        let contentToSummarize;
        if (estimatedTokens > MAX_TOKEN_LIMIT) {
          // 计算需要保留的比例
          const keepRatio = MAX_TOKEN_LIMIT / estimatedTokens;

          // 保留前60%和后40%的内容，确保包含文档的开头和结尾
          const frontPart = Math.floor(markdownContent.length * keepRatio * 0.6);
          const backPart = Math.floor(markdownContent.length * keepRatio * 0.4);

          contentToSummarize =
            markdownContent.substring(0, frontPart) +
            "\n\n...(内容已截断，原始文档过长)...\n\n" +
            markdownContent.substring(markdownContent.length - backPart);

          // 再次估算截断后的token数量，确保在限制范围内
          const truncatedTokens = estimateTokenCount(contentToSummarize);

          console.log(`文档内容已截断：原始token数 ${estimatedTokens}，截断后token数 ${truncatedTokens}`);
          console.log(`文档内容已截断：原始长度 ${markdownContent.length} 字符，截断后 ${contentToSummarize.length} 字符`);

          // 如果截断后仍然超过限制，进一步截断
          if (truncatedTokens > MAX_TOKEN_LIMIT) {
            const furtherRatio = MAX_TOKEN_LIMIT / truncatedTokens;
            const furtherFrontPart = Math.floor(contentToSummarize.length * furtherRatio * 0.6);
            const furtherBackPart = Math.floor(contentToSummarize.length * furtherRatio * 0.4);

            contentToSummarize =
              contentToSummarize.substring(0, furtherFrontPart) +
              "\n\n...(内容已大幅截断，原始文档过长)...\n\n" +
              contentToSummarize.substring(contentToSummarize.length - furtherBackPart);

            console.log(`文档内容已进一步截断：最终token数约 ${estimateTokenCount(contentToSummarize)}`);
          }
        } else {
          contentToSummarize = markdownContent;
        }

        // 构建提示
        const prompt = `作为专业论文摘要工程师，请对以下内容创建一个全面的摘要：
        * 论文标题以"##"标签开头，论文标题不一定就是文件的名称
        * 撰写详细、透彻、深入且结构清晰的摘要，同时保持语言简洁
        * 包含主要观点和核心信息，去除冗余语言，专注于关键方面
        * 严格基于提供的文本，不要添加外部信息
        * 论文的摘要，应该取自论文中，用中文进行重写，需要完整给出。
        * 论文的研究背景和亮点需要给出简洁而明确的结论。
        * 总字数控制在1000字以内

        以下是需要摘要的内容：

        ${contentToSummarize}
        `;

        // 获取API配置
        // 获取API配置
        let apiUrl, model, apiKey;
        
        // 如果用户选择了特定模型
        if (selectedModelId) {
        // 首先在用户提供商中查找选中模型的提供商信息
        let selectedProvider = providers.find(provider =>
        provider.models.some(model => model.id === selectedModelId)
        );
        
        // 如果在用户提供商中没找到，再在默认模型中查找
        if (!selectedProvider) {
        selectedProvider = defaultModels.find(provider =>
        provider.models.some(model => model.id === selectedModelId)
        );
        }
        
        if (selectedProvider) {
        console.log('使用选择的模型:', selectedModelId, '提供商:', selectedProvider.name);
        
        // 直接处理baseUrl是否有尾部斜杠
        const baseUrl = selectedProvider.baseUrl;
        if (baseUrl.endsWith('/')) {
        apiUrl = `${baseUrl}chat/completions`;
        } else {
        apiUrl = `${baseUrl}/v1/chat/completions`;
        }
        
        console.log('构建的API URL:', apiUrl);
        model = selectedModelId;
        apiKey = selectedProvider.apiKey;
        } else {
        // 未找到选中模型的提供商信息，显示警告
        console.warn('未找到选中模型的提供商信息');
        toast.error("无法找到所选模型的提供商信息", {
        description: "请选择其他模型或检查模型配置"
        });
        throw new Error("未找到模型提供商信息");
        }
        } else {
        // 用户未选择模型，显示警告
        console.warn('未选择任何模型，无法进行文档总结');
        toast.error("请选择一个模型进行文档总结", {
        description: "未选择模型无法继续操作"
        });
        throw new Error("未选择模型");
        }

        // 准备请求参数
        const payload = {
          model: model,
          messages: [{ role: "user", content: prompt }],
          temperature: 0.6,
          max_tokens: 8000  // 减小输出token限制，为输入留出更多空间
        };

        // 准备请求头
        const headers: Record<string, string> = {
          'Content-Type': 'application/json'
        };

        // 如果有API密钥，添加到请求头
        if (apiKey) {
          headers['Authorization'] = `Bearer ${apiKey}`;
        }

        // 发送请求
        const summaryResponse = await fetch(apiUrl, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify(payload)
        });

        if (!summaryResponse.ok) {
          throw new Error(`LLM API调用失败: ${summaryResponse.status}`);
        }

        const summaryResult = await summaryResponse.json();

        // 获取原始摘要内容
        let summaryContent = summaryResult.choices[0].message.content;

        // 检查是否包含 think 标签，如果有则去除其中的内容
        const thinkTagRegex = /<think>[\s\S]*?<\/think>/g;
        if (thinkTagRegex.test(summaryContent)) {
          console.log('检测到think标签，正在移除...');
          summaryContent = summaryContent.replace(thinkTagRegex, '');
          // 移除可能存在的多余空行
          summaryContent = summaryContent.replace(/\n{3,}/g, '\n\n');
        }

        summary = summaryContent;

        updateStatusUI('summary', 'success');
        toast.success("文档总结完成", { duration: 3000 });
      } catch (summaryError) {
        console.error('文档总结失败:', summaryError);
        updateStatusUI('summary', 'error');
        toast.error("文档总结失败，将使用原始内容", {
          description: summaryError instanceof Error ? summaryError.message : "请稍后再试"
        });

        // 如果总结失败，使用原始内容的前200个字符作为摘要
        summary = markdownContent.substring(0, 200) + "...";
      }

      // 第三阶段：存储到数据库
      updateStatusUI('save', 'processing');
      toast.info("正在保存文档内容和摘要...", { duration: 3000 });

      try {
        // 获取token
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');

        // 准备更新数据
        const updateData = {
          description: summary.replace(/\0/g, ''), // 移除NUL字符
          markdown_url: markdownContent.replace(/\0/g, ''), // 移除NUL字符
          // 保持title不变
          title: documentDetails?.title || docData.title
        };

        // 发送更新请求
        const updateResponse = await fetch(API.DOCUMENTS.DETAIL(docData.id), {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'accept': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        if (!updateResponse.ok) {
          const errorText = await updateResponse.text();
          console.error('更新失败详情:', errorText);
          updateStatusUI('save', 'error');
          throw new Error(`文档内容和摘要更新失败: ${errorText}`);
        }

        updateStatusUI('save', 'success');
        toast.success("文档内容和摘要已保存到数据库");

        // 更新本地文档详情
        if (documentDetails) {
          setDocumentDetails({
            ...documentDetails,
            description: summary,
            markdown_url: markdownContent
          });

          // 更新Markdown状态
          setHasMarkdown(true);
        }

        // 所有步骤完成后，3秒后关闭Popover
        setTimeout(() => {
          const popoverTrigger = document.querySelector('[data-slot="popover-trigger"]') as HTMLButtonElement;
          if (popoverTrigger) {
            popoverTrigger.click();
          }
        }, 3000);
      } catch (updateError) {
        console.error('数据库更新失败:', updateError);
        updateStatusUI('save', 'error');
        toast.error("数据库更新失败", {
          description: updateError instanceof Error ? updateError.message : "请稍后再试"
        });
      }
    } catch (error) {
      toast.error("文档处理失败", {
        description: error instanceof Error ? error.message : "请稍后再试",
      });
    } finally {
      // 无论成功还是失败，都将转换状态设置为已完成
      setIsConverting(false);
    }
  };

  // 更新状态UI
  const updateStatusUI = (stage: 'extract' | 'summary' | 'save', status: 'pending' | 'processing' | 'success' | 'error') => {
    const element = document.getElementById(`${stage}-status`);
    if (!element) return;

    // 移除所有状态类
    element.classList.remove('bg-gray-200', 'bg-blue-500', 'bg-green-500', 'bg-red-500', 'animate-pulse');

    // 添加对应状态的类
    switch (status) {
      case 'pending':
        element.classList.add('bg-gray-200');
        break;
      case 'processing':
        element.classList.add('bg-blue-500', 'animate-pulse');
        break;
      case 'success':
        element.classList.add('bg-green-500');
        break;
      case 'error':
        element.classList.add('bg-red-500');
        break;
    }

    // 更新状态对象
    setConversionStatus(prev => ({
      ...prev,
      [stage]: status
    }));
  };

  // 处理显示Markdown内容
  const handleShowMarkdown = async () => {
    if (!documentDetails?.markdown_url) {
      toast.error("该文档没有可用的Markdown内容");
      return;
    }

    // 切换到Markdown标签
    setActiveTab("markdown");

    // 如果已经加载过Markdown内容，则不需要再次加载
    if (markdownContent) return;

    // 开始加载Markdown内容
    setIsLoadingMarkdown(true);

    try {
      // 如果markdown_url是完整的URL，则直接使用
      if (documentDetails.markdown_url.startsWith('http')) {
        // 获取Markdown内容
        const response = await fetch(documentDetails.markdown_url);
        if (!response.ok) {
          throw new Error(`获取Markdown内容失败: ${response.status}`);
        }

        const content = await response.text();
        setMarkdownContent(content);
      } else {
        // 如果markdown_url是存储在数据库中的内容，则直接使用
        setMarkdownContent(documentDetails.markdown_url);
      }

      // 显示成功提示
      toast.success("Markdown内容加载成功");
    } catch (error) {
      console.error('加载Markdown内容失败:', error);
      toast.error("加载Markdown内容失败", {
        description: error instanceof Error ? error.message : "请稍后再试"
      });
    } finally {
      setIsLoadingMarkdown(false);
    }
  };

  return (
    <div className={`preview-document-dialog ${open ? 'open' : 'closed'} ${className} w-[95vw] max-h-screen h-screen overflow-hidden p-0`}>
      <div className="header p-6 pb-2">
        <div className="flex items-center justify-between">
          <h2 className="text-xl">{documentDetails?.title || docData.title}</h2>
        </div>

        {/* 文档工具栏 */}
        <div className="flex items-center gap-2 mt-4 pb-2 border-b">
          {/* 查看Markdown内容按钮 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`flex items-center gap-2`}
                onClick={handleShowMarkdown}
                disabled={!hasMarkdown}
                title={hasMarkdown ? "查看Markdown内容" : "文档没有Markdown内容"}
              >
                <FileText className={`h-4 w-4 ${hasMarkdown ? 'text-green-500' : 'text-gray-400'}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>{hasMarkdown ? "查看Markdown内容" : "文档没有Markdown内容"}</TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
                onClick={handleExtractTOC}
                disabled={isExtractingTOC}
              >
                {isExtractingTOC ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <FolderTree className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>{isExtractingTOC ? "正在抽取目录..." : "抽取文档目录"}</TooltipContent>
          </Tooltip>

          {/* 添加文档转换按钮 */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className={`flex items-center gap-2 ${hasMarkdown ? 'opacity-50' : ''}`}
                disabled={isConverting || hasMarkdown}
                title={hasMarkdown ? "文档已有Markdown内容" : "转换为Markdown"}
              >
                {isConverting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <FileCode className={`h-4 w-4 ${hasMarkdown ? 'text-gray-400' : ''}`} />
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-96 p-4" align="start">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">文档转换设置</h3>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 rounded-full"
                    onClick={() => {
                      const popoverTrigger = document.querySelector('[data-slot="popover-trigger"]') as HTMLButtonElement;
                      if (popoverTrigger) {
                        popoverTrigger.click();
                      }
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div>
                  <label htmlFor="model-select" className="block text-xs font-medium mb-1">选择大模型</label>
                  <select
                    id="model-select"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={selectedModelId || ''}
                    onChange={(e) => {
                      setSelectedModelId(e.target.value || null);
                    }}
                  >
                    <option value="">-- 请选择模型 --</option>
                    {/* 显示默认模型 */}
                    {defaultModels && defaultModels.length > 0 && defaultModels.map((provider) => (
                      <optgroup key={`default-${provider.id}`} label={`${provider.name || provider.id} (默认)`}>
                        {provider.models.map((model) => (
                          <option key={`default-${model.id}`} value={model.id}>
                            {model.name}
                          </option>
                        ))}
                      </optgroup>
                    ))}
                    {/* 显示用户模型 */}
                    {providers && providers.length > 0 && providers.map((provider) => (
                      <optgroup key={provider.id} label={`${provider.name} (${provider.type})`}>
                        {provider.models.map((model) => (
                          <option key={model.id} value={model.id}>
                            {model.name}
                          </option>
                        ))}
                      </optgroup>
                    ))}
                  </select>
                  <div className="text-xs text-gray-500 mt-1">
                    必须选择一个模型用于文档总结
                  </div>
                  <div className="text-xs text-amber-500 mt-2">
                    注意：过长的文档内容会被自动截断以符合模型的token限制(最大8192)
                  </div>

                  {selectedModelId && (
                    <div className="mt-2 p-2 bg-gray-50 rounded-md text-xs">
                      {providers && providers.map(provider => {
                        const selectedModel = provider.models.find(m => m.id === selectedModelId);
                        if (selectedModel) {
                          return (
                            <div key={provider.id} className="flex items-center gap-2">
                              <div className={`w-2 h-2 rounded-full ${provider.color || 'bg-blue-500'}`}></div>
                              <div>已选: {provider.name} / {selectedModel.name}</div>
                            </div>
                          );
                        }
                        return null;
                      })}
                    </div>
                  )}
                </div>

                <div id="conversion-status" className="space-y-2">
                  <div className="text-sm font-medium">转换进度</div>
                  <div className="space-y-1">
                    <div className="flex items-center">
                      <div id="extract-status" className="w-4 h-4 rounded-full bg-gray-200 mr-2"></div>
                      <span className="text-sm">文档抽取</span>
                    </div>
                    <div className="flex items-center">
                      <div id="summary-status" className="w-4 h-4 rounded-full bg-gray-200 mr-2"></div>
                      <span className="text-sm">内容总结</span>
                    </div>
                    <div className="flex items-center">
                      <div id="save-status" className="w-4 h-4 rounded-full bg-gray-200 mr-2"></div>
                      <span className="text-sm">保存到数据库</span>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end gap-2 pt-2">
                  <Button
                    type="button"
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                    onClick={handleConvertToMarkdown}
                    disabled={isConverting || hasMarkdown}
                  >
                    {isConverting ? "处理中..." : hasMarkdown ? "已有Markdown内容" : "开始转换"}
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          <div className="flex-1" />



          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
                onClick={() => {
                  if (!documentDetails) return;

                  // 创建博客文章内容 - 不添加标题，因为系统会自动添加
                  const postContent = `\n${documentDetails.description || '无描述'}\n![](${documentDetails.file_path})\n`;
                  console.log('博客文章内容:', postContent);

                  // 获取token
                  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
                  console.log('使用的token:', token ? token.substring(0, 10) + '...' : 'null');

                  // 创建FormData对象
                  const formData = new FormData();
                  formData.append("content", postContent);

                  if (!token) {
                    toast.error("未登录或登录已过期，请重新登录");
                    return;
                  }

                  // 使用API.POSTS.LIST获取正确的API路径
                  console.log('发布微博的API路径:', API.POSTS.LIST);

                  // 发送请求
                  fetch(API.POSTS.LIST, {
                    method: 'POST',
                    headers: {
                      'accept': 'application/json',
                      'Authorization': `Bearer ${token}`
                    },
                    body: formData
                  })
                  .then(async response => {
                    if (response.ok) {
                      console.log('博客文章发布成功');
                      toast.success("文档已成功发布为博客文章");
                    } else {
                      // 尝试获取详细的错误信息
                      let errorText = "";
                      try {
                        const errorData = await response.json();
                        errorText = errorData.detail || JSON.stringify(errorData);
                      } catch (e) {
                        errorText = await response.text();
                      }
                      console.error('发布失败，状态码:', response.status, '错误信息:', errorText);
                      toast.error(`发布失败: ${response.status} ${errorText || '请重试'}`);
                    }
                  })
                  .catch(error => {
                    console.error('发布博客文章失败:', error);
                    toast.error(`发布失败: ${error.message || '请检查网络连接'}`);
                  });
                }}
              >
                <Bird className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>将文档发布为博客文章</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="ghost" size="sm" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                {/* <span>QA</span> */}
              </Button>
            </TooltipTrigger>
            <TooltipContent>与文档进行对话</TooltipContent>
          </Tooltip>


        </div>
      </div>

      {/* 文档内容区域 */}
      <div className="content h-[calc(100vh-8rem)] px-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
          <TabsList className="mb-2">
            <TabsTrigger value="pdf">PDF 预览</TabsTrigger>
            <TabsTrigger value="markdown">Markdown 内容</TabsTrigger>
          </TabsList>

          <TabsContent value="pdf" className="h-[calc(100%-40px)]">
            <div className="h-full w-full">
              {isLoadingPdf ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-gray-500">正在加载 PDF 文件...</div>
                </div>
              ) : pdfUrl ? (
                <PDFViewer
                  url={pdfUrl}
                  className="h-full w-full"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-gray-500">文档加载失败或不支持预览</div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="markdown" className="h-[calc(100%-40px)] overflow-auto">
            {isLoadingMarkdown ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-gray-500">正在加载 Markdown 内容...</div>
              </div>
            ) : markdownContent ? (
              <div className="p-4">
                {/* 使用MarkdownComponents渲染Markdown内容 */}
                <ReactMarkdown
                  components={MarkdownComponents}
                  remarkPlugins={[remarkGfm, remarkMath]}
                  rehypePlugins={[rehypeKatex]}
                >
                  {convertMathFormula(markdownContent)}
                </ReactMarkdown>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-gray-500">没有可用的 Markdown 内容</div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}