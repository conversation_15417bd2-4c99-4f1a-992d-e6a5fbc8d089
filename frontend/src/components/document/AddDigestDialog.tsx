import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "../ui/button";
import { CircleEllipsis } from "lucide-react"; // 添加在文件顶部的 imports 中
// 添加导入
import ReactMarkdown from "react-markdown";
import { useLLMStore } from '@/store/llmStore'; // 添加 LLM Store 导入

interface AddDigestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  userId: number;  // 添加 userId 属性
}

export function AddDigestDialog({ open, onOpenChange, onSuccess, userId }: AddDigestDialogProps) {
  const [formData, setFormData] = useState({
    title: "",
    source_url: "",
    markdown_content: "",
    summary: "",
    digest_type: "blog",
    is_public: false,
    rate: 0,
    tags: "",
    user_id: 1, // 默认用户ID，实际应该从认证系统获取
  });
  
  // 获取 LLM Store 中的模型信息
  const { providers, selectedModel } = useLLMStore();

  const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/digests/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ...formData,
            user_id: userId,
          }),
        });
    
        if (!response.ok) {
          const errorData = await response.json();
          console.error("服务器错误:", errorData);
          throw new Error("添加失败");
        }
    
        onSuccess();
        onOpenChange(false);
      } catch (error) {
        console.error("添加摘要失败:", error);
        alert("添加失败，请重试");
      }
    };

  // 添加 URL 验证函数
  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  // 添加状态
  const [isExtracting, setIsExtracting] = useState(false);
  // 在组件顶部添加新的状态
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false);
  const [extractionComplete, setExtractionComplete] = useState(false);

  // 获取选中模型的提供商信息
  // 删除现有的 getSelectedModelInfo 函数，替换为：
  const generateDigest = async () => {
    if (!content.trim()) {
      toast.error('请输入要摘要的内容');
      return;
    }

    setIsGenerating(true);
    setDigest('');

    try {
      const messages = [
        {
          role: 'system',
          content: '你是一个专业的文档摘要助手。请为用户提供的内容生成简洁、准确的摘要。'
        },
        {
          role: 'user',
          content: `请为以下内容生成摘要：\n\n${content}`
        }
      ];

      const requestConfig = ModelManager.createRequestConfig(messages, {
        temperature: 0.3
      });
      
      if (!requestConfig) {
        toast.error('模型配置错误，请检查模型设置');
        return;
      }

      const response = await fetch(requestConfig.url, {
        method: 'POST',
        headers: requestConfig.headers,
        body: JSON.stringify(requestConfig.requestBody)
      });

      if (!selectedModel) {
        return {
          url: `${import.meta.env.VITE_LLM_HOST}/v1/chat/completions`,
          model: `${import.meta.env.VITE_LLM_MODEL}`,
          key: `${import.meta.env.VITE_LLM_API_KEY}`
        };
      }
      
      // 处理 selectedModel 可能是对象的情况
      const modelId = typeof selectedModel === 'string' ? selectedModel : selectedModel.id;
      
      // 查找包含选中模型的提供商
      const provider = providers.find(p => 
        p.models.some(m => m.id === modelId)
      );
      
      if (provider) {
        // 确保 URL 路径末尾包含 v1/chat/completions
        const baseUrl = provider.baseUrl || `${import.meta.env.VITE_LLM_HOST}`;
        const apiUrl = baseUrl.endsWith('/') 
          ? `${baseUrl}v1/chat/completions` 
          : `${baseUrl}/v1/chat/completions`;
        
        return {
          url: apiUrl,
          model: modelId,
          key: provider.apiKey || `${import.meta.env.VITE_LLM_API_KEY}`
        };
      }
      
      // 如果没有找到，返回默认值
      return {
        url: `${import.meta.env.VITE_LLM_HOST}/v1/chat/completions`,
        model: `${import.meta.env.VITE_LLM_MODEL}`,
        key: `${import.meta.env.VITE_LLM_API_KEY}`
      };
    };

  // 添加表单验证函数
  const isFormValid = () => {
    return (
      formData.title.trim() !== "" &&
      formData.markdown_content.trim() !== "" &&
      formData.source_url.trim() !== ""
    );
  };

  // 修改 handleUrl2Markdown 函数
  const handleUrl2Markdown = async (url: string) => {
    setIsExtracting(true);
    setExtractionComplete(false);
    // 清空表单内容，但保留 URL
    setFormData(prev => ({
      ...prev,
      title: "",
      markdown_content: "",
      summary: "",
      digest_type: "blog",
      is_public: false,
      rate: 0,
      tags: "",
    }));
    
    try {
      const response = await fetch(`https://r.jina.ai/${url}`, {
        headers: {
        //   "x-engine": "readerlm-v2",
        },
      });

      if (response.ok) {
        const markdown = await response.text();
        // console.log(markdown);
    
        // 删除Markdown文档的首尾 "```"
        const cleanedMarkdown = markdown
          .replace(/^```[\s\S]*?\n/, '')
          .replace(/\n```\s*$/, '');
    
        // 提取标题
        const titleMatch = cleanedMarkdown.match(/Title:\s*(.+?)(?:\n|$)/);
        const title = titleMatch ? titleMatch[1].trim() : '';
        
        // 其余代码保持不变
        setIsGeneratingSummary(true);
        let summary = '';
        try {
          // 截取文本的前 2000 个字符作为输入
          const truncatedContent = cleanedMarkdown.slice(0, 2000);
          
          // 获取选中的模型信息
          const modelInfo = getSelectedModelInfo();
          console.log('使用的模型信息:', modelInfo);
          
          const llmResponse = await fetch(modelInfo.url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${modelInfo.key}`,
            },
            body: JSON.stringify({
              model: modelInfo.model,
              messages: [
                {
                  role: "system",
                  content: "你是一个文章摘要生成助手。请基于提供的文章开头部分，生成一个简短的中文摘要，不超过200字。摘要应该包含文章的主要观点和关键信息。"
                },
                {
                  role: "user",
                  content: truncatedContent
                }
              ],
              temperature: 0.6,
              max_tokens: 1000,  // 增加 max_tokens 以确保有足够空间生成摘要
            }),
          });

          // 添加在组件内部，其他状态声明之后
            const cleanLLMResponse = (text: string): string => {
              // 移除 <think>...</think> 标签及其内容
              return text.replace(/<think>[\s\S]*?<\/think>/g, '')
                // 移除可能存在的其他思维过程标记
                .replace(/思考过程：[\s\S]*?(?=\n\n|$)/g, '')
                .replace(/让我思考一下：[\s\S]*?(?=\n\n|$)/g, '')
                // 清理多余的空行和空格
                .replace(/\n{3,}/g, '\n\n')
                .trim();
            };

          if (llmResponse.ok) {
            const result = await llmResponse.json();
            summary = cleanLLMResponse(result.choices[0].message.content.trim());
          } else {
            console.error('生成摘要失败');
          }
        } catch (error) {
          console.error('调用大模型服务失败:', error);
        } finally {
          setIsGeneratingSummary(false);
        }

        setFormData((prev) => ({
          ...prev,
          markdown_content: cleanedMarkdown,
          title: prev.title || title,
          summary: summary || prev.summary,
        }));
      } else {
        alert("抽取失败，请稍后重试");
      }
    } catch (error) {
      console.error("抽取失败:", error);
      alert("抽取失败，请稍后重试");
    } finally {
      setIsExtracting(false);
      setExtractionComplete(true);
      // 3秒后隐藏完成消息
      setTimeout(() => setExtractionComplete(false), 3000);
    }
  };

  // 修改 JSX 部分
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] w-full">
        <DialogHeader>
          <div className="flex items-center gap-4">
            <DialogTitle>添加文摘</DialogTitle>
            {selectedModel && (
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md">
                模型: {typeof selectedModel === 'string' ? selectedModel : selectedModel.id || '未知模型'}
              </div>
            )}
            {isExtracting && (
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <CircleEllipsis className="h-4 w-4 animate-spin" />
                <span>正在抽取文章内容...</span>
              </div>
            )}
            {!isExtracting && !isGeneratingSummary && extractionComplete && (
              <div className="flex items-center gap-2 text-sm text-green-500">
                <span>✓ 抽取完成</span>
              </div>
            )}
            {isGeneratingSummary && (
              <div className="flex items-center gap-2 text-sm text-blue-500">
                <CircleEllipsis className="h-4 w-4 animate-spin" />
                <span>正在生成摘要...</span>
              </div>
            )}
          </div>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              文章 URL <span className="text-red-500">*</span>
            </label>
            <div className="flex gap-2 items-center">
              <input
                type="url"
                value={formData.source_url}
                onChange={(e) => {
                  const url = e.target.value;
                  setFormData({ ...formData, source_url: url });
                }}
                onBlur={(e) => {
                  if (e.target.value && !isValidUrl(e.target.value)) {
                    alert("请输入有效的 URL");
                  }
                }}
                className="flex-1 mt-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                required
                placeholder="https://example.com/article"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="mt-1"
                title="抽取文章内容"
                disabled={!isValidUrl(formData.source_url) || isExtracting}
                onClick={() => handleUrl2Markdown(formData.source_url)}
              >
                <CircleEllipsis
                  className={`h-4 w-4 ${
                    !isValidUrl(formData.source_url)
                      ? "text-gray-300"
                      : isExtracting
                      ? "text-blue-500 animate-spin"
                      : ""
                  }`}
                />
              </Button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              标题 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) =>
                setFormData({ ...formData, title: e.target.value })
              }
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="文章标题"
            />
          </div>

          {/* <div>
            <label className="block text-sm font-medium text-gray-700">
              内容
            </label>
            <div className="space-y-2">
              <div className="prose prose-sm w-full border rounded-md p-4 bg-gray-50 h-[600px] overflow-y-auto">
                <ReactMarkdown
                  components={{
                    img: ({ node, ...props }) => (
                      <img
                        {...props}
                        className="max-w-full h-auto"
                        loading="lazy"
                      />
                    ),
                    pre: ({ node, ...props }) => (
                      <div className="overflow-x-auto">
                        <pre {...props} />
                      </div>
                    ),
                  }}
                >
                  {formData.markdown_content}
                </ReactMarkdown>
              </div>
            </div>
          </div> */}

          <div>
            <label className="block text-sm font-medium text-gray-700">
              摘要
            </label>
            <textarea
              value={formData.summary}
              onChange={(e) =>
                setFormData({ ...formData, summary: e.target.value })
              }
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              placeholder="文章摘要"
            />
          </div>

          <div>
            <div className="flex gap-4 items-start">
              <div className="w-1/4">
                <label className="block text-sm font-medium text-gray-700">
                  类型
                </label>
                <select
                  value={formData.digest_type}
                  onChange={(e) =>
                    setFormData({ ...formData, digest_type: e.target.value })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="blog">博客</option>
                  <option value="webpage">网页</option>
                  <option value="wechat">微信</option>
                  <option value="other">其他</option>
                </select>
              </div>

              <div className="w-1/4">
                <label className="block text-sm font-medium text-gray-700">
                  标签
                </label>
                <input
                  type="text"
                  value={formData.tags}
                  onChange={(e) =>
                    setFormData({ ...formData, tags: e.target.value })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  placeholder="使用逗号分隔多个标签"
                />
              </div>

              <div className="w-1/4">
                <label className="flex items-center text-sm font-medium text-gray-700">
                  <input
                    type="checkbox"
                    checked={formData.is_public}
                    onChange={(e) =>
                      setFormData({ ...formData, is_public: e.target.checked })
                    }
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                  <span className="ml-2">公开</span>
                </label>
              </div>
              <div className="w-1/4">
                <label className="block text-sm font-medium text-gray-700">
                  评分
                </label>
                <input
                  type="number"
                  min="0"
                  max="5"
                  step="1"
                  value={formData.rate}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      rate: parseFloat(e.target.value),
                    })
                  }
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <button
              type="button"
              onClick={() => onOpenChange(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={!isFormValid()}
              className={`px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md ${
                isFormValid()
                  ? "bg-blue-600 hover:bg-blue-700"
                  : "bg-blue-300 cursor-not-allowed"
              }`}
            >
              添加
            </button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
