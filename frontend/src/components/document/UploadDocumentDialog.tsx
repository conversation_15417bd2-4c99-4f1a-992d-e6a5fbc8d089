import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { useDocumentStore } from "@/store/documentStore";
import { FileText, Check, ArrowLeft, ArrowRight, Loader2, FolderTree, FileCode, BookOpen, ExternalLink } from "lucide-react";
import { SimpleFileUploader } from "@/components/ui/simple-file-uploader";
import API from "@/config/api";
import { PDFViewer } from "@/components/ui/pdf-viewer";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import { MarkdownComponents, convertMathFormula } from "@/components/markdown/MarkdownComponents";
import { useLLMStore } from '@/store/llmStore';
import { convertSupabaseUrl, convertUrlForDocker } from "@/utils/url-utils";

interface UploadDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// 文档类型选项
const documentTypes = [
  { value: "paper", label: "论文" },
  { value: "ppt", label: "PPT" },
  { value: "whitepaper", label: "白皮书" },
  { value: "technical_report", label: "技术报告" },
];

// 定义步骤类型
type Step = 'upload' | 'toc' | 'markdown' | 'summary' | 'save';

export function UploadDocumentDialog({ open, onOpenChange }: UploadDocumentDialogProps) {
  // 基本状态
  const [title, setTitle] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [documentType, setDocumentType] = useState("paper"); // 默认选择论文
  const { fetchDocuments } = useDocumentStore();
  const { providers } = useLLMStore();

  // 步骤状态
  const [currentStep, setCurrentStep] = useState<Step>('upload');
  const [completedSteps, setCompletedSteps] = useState<Set<Step>>(new Set());
  const [activeSteps, setActiveSteps] = useState<Set<Step>>(new Set(['upload']));

  // 文档处理状态
  const [tocData, setTocData] = useState<any[]>([]);
  const [isExtractingTOC, setIsExtractingTOC] = useState(false);
  const [markdownContent, setMarkdownContent] = useState<string>("");
  const [markdownOutline, setMarkdownOutline] = useState<any[]>([]);
  const [isConvertingMarkdown, setIsConvertingMarkdown] = useState(false);
  const [summary, setSummary] = useState<string>("");
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedModelId, setSelectedModelId] = useState<string>("");
  const [documentId, setDocumentId] = useState<string | null>(null);
  const [filePath, setFilePath] = useState<string | null>(null);
  const [isLoadingFilePath, setIsLoadingFilePath] = useState(false);

  // 使用 useEffect 监听步骤变化
  useEffect(() => {
    // 当对话框打开时，重置所有状态
    if (open) {
      setCurrentStep('upload');
      setCompletedSteps(new Set());
      setActiveSteps(new Set(['upload']));
      setTitle("");
      setFile(null);
      setDocumentType("paper");
      setTocData([]);
      setMarkdownContent("");
      setMarkdownOutline([]);
      setSummary("");
      setDocumentId(null);
      setSelectedModelId("");
      setFilePath(null);
    }
  }, [open]);

  // 使用 useEffect 加载文件路径
  useEffect(() => {
    const loadFilePath = async () => {
      if (!documentId) return;

      setIsLoadingFilePath(true);

      try {
        // 获取token
        const token = localStorage.getItem('token') || sessionStorage.getItem('token');

        // 获取文档详情，以获取服务器上的文件路径
        const docResponse = await fetch(API.DOCUMENTS.DETAIL(documentId), {
          headers: {
            'Authorization': `Bearer ${token}`,
            'accept': 'application/json'
          }
        });

        if (!docResponse.ok) {
          throw new Error("获取文档详情失败");
        }

        const docDetails = await docResponse.json();
        const path = docDetails.file_path;

        if (!path) {
          throw new Error("文档路径不存在");
        }

        // 转换 URL 使其在浏览器中可访问
        // 对于 PDF 服务，我们需要使用完整的 Supabase Storage URL
        const supabaseUrl = path.replace('/storage-proxy', 'http://host.docker.internal:54321/storage');
        console.log('原始文件路径:', path);
        console.log('转换后的文件路径:', supabaseUrl);

        setFilePath(supabaseUrl);
      } catch (error) {
        toast.error("加载文件路径失败: " + (error instanceof Error ? error.message : "未知错误"));
      } finally {
        setIsLoadingFilePath(false);
      }
    };

    loadFilePath();
  }, [documentId]);

  // 步骤配置
  const steps: { id: Step, label: string, icon: React.ReactNode }[] = [
    { id: 'upload', label: '上传预览', icon: <FileText className="h-4 w-4" /> },
    { id: 'toc', label: '抽取目录', icon: <FolderTree className="h-4 w-4" /> },
    { id: 'markdown', label: '转换Markdown', icon: <FileCode className="h-4 w-4" /> },
    { id: 'summary', label: '生成摘要', icon: <BookOpen className="h-4 w-4" /> },
    { id: 'save', label: '保存文档', icon: <Check className="h-4 w-4" /> },
  ];

  // 处理文件变更
  const handleFileChange = (selectedFile: File | null) => {
    setFile(selectedFile);
    // 如果没有设置标题，则使用文件名作为标题
    if (selectedFile && !title) {
      setTitle(selectedFile.name.split('.')[0]);
    }

    // 重置文档ID，因为文件已经改变
    setDocumentId(null);
  };

  // 处理步骤导航
  const goToStep = (step: Step) => {
    // 只允许导航到已激活的步骤
    if (activeSteps.has(step)) {
      setCurrentStep(step);
    }
  };

  // 处理下一步
  const handleNext = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    if (currentIndex < steps.length - 1) {
      const nextStep = steps[currentIndex + 1].id;

      // 将当前步骤标记为已完成
      const newCompleted = new Set(completedSteps);
      newCompleted.add(currentStep);
      setCompletedSteps(newCompleted);

      // 激活下一步
      const newActive = new Set(activeSteps);
      newActive.add(nextStep);
      setActiveSteps(newActive);

      // 设置当前步骤为下一步
      setCurrentStep(nextStep);
    }
  };

  // 处理上一步
  const handleBack = () => {
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    if (currentIndex > 0) {
      const prevStep = steps[currentIndex - 1].id;
      setCurrentStep(prevStep);
    }
  };

  // 第一步：上传文档
  const handleUploadDocument = async () => {
    if (!file) {
      toast.error("请选择要上传的文件");
      return;
    }

    if (!title.trim()) {
      toast.error("请输入文档标题");
      return;
    }

    setUploading(true);

    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const formData = new FormData();
      formData.append("file", file);
      formData.append("document_title", title);
      formData.append("document_type", documentType);

      const response = await fetch(API.DOCUMENTS.UPLOAD, {
        method: "POST",
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.detail || "上传失败");
      }

      const result = await response.json();
      setDocumentId(result.id);

      toast.success("文档上传成功");
      handleNext(); // 进入下一步
    } catch (error) {
      toast.error("文档上传失败: " + (error instanceof Error ? error.message : "未知错误"));
    } finally {
      setUploading(false);
    }
  };

  // 第二步：抽取目录
  const handleExtractTOC = async () => {
    if (!file || !documentId) {
      toast.error("请先上传文件并完成第一步");
      return;
    }

    setIsExtractingTOC(true);

    try {
      // 获取token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');

      // 获取文档详情，以获取服务器上的文件路径
      const docResponse = await fetch(API.DOCUMENTS.DETAIL(documentId), {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });

      if (!docResponse.ok) {
        throw new Error("获取文档详情失败");
      }

      const docDetails = await docResponse.json();
      const filePath = docDetails.file_path;

      if (!filePath) {
        throw new Error("文档路径不存在");
      }

      // 转换 URL 使其在浏览器中可访问
      // 对于 PDF 服务，我们需要使用完整的 Supabase Storage URL
      const supabaseUrl = filePath.replace('/storage-proxy', 'http://host.docker.internal:54321/storage');
      console.log('原始文件路径:', filePath);
      console.log('转换后的文件路径:', supabaseUrl);

      // 使用转换后的文件路径进行TOC提取
      const response = await fetch(API.SERVICES.PDF.EXTRACT_TOC(supabaseUrl), {
        method: 'GET',
        headers: {
          'accept': 'application/json'
        }
      });

      console.log('目录抽取结果:', response);

      if (!response.ok) {
        throw new Error("目录抽取失败");
      }

      const result = await response.json();
      setTocData(result.toc || []);

      toast.success("目录抽取成功");
      handleNext(); // 进入下一步
    } catch (error) {
      toast.error("目录抽取失败: " + (error instanceof Error ? error.message : "未知错误"));
    } finally {
      setIsExtractingTOC(false);
    }
  };

  // 第三步：转换为Markdown
  const handleConvertToMarkdown = async () => {
    if (!documentId) {
      toast.error("请先完成前两步");
      return;
    }

    setIsConvertingMarkdown(true);

    try {
      // 获取token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');

      // 获取文档详情，以获取服务器上的文件路径
      const docResponse = await fetch(API.DOCUMENTS.DETAIL(documentId), {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      });

      if (!docResponse.ok) {
        throw new Error("获取文档详情失败");
      }

      const docDetails = await docResponse.json();
      const filePath = docDetails.file_path;

      if (!filePath) {
        throw new Error("文档路径不存在");
      }

      // 转换 URL 使其在浏览器中可访问
      // 对于 PDF 服务，我们需要使用完整的 Supabase Storage URL
      const supabaseUrl = filePath.replace('/storage-proxy', 'http://host.docker.internal:54321/storage');
      console.log('原始文件路径:', filePath);
      console.log('转换后的文件路径:', supabaseUrl);

      // 使用转换后的文件路径进行Markdown转换
      const response = await fetch(API.SERVICES.PDF.CONVERT_TO_MARKDOWN(supabaseUrl), {
        method: 'GET',
        headers: {
          'accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error("Markdown转换失败");
      }

      const result = await response.json();
      setMarkdownContent(result.markdown_content || "");

      // 如果后台返回了大纲数据，保存它
      if (result.outline && Array.isArray(result.outline)) {
        setMarkdownOutline(result.outline);
        console.log('Markdown大纲:', result.outline);
      }

      toast.success("Markdown转换成功");
      handleNext(); // 进入下一步
    } catch (error) {
      toast.error("Markdown转换失败: " + (error instanceof Error ? error.message : "未知错误"));
    } finally {
      setIsConvertingMarkdown(false);
    }
  };

  // 第四步：生成摘要
  const handleGenerateSummary = async () => {
    if (!markdownContent) {
      toast.error("没有可用的Markdown内容");
      return;
    }

    setIsGeneratingSummary(true);

    try {
      // 只取Markdown内容的前500字用于摘要生成
      const contentPreview = markdownContent.substring(0, 500) + "...(内容已截断)";

      // 准备大纲内容
      let outlineText = "";
      if (markdownOutline && markdownOutline.length > 0) {
        outlineText = "文档大纲结构：\n" +
          markdownOutline.map(item => {
            // 根据标题级别添加缩进
            const indent = "  ".repeat(item.level - 1);
            return `${indent}${item.level}. ${item.title}`;
          }).join("\n");
      }

      // 构建提示
      const prompt = `作为专业文档摘要工程师，请基于以下信息创建一个全面的摘要，不需要思考部分：

      1. 文档大纲：提供了文档的整体结构和主要章节
      2. 文档开头内容：提供了文档的引言和开始部分

      请遵循以下要求：
        * 综合考虑文档大纲和内容，提取文档的核心主题和要点
        * 撰写详细、透彻、深入且结构清晰的摘要，同时保持语言简洁
        * 根据大纲结构，概述文档的主要章节和内容组织
        * 包含主要观点和核心信息，去除冗余语言，专注于关键方面
        * 严格基于提供的文本，不要添加外部信息
        * 如果是论文，应该提取研究背景、方法、结果和结论
        * 总字数控制在500字以内

      文档大纲：
      ${outlineText || "未提供大纲信息"}

      文档开头内容：
      ${contentPreview}
      `;

      // 获取API配置
      let apiUrl, model, apiKey;

      // 检查用户是否选择了模型
      if (!selectedModelId) {
        console.warn('未选择任何模型，无法进行文档总结');
        toast.error("请选择一个模型进行文档总结", {
          description: "未选择模型无法继续操作"
        });
        throw new Error("未选择模型");
      }

      // 查找选中模型的提供商信息
      const selectedProvider = providers?.find(provider =>
        provider.models.some(model => model.id === selectedModelId)
      );

      if (selectedProvider) {
        console.log('使用用户选择的模型:', selectedModelId, '提供商:', selectedProvider.name);

        // 直接处理baseUrl是否有尾部斜杠
        const baseUrl = selectedProvider.baseUrl;
        if (baseUrl.endsWith('/')) {
          apiUrl = `${baseUrl}chat/completions`;
        } else {
          apiUrl = `${baseUrl}/v1/chat/completions`;
        }

        console.log('构建的API URL:', apiUrl);
        model = selectedModelId;
        apiKey = selectedProvider.apiKey;
      } else {
        // 未找到选中模型的提供商信息，显示警告
        console.warn('未找到选中模型的提供商信息');
        toast.error("无法找到所选模型的提供商信息", {
          description: "请选择其他模型或检查模型配置"
        });
        throw new Error("未找到模型提供商信息");
      }

      // 准备请求参数
      const payload = {
        model: model,
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 8192
      };

      // 准备请求头
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // 如果有API密钥，添加到请求头
      if (apiKey) {
        headers['Authorization'] = `Bearer ${apiKey}`;
      }

      // 发送请求
      const summaryResponse = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(payload)
      });

      if (!summaryResponse.ok) {
        throw new Error(`LLM API调用失败: ${summaryResponse.status}`);
      }

      const summaryResult = await summaryResponse.json();

      // 获取原始摘要内容
      let summaryContent = summaryResult.choices[0].message.content;

      // 检查是否包含 think 标签，如果有则去除其中的内容
      const thinkTagRegex = /<think>[\s\S]*?<\/think>/g;
      if (thinkTagRegex.test(summaryContent)) {
        console.log('检测到think标签，正在移除...');
        summaryContent = summaryContent.replace(thinkTagRegex, '');
        // 移除可能存在的多余空行
        summaryContent = summaryContent.replace(/\n{3,}/g, '\n\n');
      }

      setSummary(summaryContent);

      console.log('摘要生成结果:', summaryResult);

      toast.success("摘要生成成功");
      handleNext(); // 进入下一步
    } catch (error) {
      toast.error("摘要生成失败: " + (error instanceof Error ? error.message : "未知错误"));
      // 如果摘要失败，使用原始内容的前200个字符作为摘要
      setSummary(markdownContent.substring(0, 200) + "...");
    } finally {
      setIsGeneratingSummary(false);
    }
  };

  // 第五步：保存到数据库
  const handleSaveDocument = async () => {
    if (!documentId) {
      toast.error("没有可用的文档ID");
      return;
    }

    setIsSaving(true);

    try {
      // 获取token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');

      // 准备更新数据
      const updateData = {
        description: summary,
        markdown_url: markdownContent,
        title: title
      };

      // 发送更新请求
      const updateResponse = await fetch(API.DOCUMENTS.DETAIL(documentId), {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'accept': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!updateResponse.ok) {
        const errorText = await updateResponse.text();
        throw new Error(`文档内容和摘要更新失败: ${errorText}`);
      }

      toast.success("文档内容和摘要已保存到数据库");
      fetchDocuments(); // 刷新文档列表
      onOpenChange(false); // 关闭对话框
    } catch (error) {
      toast.error("保存失败: " + (error instanceof Error ? error.message : "未知错误"));
    } finally {
      setIsSaving(false);
    }
  };

  // 处理当前步骤的操作
  const handleStepAction = () => {
    switch (currentStep) {
      case 'upload':
        return handleUploadDocument();
      case 'toc':
        return handleExtractTOC();
      case 'markdown':
        return handleConvertToMarkdown();
      case 'summary':
        return handleGenerateSummary();
      case 'save':
        return handleSaveDocument();
      default:
        return;
    }
  };

  // 处理关闭对话框
  const handleClose = () => {
    // 重置状态
    setTitle("");
    setFile(null);
    setDocumentType("paper");
    onOpenChange(false);
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 'upload':
        return (
          <div className="space-y-4">
            <div className="flex flex-row justify-between gap-4">
              <div className="w-2/3">
                <Label htmlFor="title" className="text-sm font-medium">
                  文档标题
                </Label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="请输入文档标题或上传文件自动填入"
                  className="mt-1 w-full break-words whitespace-normal overflow-wrap-anywhere"
                  style={{ wordBreak: 'break-word' }}
                />
              </div>

              <div className="w-1/3">
                <Label htmlFor="document-type" className="text-sm font-medium">
                  文档类型
                </Label>
                <Select value={documentType} onValueChange={setDocumentType}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="选择文档类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {documentTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">
                上传文件
              </Label>

              <SimpleFileUploader
                onFileChange={handleFileChange}
                acceptedFileTypes=".pdf,.doc,.docx,.txt,.md"
                maxSizeText="支持 PDF, Word, TXT, Markdown 等格式"
                className="mt-1"
              />
            </div>

            {/* 预览按钮，点击后显示预览 */}
            {documentId && (
              <div className="mt-4">
                <Button
                  type="button"
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={async () => {
                    try {
                      // 获取token
                      const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                      // 获取文档详情，以获取服务器上的文件路径
                      const docResponse = await fetch(API.DOCUMENTS.DETAIL(documentId), {
                        headers: {
                          'Authorization': `Bearer ${token}`,
                          'accept': 'application/json'
                        }
                      });

                      if (!docResponse.ok) {
                        throw new Error("获取文档详情失败");
                      }

                      const docDetails = await docResponse.json();
                      const filePath = docDetails.file_path;

                      if (!filePath) {
                        throw new Error("文档路径不存在");
                      }

                      // 转换 URL 使其在浏览器中可访问
                      const convertedPath = convertSupabaseUrl(filePath);
                      console.log('预览文档 - 原始文件路径:', filePath);
                      console.log('预览文档 - 转换后的文件路径:', convertedPath);

                      // 在新窗口中打开PDF预览
                      window.open(convertedPath, '_blank');
                    } catch (error) {
                      toast.error("预览文档失败: " + (error instanceof Error ? error.message : "未知错误"));
                    }
                  }}
                >
                  <ExternalLink className="h-4 w-4" />
                  在新窗口中预览文档
                </Button>
              </div>
            )}
          </div>
        );

      case 'toc':
        return (
          <div className="space-y-4">
            <div className="text-sm">
              <p className="mb-2">从PDF文档中抽取目录结构，帮助理解文档组织。</p>

              {/* 显示上传的PDF预览 */}
              {documentId && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium mb-2">文档预览</h3>
                  <div className="border rounded-lg overflow-hidden" style={{ height: '300px' }}>
                    {isLoadingFilePath ? (
                      <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-2" />
                        <p className="ml-2">加载文档...</p>
                      </div>
                    ) : filePath ? (
                      <PDFViewer url={filePath} className="h-full w-full" />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <p className="text-red-500">无法加载文档预览</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 目录抽取结果 */}
              {isExtractingTOC ? (
                <div className="flex items-center justify-center h-64">
                  <div className="flex flex-col items-center">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-2" />
                    <p>正在抽取目录...</p>
                  </div>
                </div>
              ) : tocData.length > 0 ? (
                <div className="border rounded-lg p-4 max-h-64 overflow-auto w-full">
                  <ul className="space-y-1 w-full">
                    {tocData.map((item, index) => (
                      <li key={index}
                          style={{ paddingLeft: `${item.level * 12}px` }}
                          className="text-wrap break-words overflow-hidden text-ellipsis"
                      >
                        {item.title} (页码: {item.page})
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="flex items-center justify-center h-24 border rounded-lg">
                  <p className="text-gray-500">点击"抽取目录"按钮开始处理</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'markdown':
        return (
          <div className="space-y-4">
            <div className="text-sm">
              <p className="mb-2">将PDF文档转换为Markdown格式，便于后续处理和展示。</p>

              {/* 显示抽取的目录树 */}
              {tocData.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium mb-2">文档目录结构</h3>
                  <div className="border rounded-lg p-4 max-h-64 overflow-auto w-full">
                    <ul className="space-y-1 w-full">
                      {tocData.map((item, index) => (
                        <li key={index}
                            style={{ paddingLeft: `${item.level * 12}px` }}
                            className="text-wrap break-words overflow-hidden text-ellipsis"
                        >
                          {item.title} (页码: {item.page})
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Markdown转换结果 */}
              {isConvertingMarkdown ? (
                <div className="flex items-center justify-center h-64">
                  <div className="flex flex-col items-center">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-2" />
                    <p>正在转换为Markdown...</p>
                  </div>
                </div>
              ) : markdownContent ? (
                <div>
                  {/* Markdown内容和大纲并排显示 */}
                  <div className="flex flex-row space-x-4">
                    {/* Markdown内容预览 - 左侧 */}
                    <div className="flex-1">
                      <h3 className="text-sm font-medium mb-2">Markdown内容</h3>
                      <div className="border rounded-lg p-4 h-64 overflow-auto w-full">
                        <pre className="text-xs whitespace-pre-wrap break-words w-full">{markdownContent.substring(0, 1000)}...</pre>
                      </div>
                    </div>

                    {/* Markdown大纲显示 - 右侧 */}
                    {markdownOutline.length > 0 && (
                      <div className="flex-1">
                        <h3 className="text-sm font-medium mb-2">文档大纲</h3>
                        <div className="border rounded-lg p-4 h-64 overflow-auto w-full">
                          <ul className="space-y-1 w-full">
                            {markdownOutline.map((item, index) => (
                              <li key={index}
                                  style={{ paddingLeft: `${item.level * 12}px` }}
                                  className="text-wrap break-words overflow-hidden text-ellipsis"
                              >
                                {item.title}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64 border rounded-lg">
                  <p className="text-gray-500">点击"转换Markdown"按钮开始处理</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'summary':
        return (
          <div className="space-y-4">
            <div className="text-sm">
              <p className="mb-2">使用AI生成文档摘要，提取关键信息。</p>

              {/* 显示转换的Markdown内容和大纲（并排显示） */}
              {markdownContent && (
                <div className="mb-4">
                  <div className="flex flex-row space-x-4">
                    {/* Markdown内容预览 - 左侧 */}
                    <div className="flex-1">
                      <h3 className="text-sm font-medium mb-2">Markdown内容预览</h3>
                      <div className="border rounded-lg p-4 h-64 overflow-auto w-full">
                        <div className="w-full break-words">
                          <ReactMarkdown
                            components={MarkdownComponents}
                            remarkPlugins={[remarkGfm, remarkMath]}
                            rehypePlugins={[rehypeKatex]}
                          >
                            {convertMathFormula(markdownContent.substring(0, 500) + "...")}
                          </ReactMarkdown>
                        </div>
                      </div>
                    </div>

                    {/* 文档大纲 - 右侧 */}
                    {markdownOutline.length > 0 && (
                      <div className="flex-1">
                        <h3 className="text-sm font-medium mb-2">文档大纲 (用于摘要生成)</h3>
                        <div className="border rounded-lg p-4 h-64 overflow-auto w-full">
                          <ul className="space-y-1 w-full">
                            {markdownOutline.map((item, index) => (
                              <li key={index}
                                  style={{ paddingLeft: `${item.level * 12}px` }}
                                  className="text-wrap break-words overflow-hidden text-ellipsis"
                              >
                                {item.title}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="mb-4 w-full">
                <Label htmlFor="model-select" className="text-xs font-medium mb-1">选择大模型 (必选)</Label>
                <Select value={selectedModelId} onValueChange={(value) => setSelectedModelId(value)}>
                  <SelectTrigger className="mt-1 w-full">
                    <SelectValue placeholder="-- 请选择模型 --" className="w-full truncate" />
                  </SelectTrigger>
                  <SelectContent className="max-w-[calc(100vw-4rem)]">
                    {/* 不需要默认选项，因为已经有了placeholder */}
                    {providers && providers.length > 0 ? providers.flatMap((provider) =>
                      provider.models.map((model) => (
                        <SelectItem key={`${provider.id}-${model.id}`} value={model.id} className="truncate">
                          {provider.name} / {model.name}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="p-2 text-center text-amber-500 text-xs">
                        没有可用的模型，请先在设置中配置模型
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>

              {/* 摘要生成结果 */}
              <h3 className="text-sm font-medium mb-2">摘要结果</h3>
              {isGeneratingSummary ? (
                <div className="flex items-center justify-center h-64">
                  <div className="flex flex-col items-center">
                    <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-2" />
                    <p>正在生成摘要...</p>
                  </div>
                </div>
              ) : summary ? (
                <div className="border rounded-lg p-4 max-h-64 overflow-auto w-full">
                  <div className="w-full break-words">
                    <ReactMarkdown
                      components={MarkdownComponents}
                      remarkPlugins={[remarkGfm, remarkMath]}
                      rehypePlugins={[rehypeKatex]}
                    >
                      {convertMathFormula(summary)}
                    </ReactMarkdown>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-24 border rounded-lg">
                  <p className="text-gray-500">点击"生成摘要"按钮开始处理</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'save':
        return (
          <div className="space-y-4">
            <div className="text-sm">
              <p className="mb-2">保存处理结果到数据库。</p>

              {/* 显示生成的摘要 */}
              {summary && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium mb-2">文档摘要</h3>
                  <div className="border rounded-lg p-4 max-h-64 overflow-auto w-full">
                    <div className="w-full break-words">
                      <ReactMarkdown
                        components={MarkdownComponents}
                        remarkPlugins={[remarkGfm, remarkMath]}
                        rehypePlugins={[rehypeKatex]}
                      >
                        {convertMathFormula(summary)}
                      </ReactMarkdown>
                    </div>
                  </div>
                </div>
              )}

              <div className="border rounded-lg p-4 w-full">
                <h3 className="font-medium mb-2">文档信息</h3>
                <div className="space-y-2 w-full">
                  <p className="break-words"><span className="font-medium">标题:</span> {title}</p>
                  <p><span className="font-medium">类型:</span> {documentTypes.find(t => t.value === documentType)?.label || documentType}</p>
                  <p><span className="font-medium">目录:</span> {tocData.length > 0 ? `已抽取 (${tocData.length} 项)` : '无'}</p>
                  <p><span className="font-medium">Markdown:</span> {markdownContent ? `已生成 (${markdownContent.length} 字符)` : '无'}</p>
                  <p><span className="font-medium">摘要:</span> {summary ? '已生成' : '无'}</p>
                </div>
              </div>

              {isSaving && (
                <div className="flex items-center justify-center mt-4">
                  <Loader2 className="h-5 w-5 animate-spin text-blue-500 mr-2" />
                  <p>正在保存...</p>
                </div>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // 获取当前步骤的按钮文本
  const getActionButtonText = () => {
    switch (currentStep) {
      case 'upload':
        return uploading ? '上传中...' : '上传文档';
      case 'toc':
        return isExtractingTOC ? '处理中...' : '抽取目录';
      case 'markdown':
        return isConvertingMarkdown ? '转换中...' : '转换Markdown';
      case 'summary':
        return isGeneratingSummary ? '生成中...' : '生成摘要';
      case 'save':
        return isSaving ? '保存中...' : '保存文档';
      default:
        return '下一步';
    }
  };

  // 获取当前步骤的按钮加载状态
  const isActionButtonLoading = () => {
    switch (currentStep) {
      case 'upload':
        return uploading;
      case 'toc':
        return isExtractingTOC;
      case 'markdown':
        return isConvertingMarkdown;
      case 'summary':
        return isGeneratingSummary;
      case 'save':
        return isSaving;
      default:
        return false;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-3xl md:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-500" />
            文档处理流程
          </DialogTitle>
        </DialogHeader>

        {/* 步骤导航 */}
        <div className="mt-4">
          <ul className="relative flex flex-row gap-x-2">
            {steps.map((step, index) => {
              const isActive = currentStep === step.id;
              const isCompleted = completedSteps.has(step.id);
              const isEnabled = activeSteps.has(step.id);

              return (
                <li
                  key={step.id}
                  className={`flex items-center gap-x-2 shrink basis-0 flex-1 group ${isActive ? 'active' : ''}`}
                  onClick={() => isEnabled && goToStep(step.id)}
                >
                  <span className={`min-w-7 min-h-7 group inline-flex items-center text-xs align-middle cursor-pointer ${isEnabled ? '' : 'opacity-50 pointer-events-none'}`}>
                    <span className={`size-7 flex justify-center items-center shrink-0 font-medium rounded-full
                      ${isActive ? 'bg-blue-600 text-white' :
                        isCompleted ? 'bg-green-500 text-white' :
                        'bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white'}`}
                    >
                      {isCompleted ? (
                        <Check className="h-3 w-3" />
                      ) : (
                        <span>{index + 1}</span>
                      )}
                    </span>
                    <span className="ms-2 text-sm font-medium text-gray-800 dark:text-white">
                      {step.label}
                    </span>
                  </span>
                  {index < steps.length - 1 && (
                    <div className={`w-full h-px flex-1 bg-gray-200 group-last:hidden
                      ${isCompleted ? 'bg-green-500' : ''}`}
                    ></div>
                  )}
                </li>
              );
            })}
          </ul>
        </div>

        {/* 步骤内容 */}
        <div className="mt-6">
          {renderStepContent()}
        </div>

        {/* 操作按钮 */}
        <DialogFooter className="flex justify-between items-center gap-x-2 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={handleBack}
            disabled={currentStep === 'upload' || isActionButtonLoading()}
            className="flex items-center gap-1"
          >
            <ArrowLeft className="h-4 w-4" />
            上一步
          </Button>

          <Button
            type="button"
            onClick={handleStepAction}
            disabled={isActionButtonLoading()}
            className="flex items-center gap-1"
          >
            {isActionButtonLoading() && (
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
            )}
            {getActionButtonText()}
            {currentStep !== 'save' && (
              <ArrowRight className="h-4 w-4 ml-1" />
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}