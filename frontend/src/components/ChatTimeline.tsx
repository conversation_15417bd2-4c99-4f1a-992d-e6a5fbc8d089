import { cn } from "@/lib/utils";
import { Save, PlusCircle, ChevronDown, ChevronU<PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Card, CardContent, CardFooter } from "./ui/card";
import { useState, useEffect, useRef } from "react";
import { useMessageStore } from '@/store/messageStore';
import { useAgentStore } from '@/store/agentStore';
import { useHistoryStore } from '@/store/historyStore';
import { useAcademicStore } from '@/store/academicStore';
import markdownDocx, { Packer } from 'markdown-docx';
import WordIcon from '@/assets/word-1.svg'; // 导入Word图标
import API from '@/config/api'; // 导入API配置
import { manualRefreshSession } from '@/utils/session-manager'; // 导入会话管理工具
import { toast } from 'sonner'; // 导入 toast 通知
import { SparklesIcon } from "@/components/ui/sparkles-icon";
// 移除 mockAuth 导入

interface TimelineItem {
  id: number;
  content: string;
  isActive?: boolean;
  type: 'user' | 'assistant';
}

interface ChatTimelineProps {
  items: TimelineItem[];
  onItemClick: (id: number) => void;
}

export function ChatTimeline({ items, onItemClick }: ChatTimelineProps) {
  // 移除 mockAuth 的使用
  const [isSaved, setIsSaved] = useState(false);

  // 折叠状态
  const [isExpanded, setIsExpanded] = useState(false);
  // 内容容器的引用
  const contentRef = useRef<HTMLDivElement>(null);
  // 是否需要显示折叠/展开按钮
  const [showToggle, setShowToggle] = useState(false);

  const { currentSessionId, messages, setCurrentSessionId } = useMessageStore();
  const { startNewConversation } = useHistoryStore();
  const { currentAgent } = useAgentStore();

  // 获取清空当前页面内容的函数
  const { clearAll } = useAcademicStore();

  // 检测内容高度，决定是否显示折叠/展开按钮
  useEffect(() => {
    if (contentRef.current) {
      // 如果内容高度超过500px，显示折叠/展开按钮
      setShowToggle(contentRef.current.scrollHeight > 500);
    }
  }, [items]);

  // 新会话功能
  const startNewSession = () => {
    // 创建新的会话ID
    const newSessionId = startNewConversation();
    // 设置当前会话ID
    setCurrentSessionId(newSessionId);
    // 清空当前页面内容
    clearAll();
    // 重定向到学术页面，加载默认代理
    window.location.href = `/academic?initial=true&agent=${currentAgent}`;
  };

  const saveConversation = async () => {
    if (!currentSessionId || !messages[currentSessionId]) {
      console.error('No current session or messages found');
      return;
    }

    try {
      // 确保 token 有效
      const token = localStorage.getItem('token');
      const refreshToken = localStorage.getItem('refreshToken');

      if (!token || !refreshToken) {
        console.error('保存对话失败: 未找到认证令牌');
        return;
      }

      // 在保存对话前手动刷新会话，确保会话处于活跃状态
      try {
        await manualRefreshSession();
      } catch (refreshError) {
        console.warn('会话刷新失败，但将继续尝试保存对话:', refreshError);
      }

      // 1. 创建会话，使用第一条消息作为会话名称
      const sessionMessages = messages[currentSessionId];
      const firstMessage = sessionMessages[0];

      // 使用API配置中的会话端点，通过查询参数传递title
      const title = encodeURIComponent(firstMessage.content.slice(0, 100));
      const sessionResponse = await fetch(
        `${API.SESSIONS.LIST}?title=${title}`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!sessionResponse.ok) {
        throw new Error('创建会话失败');
      }

      const sessionData = await sessionResponse.json();

      const sessionId = sessionData.id || currentSessionId;

      // 2. 保存所有消息到新的端点
      for (const message of sessionMessages) {
        // 确定正确的角色值
        let role = 'user';
        if (message.role === 'assistant') {
          role = 'assistant';
        }

        // 使用API配置中的消息端点，通过查询参数传递内容和角色
        // 将消息内容分块处理，避免URL过长
        const content = message.content;
        const maxChunkSize = 1000; // 每个块的最大大小

        // 如果内容较短，直接发送
        if (content.length <= maxChunkSize) {
          const messageResponse = await fetch(
            `${API.SESSIONS.MESSAGES(sessionId)}?content=${encodeURIComponent(content)}&role=${role}`,
            {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );

          if (!messageResponse.ok) {
            throw new Error('保存消息失败');
          }
        } else {
          // 如果内容较长，分块处理
          // 先创建一个空消息
          const initialResponse = await fetch(
            `${API.SESSIONS.MESSAGES(sessionId)}?content=&role=${role}`,
            {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          );

          if (!initialResponse.ok) {
            throw new Error('创建初始消息失败');
          }

          // 获取创建的消息ID
          const initialMessage = await initialResponse.json();
          const messageId = initialMessage.id;

          // 使用PUT请求更新消息内容，将内容放在请求体中
          const updateResponse = await fetch(
            `${API.API_PATH}/messages/${messageId}`,
            {
              method: 'PUT',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                content: content
              })
            }
          );

          if (!updateResponse.ok) {
            throw new Error('更新消息内容失败');
          }
        }
      }

      // 3. 设置保存成功状态
      setIsSaved(true);
    } catch (error) {
      console.error('保存对话失败:', error);

      // 检查是否是 token 刷新错误
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('Already Used') || errorMessage.includes('刷新 token 失败')) {
        toast.error('登录已过期', {
          description: '请重新登录后再尝试保存',
          position: 'top-center',
          duration: 4000,
        });
      } else {
        // 显示一般错误消息
        toast.error('保存失败', {
          description: errorMessage.substring(0, 100), // 限制长度
          position: 'top-center',
          duration: 4000,
        });
      }
    }
  };

  // 添加导出Word功能
  const exportToWord = async () => {
    try {
      // 获取学术对话的所有消息
      const { messages: academicMessages } = useAcademicStore.getState();

      if (!academicMessages || academicMessages.length === 0) {
        console.error('没有可导出的对话');
        return;
      }

      // 将对话转换为Markdown格式
      let markdownContent = '# 对话记录\n\n';

      // 添加时间戳
      const now = new Date();
      markdownContent += `导出时间: ${now.toLocaleString()}\n\n`;
      markdownContent += `---\n\n`;

      // 处理所有消息
      academicMessages.forEach((message, index) => {
        const role = message.role === 'user' ? '**用户**' : '**助手**';

        // 添加消息编号
        markdownContent += `## 消息 ${index + 1}\n\n`;
        markdownContent += `${role}:\n\n${message.content}\n\n`;

        // 在消息之间添加分隔线，但最后一条消息后不添加
        if (index < academicMessages.length - 1) {
          markdownContent += `---\n\n`;
        }
      });

      // 使用markdown-docx库转换为docx
      const doc = await markdownDocx(markdownContent);

      // 生成blob用于下载
      const blob = await Packer.toBlob(doc);

      // 创建下载链接
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;

      // 使用当前日期时间作为文件名
      const fileName = `对话记录_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}.docx`;
      a.download = fileName;
      a.click();

      // 清理
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('导出Word文档失败:', error);
    }
  };

  return (
    <Card className="fixed right-10 top-1/2 -translate-y-1/2 w-72
                    bg-background/80 backdrop-blur-sm
                    shadow-lg">
      <CardContent className="p-4 flex flex-col">
        {/* 内容区域 - 根据折叠状态控制高度和滚动条 */}
        <div
          ref={contentRef}
          className={cn(
            "relative overflow-y-auto transition-all duration-300",
            isExpanded ? "max-h-[70vh]" : "max-h-[500px]"
          )}
          style={{ scrollbarWidth: 'thin' }}
        >
          <ol className="relative ml-1.5 border-s border-gray-200 dark:border-gray-700 space-y-6">
            {items.map((item) => (
              <li
                key={item.id}
                className="ms-4"
                onClick={() => onItemClick(item.id)}
              >
                <div className={cn(
                  "absolute w-3 h-3 rounded-full mt-1.5 -start-1.5 border border-white dark:border-gray-900",
                  item.isActive
                    ? "bg-blue-500 dark:bg-blue-400"
                    : "bg-gray-200 dark:bg-gray-700"
                )}></div>
                <div className={cn(
                  "p-3 rounded-lg cursor-pointer transition-colors",
                  "hover:bg-gray-100 dark:hover:bg-gray-800",
                  item.isActive
                    ? "bg-gray-100 dark:bg-gray-800"
                    : "bg-transparent"
                )}>
                  <p className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
                    {item.content}
                  </p>
                </div>
              </li>
            ))}
          </ol>
        </div>

        {/* 折叠/展开按钮 - 只在需要时显示 */}
        {showToggle && (
          <div className="flex justify-center mt-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center gap-1 px-3 py-1 text-xs text-gray-500 bg-gray-100 dark:bg-gray-800 dark:text-gray-400 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              {isExpanded ? (
                <>
                  <ChevronUp className="h-3 w-3" />
                  <span>收起列表</span>
                </>
              ) : (
                <>
                  <ChevronDown className="h-3 w-3" />
                  <span>展开更多</span>
                </>
              )}
            </button>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between pt-0">
        {/* 左侧：新会话按钮 */}
        <div>
          <Button
            variant="ghost"
            size="icon"
            className="p-1.5 transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
            onClick={startNewSession}
            title="新会话"
          >
            <Eraser className="h-4 w-4" />
          </Button>
        </div>

        {/* 右侧：导出和保存按钮 */}
        <div className="flex">
          <Button
            variant="ghost"
            size="icon"
            className="p-1.5 transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
            onClick={exportToWord}
            title="导出为Word文档"
          >
            <img src={WordIcon} alt="Word" className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "p-1.5 transition-colors",
                isSaved
                  ? "text-green-500 hover:text-green-600"
                  : "hover:bg-gray-100 dark:hover:bg-gray-700"
              )}
              onClick={saveConversation}
              disabled={isSaved}
              title={isSaved ? "对话已保存" : "存储对话"}
            >
              <Save className="h-4 w-4" />
            </Button>
            {/* 添加动态灯泡图标 */}
            <div className="transition-opacity duration-300">
              <SparklesIcon size={16} />
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}