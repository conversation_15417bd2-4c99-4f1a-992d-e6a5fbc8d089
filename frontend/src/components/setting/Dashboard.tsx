import { useState, useEffect } from "react";
import { useAuth } from "@/utils/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { API } from '../../config/api';
import axios from 'axios';
import { 
  MessageSquare, 
  FileText, 
  Image, 
  User, 
  Settings, 
  Edit, 
  Calendar,
  Clock,
  Shield,
  Mail,
  UserCheck,
  Activity,
  TrendingUp
} from "lucide-react";

interface Stats {
  totalChats: number;
  totalDocuments: number;
  totalDiagrams: number;
  recentActivities: Activity[];
}

interface Activity {
  id: string;
  type: 'chat' | 'document' | 'diagram';
  action: string;
  timestamp: string;
  details: string;
}

export function Dashboard() {
  const { user } = useAuth();
  const userId = user?.id;

  const [stats, setStats] = useState<Stats>({
    totalChats: 0,
    totalDocuments: 0,
    totalDiagrams: 0,
    recentActivities: []
  });
  const [loading, setLoading] = useState(true);
  
  // 新增状态管理 - 移到这里
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState<any>(null);
  const [updateStatus, setUpdateStatus] = useState<{ userId: string; status: string } | null>(null);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  const [currentUser, setCurrentUser] = useState(user);
                  // 在组件状态声明部分添加
                  const [showAvatarGallery, setShowAvatarGallery] = useState(false);
  // 添加一个刷新计数器状态
  const [refreshKey, setRefreshKey] = useState(0);

  // 所有 useEffect 都移到这里，在条件性返回之前
  useEffect(() => {
    const fetchStats = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        const mockData: Stats = {
          totalChats: 128,
          totalDocuments: 45,
          totalDiagrams: 67,
          recentActivities: [
            {
              id: '1',
              type: 'chat',
              action: '新建对话',
              details: '与 AI 助手讨论论文解读',
              timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString()
            },
            {
              id: '2',
              type: 'document',
              action: '上传文档',
              details: 'Large Language Models 研究论文.pdf',
              timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString()
            },
            {
              id: '3',
              type: 'diagram',
              action: '生成图表',
              details: 'AI 系统架构图',
              timestamp: new Date(Date.now() - 1000 * 60 * 60).toISOString()
            },
            {
              id: '4',
              type: 'chat',
              action: '继续对话',
              details: '探讨深度学习最新进展',
              timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString()
            },
            {
              id: '5',
              type: 'document',
              action: '导入文档',
              details: '深度学习框架对比分析.docx',
              timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString()
            }
          ]
        };

        setStats(mockData);
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchStats();
    }
  }, [userId]);

  // 当user变化时，更新本地状态 - 移到这里
  useEffect(() => {
    setCurrentUser(user);
  }, [user]);

  // 条件性返回移到所有 hooks 之后
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const getUserInitials = () => {
    if (user?.display_name) {
      return user.display_name.split(' ').map(n => n[0]).join('').toUpperCase();
    }
    if (user?.username) {
      return user.username.slice(0, 2).toUpperCase();
    }
    return user?.email?.slice(0, 2).toUpperCase() || 'U';
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };


  // 生成随机头像URL
  const generateRandomAvatar = () => {
    const avatarId = Math.floor(Math.random() * 70) + 1;
    return `https://i.pravatar.cc/150?img=${avatarId}`;
  };

  // 获取用户头像URL
  const getAvatarUrl = (userObj: any): string => {
    // 优先使用 thumbnail，然后是 avatar_url
    if (userObj?.thumbnail || userObj?.avatar_url) {
      return userObj.thumbnail || userObj.avatar_url;
    }
    
    // 如果没有头像，生成基于用户ID的固定头像
    if (userObj?.id) {
      const hash = userObj.id.split('').reduce((a: number, b: string) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
      }, 0);
      const avatarId = Math.abs(hash) % 70 + 1;
      return `https://i.pravatar.cc/150?img=${avatarId}`;
    }
    
    return '';
  };

  // 打开编辑用户信息模态框
  const openEditModal = () => {
    setEditingUser({
      ...currentUser,
      thumbnail: currentUser?.avatar_url || currentUser?.thumbnail || getAvatarUrl(currentUser)
    });
    setShowEditModal(true);
  };

  // 关闭编辑模态框
  const closeEditModal = () => {
    setEditingUser(null);
    setShowEditModal(false);
  };

  // 更新用户信息
  const updateUserInfo = async (userId: string, userInfo: { display_name: string; thumbnail?: string }) => {
    try {
      setUpdateStatus({ userId, status: '处理中...' });
      const token = localStorage.getItem('token');
      const response = await axios.patch(
        API.USER.UPDATE_INFO(userId),
        userInfo,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      setUpdateStatus({ userId, status: '成功' });
      
      // 更新本地用户状态
      const updatedUser = {
        ...currentUser,
        display_name: userInfo.display_name,
        avatar_url: userInfo.thumbnail,
        thumbnail: userInfo.thumbnail
      };
      setCurrentUser(updatedUser);
      
      // 同时更新localStorage中的用户信息
      localStorage.setItem('user', JSON.stringify(updatedUser));
      
      // 显示成功toast
      setToast({ message: '用户信息更新成功！', type: 'success' });
      
      // 关闭对话框
      closeEditModal();
      
      // 清除状态
      setTimeout(() => {
        setUpdateStatus(null);
        setToast(null);
      }, 3000);
    } catch (err: any) {
      setUpdateStatus({ userId, status: '失败' });
      const errorMessage = err.response?.data?.detail || '更新用户信息失败';
      setToast({ message: errorMessage, type: 'error' });
      
      setTimeout(() => {
        setUpdateStatus(null);
        setToast(null);
      }, 3000);
    }
  };

  return (
    <div className="space-y-8">
      {/* Toast 通知 */}
      {toast && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
          toast.type === 'success' 
            ? 'bg-green-100 border border-green-400 text-green-700' 
            : 'bg-red-100 border border-red-400 text-red-700'
        }`}>
          {toast.message}
        </div>
      )}

      {/* 编辑用户信息侧边面板 */}
      {showEditModal && editingUser && (
        <>
          {/* 背景遮罩 */}
          <div 
            className="fixed inset-0 bg-transparent z-40 transition-opacity duration-300"
            onClick={closeEditModal}
          ></div>
          
          {/* 侧边面板 */}
          <div className="fixed inset-y-0 right-0 w-96 bg-white dark:bg-gray-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out">
            <div className="flex flex-col h-full">
              {/* 头部 */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  编辑个人资料
                </h3>
                <button
                  onClick={closeEditModal}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {/* 内容区域 */}
              <div className="flex-1 overflow-y-auto p-6">
                {/* 用户头像预览 - 可点击更换 */}
                <div className="flex flex-col items-center mb-6">
                  <div 
                    className="w-20 h-20 rounded-full overflow-hidden mb-3 cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all"
                    onClick={() => {
                      const newAvatar = generateRandomAvatar();
                      setEditingUser({...editingUser, thumbnail: newAvatar});
                      setRefreshKey(prev => prev + 1); // 强制刷新
                    }}
                    title="点击更换随机头像"
                  >
                    {getAvatarUrl(editingUser) ? (
                      <img 
                        key={`avatar-${refreshKey}`} // 添加key确保重新渲染
                        src={getAvatarUrl(editingUser)} 
                        alt={editingUser.display_name || editingUser.username}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : null}
                    <div className={`w-full h-full bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center ${getAvatarUrl(editingUser) ? 'hidden' : ''}`}>
                      <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    {editingUser.email}<br/>
                    <span className="text-blue-500">点击头像更换随机图标</span>
                  </p>
                </div>
                
                <form 
                  id="edit-user-form"
                  key={editingUser.id}
                  onSubmit={(e) => {
                    e.preventDefault();
                    const formData = new FormData(e.target as HTMLFormElement);
                    const display_name = formData.get('display_name') as string;
                    const thumbnail = formData.get('thumbnail') as string;
                    
                    updateUserInfo(editingUser.id, { 
                      display_name, 
                      thumbnail: thumbnail || undefined
                    });
                  }} 
                  className="space-y-6"
                >
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                      显示名称
                    </label>
                    <input
                      type="text"
                      name="display_name"
                      defaultValue={editingUser.display_name || editingUser.username}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-xs"
                      required
                    />
                  </div>
                  

                  
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                      头像URL（可选）
                    </label>
                    <input
                      type="url"
                      name="thumbnail"
                      value={editingUser.thumbnail || ''}
                      onChange={(e) => {
                        setEditingUser({...editingUser, thumbnail: e.target.value});
                      }}
                      placeholder="https://example.com/avatar.jpg"
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-xs"
                    />
                    
                    {/* 头像选择器按钮 */}
                    <div className="mt-2">
                      <button
                        type="button"
                        onClick={() => setShowAvatarGallery(!showAvatarGallery)}
                        className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {showAvatarGallery ? '隐藏头像库' : '从头像库选择'}
                      </button>
                    </div>
                    
                    {/* 头像橱窗容器 */}
                    {showAvatarGallery && (
                      <div className="mt-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800">
                        <div className="mb-3">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">选择头像</h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400">点击任意头像进行选择</p>
                        </div>
                        
                        {/* 头像网格 */}
                        <div className="grid grid-cols-7 gap-2 max-h-64 overflow-y-auto">
                          {Array.from({ length: 70 }, (_, index) => {
                            const avatarId = index + 1;
                            const avatarUrl = `https://i.pravatar.cc/150?img=${avatarId}`;
                            const isSelected = editingUser.thumbnail === avatarUrl;
                            
                            return (
                              <div
                                key={avatarId}
                                className={`relative cursor-pointer rounded-lg overflow-hidden transition-all hover:scale-105 ${
                                  isSelected 
                                    ? 'ring-2 ring-blue-500 ring-offset-2 ring-offset-gray-50 dark:ring-offset-gray-800' 
                                    : 'hover:ring-2 hover:ring-gray-300 dark:hover:ring-gray-600'
                                }`}
                                onClick={() => {
                                  setEditingUser({...editingUser, thumbnail: avatarUrl});
                                  setRefreshKey(prev => prev + 1);
                                }}
                                title={`头像 ${avatarId}`}
                              >
                                <img
                                  src={avatarUrl}
                                  alt={`Avatar ${avatarId}`}
                                  className="w-12 h-12 object-cover"
                                  loading="lazy"
                                />
                                {isSelected && (
                                  <div className="absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center">
                                    <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                        
                        {/* 快速操作 */}
                        <div className="mt-3 flex justify-between items-center">
                          <button
                            type="button"
                            onClick={() => {
                              const randomId = Math.floor(Math.random() * 70) + 1;
                              const randomUrl = `https://i.pravatar.cc/150?img=${randomId}`;
                              setEditingUser({...editingUser, thumbnail: randomUrl});
                              setRefreshKey(prev => prev + 1);
                            }}
                            className="text-xs text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 flex items-center gap-1"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            随机选择
                          </button>
                          
                          <button
                            type="button"
                            onClick={() => {
                              setEditingUser({...editingUser, thumbnail: ''});
                              setRefreshKey(prev => prev + 1);
                            }}
                            className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 flex items-center gap-1"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            清除头像
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </form>
              </div>

              {/* 图片栏 https://i.pravatar.cc/150?img=** */}

              
              {/* 底部按钮 */}
              <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <button
                  type="button"
                  onClick={closeEditModal}
                  className="px-4 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  form="edit-user-form"
                  disabled={updateStatus?.userId === editingUser.id && updateStatus.status === '处理中...'}
                  className="px-4 py-2 text-xs font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {updateStatus?.userId === editingUser.id && updateStatus.status === '处理中...' ? '保存中...' : '保存更改'}
                </button>
              </div>
            </div>
          </div>
        </>
      )}

      {/* 用户信息头部 */}
      <div className="relative">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-2xl"></div>
        
        <Card className="relative border-0 shadow-lg bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm">
          <CardContent className="p-8">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
              {/* 用户头像 */}
              <div className="relative">
                <Avatar className="h-24 w-24 ring-4 ring-white dark:ring-gray-800 shadow-lg">
                  <AvatarImage src={currentUser?.avatar_url || currentUser?.thumbnail} alt={currentUser?.display_name || currentUser?.username} />
                  <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-1 -right-1 h-6 w-6 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center">
                  <UserCheck className="h-3 w-3 text-white" />
                </div>
              </div>

              {/* 用户基本信息 */}
              <div className="flex-1 space-y-3">
                <div className="flex flex-col md:flex-row md:items-center gap-3">
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                    {currentUser?.display_name || currentUser?.username || '用户'}
                  </h1>
                  <div className="flex gap-2">
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Shield className="h-3 w-3" />
                      {currentUser?.role === 'admin' ? '管理员' : '用户'}
                    </Badge>
                    {currentUser?.is_active && (
                      <Badge variant="default" className="flex items-center gap-1 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        <Activity className="h-3 w-3" />
                        活跃
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-300">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    <span>{currentUser?.email || '未设置邮箱'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>加入时间：{formatDate(currentUser?.created_at)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>最后登录：{formatDate(currentUser?.last_login)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>用户ID：{currentUser?.id?.slice(0, 8)}...</span>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex flex-col gap-3">
                <Button 
                  onClick={openEditModal}
                  className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  <Edit className="h-4 w-4" />
                  编辑资料
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 统计数据卡片 */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">总对话数</CardTitle>
            <div className="p-2 bg-blue-500 rounded-lg group-hover:scale-110 transition-transform">
              <MessageSquare className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-900 dark:text-blue-100">{stats.totalChats}</div>
            <div className="flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 mt-1">
              <TrendingUp className="h-3 w-3" />
              <span>较上月增长 12%</span>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">文档数量</CardTitle>
            <div className="p-2 bg-green-500 rounded-lg group-hover:scale-110 transition-transform">
              <FileText className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-900 dark:text-green-100">{stats.totalDocuments}</div>
            <div className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400 mt-1">
              <TrendingUp className="h-3 w-3" />
              <span>较上月增长 8%</span>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">图表数量</CardTitle>
            <div className="p-2 bg-purple-500 rounded-lg group-hover:scale-110 transition-transform">
              <Image className="h-5 w-5 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-900 dark:text-purple-100">{stats.totalDiagrams}</div>
            <div className="flex items-center gap-1 text-xs text-purple-600 dark:text-purple-400 mt-1">
              <TrendingUp className="h-3 w-3" />
              <span>较上月增长 15%</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 最近活动 */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" />
              最近活动
            </CardTitle>
            <Button variant="ghost" size="sm" className="text-sm text-gray-500 hover:text-gray-700">
              查看全部
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {stats?.recentActivities?.slice(0, 5).map((activity) => (
              <div
                key={activity.id}
                className="flex items-center space-x-4 p-4 rounded-xl bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex-shrink-0">
                  <div className={`p-2 rounded-lg ${
                    activity.type === 'chat' ? 'bg-blue-100 dark:bg-blue-900/50' :
                    activity.type === 'document' ? 'bg-green-100 dark:bg-green-900/50' :
                    'bg-purple-100 dark:bg-purple-900/50'
                  }`}>
                    {activity.type === 'chat' && <MessageSquare className={`h-4 w-4 ${
                      activity.type === 'chat' ? 'text-blue-600 dark:text-blue-400' : ''
                    }`} />}
                    {activity.type === 'document' && <FileText className={`h-4 w-4 ${
                      activity.type === 'document' ? 'text-green-600 dark:text-green-400' : ''
                    }`} />}
                    {activity.type === 'diagram' && <Image className={`h-4 w-4 ${
                      activity.type === 'diagram' ? 'text-purple-600 dark:text-purple-400' : ''
                    }`} />}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {activity.action}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                    {activity.details}
                  </p>
                </div>
                <div className="flex-shrink-0 text-xs text-gray-400 dark:text-gray-500">
                  {new Date(activity.timestamp).toLocaleString('zh-CN', {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}