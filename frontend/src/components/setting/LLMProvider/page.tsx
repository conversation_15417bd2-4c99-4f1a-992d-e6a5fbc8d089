"use client";

import { <PERSON>, Settings, Plus, Trash2, Eye, <PERSON>Off, <PERSON>fresh<PERSON><PERSON>, Loader2 } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useState, useEffect, useRef } from "react";
import { useLLMStore } from '@/store/llmStore';
import { AddProviderDialog } from './AddProviderDialog';
import { Input } from "@/components/ui/input";
import { ModelManageDialog } from './ModelManageDialog';
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import API from '@/config/api';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

// 定义模型接口
interface Model {
  id: string;
  name: string;
  contextWindow?: number;
}

export default function LLMFactory() {
  const { providers, selectedProvider, setSelectedProvider, toggleProvider, updateProvider, deleteProvider, fetchProviders } = useLLMStore();
  
  // Add initialization effect
  useEffect(() => {
    // Fetch providers on component mount
    fetchProviders().then(() => {
      // After fetching, get the latest state
      const currentState = useLLMStore.getState();
      // If we have providers but no selected provider, select the first one
      if (currentState.providers.length > 0 && !currentState.selectedProvider) {
        setSelectedProvider(currentState.providers[0]);
      }
    });
  }, []); // Empty dependency array means this runs once on mount

  const [baseUrl, setBaseUrl] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [apiKey, setApiKey] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // 添加模型管理相关状态
  const [loading, setLoading] = useState(false);
  const [availableModels, setAvailableModels] = useState<Model[]>([]);
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [modelType, setModelType] = useState<'openai' | 'deepseek' | 'qwen'>('openai');

  // 当选中的提供商变化时，更新输入框的值
  useEffect(() => {
    if (selectedProvider) {
      setBaseUrl(selectedProvider.baseUrl || '');
      setApiKey(selectedProvider.apiKey || '');
      setModelType((selectedProvider.type as 'openai' | 'deepseek' | 'qwen') || 'openai');
      
      // 从已有的提供商配置中获取选中的模型
      setSelectedModels(selectedProvider.models.map(model => model.id));
    }
  }, [selectedProvider]);

  // 处理输入变化，使用防抖
  const handleBaseUrlChange = (value: string) => {
    setBaseUrl(value);
    
    // 清除之前的定时器
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    // 设置新的定时器，延迟更新
    updateTimeoutRef.current = setTimeout(() => {
      if (selectedProvider) {
        updateProvider(selectedProvider.id, { baseUrl: value });
      }
    }, 500); // 500毫秒的延迟
  };

  // 处理 API 密钥变化
  const handleApiKeyChange = (value: string) => {
    setApiKey(value);
    
    // 清除之前的定时器
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    // 设置新的定时器，延迟更新
    updateTimeoutRef.current = setTimeout(() => {
      if (selectedProvider) {
        updateProvider(selectedProvider.id, { apiKey: value });
      }
    }, 500); // 500毫秒的延迟
  };

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  // 获取模型列表
  const fetchModels = async () => {
    if (!baseUrl) {
      setError('请先设置 API 地址');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 构建API URL，确保移除末尾的斜杠
      const baseUrlTrimmed = baseUrl.trim().replace(/\/$/, '');

      // 尝试两种可能的API路径格式
      // 1. 标准OpenAI格式: /v1/models
      // 2. LiteLLM可能使用的格式: /models
      const apiUrl = `${baseUrlTrimmed}/v1/models`;
      console.log('请求模型列表URL:', apiUrl);

      const response = await fetch(apiUrl, {
        headers: {
          'Authorization': `Bearer ${apiKey || 'sk-1234'}`,
          'Content-Type': 'application/json'
        }
      });

      // 检查响应类型
      const contentType = response.headers.get('content-type');
      console.log('响应Content-Type:', contentType);

      if (!response.ok) {
        // 尝试获取错误响应文本以便调试
        const errorText = await response.text();
        console.error('API错误响应:', errorText);

        // 检查是否返回了HTML而不是JSON
        if (contentType && contentType.includes('text/html')) {
          console.log('收到HTML响应而不是JSON，尝试备用API路径');

          // 尝试备用路径 - LiteLLM可能使用不同的路径格式
          const fallbackUrl = `${baseUrlTrimmed}/models`;
          console.log('尝试备用URL:', fallbackUrl);

          const fallbackResponse = await fetch(fallbackUrl, {
            headers: {
              'Authorization': `Bearer ${apiKey || 'sk-1234'}`,
              'Content-Type': 'application/json'
            }
          });

          if (!fallbackResponse.ok) {
            const fallbackErrorText = await fallbackResponse.text();
            console.error('备用API错误响应:', fallbackErrorText);
            throw new Error(`获取模型列表失败: ${fallbackResponse.status} ${fallbackResponse.statusText}`);
          }

          // 尝试解析备用响应
          let fallbackData;
          try {
            fallbackData = await fallbackResponse.json();
            console.log('备用API返回数据:', fallbackData);

            // 处理备用响应数据
            if (!fallbackData.data && Array.isArray(fallbackData)) {
              // 如果直接返回数组，适配为标准格式
              fallbackData = { data: fallbackData };
            } else if (!fallbackData.data || !Array.isArray(fallbackData.data)) {
              throw new Error('API返回格式不符合预期');
            }
          } catch (jsonError) {
            console.error('备用API JSON解析错误:', jsonError);

            // 尝试第三种格式 - LiteLLM专用格式
            console.log('尝试LiteLLM专用格式');
            const litellmUrl = `${baseUrlTrimmed}/router/models`;
            console.log('尝试LiteLLM专用URL:', litellmUrl);

            const litellmResponse = await fetch(litellmUrl, {
              headers: {
                'Authorization': `Bearer ${apiKey || 'sk-1234'}`,
                'Content-Type': 'application/json'
              }
            });

            if (!litellmResponse.ok) {
              const litellmErrorText = await litellmResponse.text();
              console.error('LiteLLM专用API错误响应:', litellmErrorText);
              throw new Error(`获取模型列表失败: ${litellmResponse.status} ${litellmResponse.statusText}`);
            }

            fallbackData = await litellmResponse.json();
            console.log('LiteLLM专用API返回数据:', fallbackData);

            // LiteLLM专用格式可能有不同的数据结构
            if (!fallbackData.data && Array.isArray(fallbackData)) {
              // 如果直接返回数组，适配为标准格式
              fallbackData = { data: fallbackData };
            } else if (!fallbackData.data || !Array.isArray(fallbackData.data)) {
              throw new Error('API返回格式不符合预期');
            }
          }

          const models: Model[] = fallbackData.data
            .filter((model: any) => !model.id.includes('text-embedding'))
            .map((model: any) => ({
              id: model.id,
              name: model.id.split('/').pop() || model.id,
              contextWindow: model.context_length || undefined
            }));

          setAvailableModels(models);
          return;
        }

        throw new Error(`获取模型列表失败: ${response.status} ${response.statusText}`);
      }

      // 尝试解析JSON响应
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('JSON解析错误:', jsonError);
        const rawText = await response.text();
        console.log('原始响应内容:', rawText);
        throw new Error('无法解析API响应为JSON');
      }

      // 添加调试日志
      console.log('API返回数据:', data);

      // 处理返回的模型数据
      if (!data.data || !Array.isArray(data.data)) {
        throw new Error('API返回格式不符合预期');
      }

      const models: Model[] = data.data
        // 移除 text-embedding 类型的模型
        .filter((model: any) => !model.id.includes('text-embedding'))
        .map((model: any) => ({
          id: model.id,
          name: model.id.split('/').pop() || model.id,
          contextWindow: model.context_length || undefined
        }));

      setAvailableModels(models);
    } catch (err: any) {
      // 提供更详细的错误信息
      let errorMessage = err.message || '获取模型列表失败';

      // 检查是否是HTML解析错误
      if (errorMessage.includes('Unexpected token') && errorMessage.includes('<!DOCTYPE')) {
        errorMessage = '获取模型列表失败: 服务器返回了HTML而不是JSON。这可能是因为ngrok配置问题或服务器错误。请检查服务器日志。';
        console.error('HTML解析错误，服务器可能返回了错误页面而不是API响应');
      }
      // 检查是否是网络错误
      else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        errorMessage = '获取模型列表失败: 网络连接错误，请检查服务器是否正在运行并且可以访问。';
      }
      // 检查是否是认证错误
      else if (errorMessage.includes('401') || errorMessage.includes('403')) {
        errorMessage = '获取模型列表失败: 认证失败，请检查API密钥是否正确。';
      }

      setError(errorMessage);
      console.error('获取模型列表失败:', err);
    } finally {
      setLoading(false);
    }
  };

  // 处理模型选择变化
  const handleModelToggle = (modelId: string) => {
    setSelectedModels(prev =>
      prev.includes(modelId)
        ? prev.filter(id => id !== modelId)
        : [...prev, modelId]
    );
  };

  // 删除提供商的所有现有模型
  const deleteExistingModels = async (providerName: string, apiBaseUrl: string, apiKey: string) => {
    try {
      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌，请先登录');
      }

      // 获取现有模型列表
      const existingModelsResponse = await fetch(API.MODELS.LIST, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const existingModels = await existingModelsResponse.json();

      // 找出属于当前提供商的所有模型 - 使用provider字段匹配
      const modelsToDelete = existingModels.filter((model: any) =>
        model.provider === providerName &&
        model.base_url === apiBaseUrl &&
        model.credential === apiKey
      );

      console.log(`找到 ${modelsToDelete.length} 个需要删除的现有模型`);

      // 删除这些模型
      for (const model of modelsToDelete) {
        console.log(`删除模型: ${model.model_name}`);
        await fetch(API.MODELS.DETAIL(model.id), {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      }

      return true;
    } catch (error) {
      console.error('删除现有模型失败:', error);
      throw error;
    }
  };

  // 保存到数据库
  const saveToDatabase = async (models: Model[]) => {
    try {
      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌，请先登录');
      }

      // 存储所有创建的模型ID
      const createdModelIds: string[] = [];
      let providerId: string = '';

      // 获取现有模型列表
      const existingModelsResponse = await fetch(API.MODELS.LIST, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const existingModels = await existingModelsResponse.json();

      // 循环插入每个模型
      for (let i = 0; i < models.length; i++) {
        const model = models[i];

        // 检查是否已存在相同配置的模型 - 使用provider字段匹配
        const existingModel = existingModels.find((em: any) =>
          em.model_name === model.id &&
          em.credential === apiKey &&
          em.base_url === baseUrl &&
          em.provider === selectedProvider?.name // 使用provider字段进行比较
        );

        if (existingModel) {
          console.log(`模型 ${model.id} 已存在，跳过插入`);
          createdModelIds.push(existingModel.id);
          if (i === 0) {
            providerId = existingModel.id;
          }
          continue;
        }

        console.log(`保存新模型 ${i+1}/${models.length}: ${model.id}`);

        // 准备请求数据
        // 为每个模型生成唯一名称
        const uniqueName = `${selectedProvider?.name}-${model.id.split('/').pop() || model.id}`;
        const payload = {
          name: uniqueName, // 使用唯一名称
          model_type: modelType,
          model_name: model.id,
          provider: selectedProvider?.name,
          credential: apiKey,
          base_url: baseUrl
        };

        // 创建模型记录
        const response = await fetch(API.MODELS.LIST, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(payload)
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`保存模型 ${model.id} 失败:`, errorText);

          // 检查是否是唯一约束冲突错误
          if (errorText.includes('unique') || errorText.includes('唯一') || errorText.includes('constraint')) {
            console.warn(`模型 ${model.id} 可能存在名称冲突，尝试跳过`);
            // 尝试获取现有模型
            const retryExistingModels = await (await fetch(API.MODELS.LIST, {
              headers: { 'Authorization': `Bearer ${token}` }
            })).json();

            // 再次尝试查找匹配的模型
            const matchingModel = retryExistingModels.find((em: any) =>
              em.model_name === model.id &&
              em.base_url === baseUrl
            );

            if (matchingModel) {
              console.log(`找到匹配的模型 ${model.id}，使用现有ID`);
              createdModelIds.push(matchingModel.id);
              if (i === 0) {
                providerId = matchingModel.id;
              }
              continue; // 跳过当前模型，继续处理下一个
            }
          }

          throw new Error(`保存模型 ${model.id} 失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`模型 ${model.id} 保存成功，返回数据:`, data);

        createdModelIds.push(data.id);

        // 保存第一个模型的ID作为提供商ID
        if (i === 0) {
          providerId = data.id;
        }
      }

      console.log(`成功处理了 ${createdModelIds.length} 个模型，提供商ID: ${providerId}`);
      return providerId;
    } catch (error) {
      console.error('保存到数据库失败:', error);
      throw error;
    }
  };

  // 保存选定的模型
  const handleSaveModels = async () => {
    if (!baseUrl || !apiKey || !selectedProvider) {
      setError('请填写完整的 API 信息');
      return;
    }

    if (selectedModels.length === 0) {
      setError('请至少选择一个模型');
      return;
    }

    setLoading(true);

    try {
      // 从可用模型中筛选出用户选中的模型
      const updatedModels = availableModels
        .filter(model => selectedModels.includes(model.id))
        .map(model => ({
          id: model.id,
          name: model.id.split('/').pop() || model.id, // 使用简化的模型名称
          contextWindow: model.contextWindow
        }));

      // 0. 先删除该提供商的所有现有模型
      await deleteExistingModels(selectedProvider.name, baseUrl, apiKey);

      // 1. 保存到数据库
      const providerId = await saveToDatabase(updatedModels);

      // 2. 更新本地状态
      const providerType = modelType;

      const updatedProvider = {
        id: providerId,
        name: selectedProvider.name,
        type: providerType,
        apiKey: apiKey,
        baseUrl: baseUrl,
        models: updatedModels,
        enabled: true,
        icon: selectedProvider.name.charAt(0).toUpperCase(),
        color: 'bg-blue-500'
      };

      // 强制刷新数据，绕过缓存机制
      useLLMStore.setState({ lastFetchTime: 0 }); // 重置上次获取时间，强制刷新
      await useLLMStore.getState().fetchProviders();

      // 从刷新后的数据中找到对应的提供商
      const refreshedProviders = useLLMStore.getState().providers;
      const refreshedProvider = refreshedProviders.find(p =>
        p.name === selectedProvider.name &&
        p.baseUrl === baseUrl &&
        p.apiKey === apiKey
      );

      if (refreshedProvider) {
        // 更新当前选中的提供商
        useLLMStore.getState().setSelectedProvider(refreshedProvider);
        // 强制更新本地状态，确保UI刷新
        setSelectedProvider(refreshedProvider);
      } else {
        // 如果找不到刷新后的提供商，使用更新后的数据
        await useLLMStore.getState().addProvider(updatedProvider);
        useLLMStore.getState().setSelectedProvider(updatedProvider);
        // 强制更新本地状态
        setSelectedProvider(updatedProvider);
      }

      // 显示成功消息
      setError(null);
      setSuccessMessage(`成功保存了 ${updatedModels.length} 个模型！`);

      // 3秒后清除成功消息
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err: any) {
      // 提供更详细的错误信息
      let errorMessage = err.message || '保存失败';

      // 检查是否是数据库唯一约束错误
      if (errorMessage.includes('unique') || errorMessage.includes('唯一') || errorMessage.includes('constraint')) {
        errorMessage = '保存失败：模型名称冲突。请使用不同的提供商名称或检查是否已存在相同配置的模型。';
      }
      // 检查是否是网络错误
      else if (errorMessage.includes('network') || errorMessage.includes('网络')) {
        errorMessage = '保存失败：网络连接错误，请检查您的网络连接。';
      }
      // 检查是否是认证错误
      else if (errorMessage.includes('401') || errorMessage.includes('403') || errorMessage.includes('认证')) {
        errorMessage = '保存失败：认证失败，请重新登录后再试。';
      }

      setError(errorMessage);
      setSuccessMessage(null);
      console.error('保存失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveModel = async (modelId: string) => {
    if (!selectedProvider) return;

    const updatedModels = selectedProvider.models.filter(model => model.id !== modelId);

    // 更新 provider 并获取更新后的数据
    await updateProvider(selectedProvider.id, {
      ...selectedProvider,
      models: updatedModels
    });

    // 强制重新选择当前 provider 以刷新界面
    setSelectedProvider({
      ...selectedProvider,
      models: updatedModels
    });
  };
  
  // 处理删除提供商
  const handleDeleteProvider = async () => {
    if (!selectedProvider) return;

    setIsDeleting(true);
    setDeleteError(null);

    try {
      // 调用删除函数
      const success = await deleteProvider(selectedProvider.id);

      if (success) {
        // 刷新提供商列表
        await fetchProviders();
      } else {
        setDeleteError('删除提供商失败，请重试');
      }
    } catch (error) {
      console.error('删除提供商时出错:', error);
      setDeleteError('删除提供商时出错: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsDeleting(false);
    }
  };

  // 渲染提供商详情
  const renderProviderDetails = () => {
    if (!selectedProvider) return null;
    
    return (
      <>
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-2">
            <h1 className="text-xl font-medium">{selectedProvider.name}</h1>
            <Label htmlFor={`provider-${selectedProvider.id}`} className="text-sm text-gray-500">
              {selectedProvider.enabled ? '已启用' : '已禁用'}
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id={`provider-${selectedProvider.id}`}
              checked={selectedProvider.enabled || false}
              onCheckedChange={() => toggleProvider(selectedProvider.id)}
            />

            {/* 删除提供商按钮 */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <button
                  className="ml-2 p-2 text-gray-500 hover:text-red-500 rounded-md hover:bg-gray-100"
                  title="删除提供商"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>确认删除</AlertDialogTitle>
                  <AlertDialogDescription>
                    您确定要删除提供商 "{selectedProvider.name}" 及其所有模型吗？此操作无法撤销。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                {deleteError && (
                  <div className="bg-red-50 p-3 rounded-md text-red-500 text-sm mb-4">
                    {deleteError}
                  </div>
                )}
                <AlertDialogFooter>
                  <AlertDialogCancel disabled={isDeleting}>取消</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={(e) => {
                      e.preventDefault();
                      handleDeleteProvider();
                    }}
                    disabled={isDeleting}
                    className="bg-red-500 hover:bg-red-600 text-white"
                  >
                    {isDeleting ? '删除中...' : '确认删除'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <h2 className="text-sm font-medium mb-2">API 密钥</h2>
            <div className="flex">
              <input
                type={showApiKey ? "text" : "password"}
                value={showApiKey ? apiKey : "••••••••••••"}
                onChange={(e) => handleApiKeyChange(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-200 rounded-lg bg-white"
                readOnly={!showApiKey}
              />
              <button
                className="ml-2 px-3 py-2 border border-gray-200 rounded-lg text-sm flex items-center"
                onClick={() => setShowApiKey(!showApiKey)}
                title={showApiKey ? "隐藏 API 密钥" : "显示 API 密钥"}
              >
                {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <div>
            <h2 className="text-sm font-medium mb-2">API 地址</h2>
            <Input
              value={baseUrl}
              onChange={(e) => handleBaseUrlChange(e.target.value)}
              className="w-full"
            />
            <div className="mt-1 flex items-center text-xs text-gray-500">
              <span>
                {baseUrl ? (
                  baseUrl.endsWith('/') ? 
                    `${baseUrl}chat/completions` : 
                    `${baseUrl}/v1/chat/completions`
                ) : ''}
              </span>
              <div className="flex-1"></div>
              <span>请确保您的IP已在白名单内</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <h2 className="text-sm font-medium mb-2">模型类型</h2>
            <div className="flex space-x-2">
              <Select
                value={modelType}
                onValueChange={(value: 'openai' | 'deepseek' | 'qwen') => setModelType(value)}
              >
                <SelectTrigger id="model-type" className="w-full">
                  <SelectValue placeholder="选择模型类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="deepseek">DeepSeek</SelectItem>
                  <SelectItem value="qwen">Qwen</SelectItem>
                </SelectContent>
              </Select>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    onClick={(e) => {
                      // e.preventDefault(); // 尝试移除或注释掉这一行
                      fetchModels();
                    }}
                    disabled={!baseUrl || loading}
                    variant="outline"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        获取中...
                      </>
                    ) : (
                      '获取模型'
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  {/* 错误和成功消息 */}
                  {error && (
                    <div className="py-2 px-4 text-center border-b border-gray-100">
                      <p className="text-red-500 text-sm">{error}</p>
                    </div>
                  )}

                  {successMessage && (
                    <div className="py-2 px-4 text-center border-b border-gray-100">
                      <p className="text-green-500 text-sm">{successMessage}</p>
                    </div>
                  )}

                  {/* 可用模型列表 - Popover形式 */}
                  {availableModels.length > 0 ? (
                    <div>
                      <div className="p-4 border-b border-gray-100">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">可用模型列表</span>
                          <span className="text-xs text-gray-500">
                            已选择 {selectedModels.length}/{availableModels.length}
                          </span>
                        </div>
                      </div>
                      <div className="max-h-[300px] overflow-y-auto p-2">
                        {availableModels.map((model) => (
                          <div
                            key={model.id}
                            className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded-md"
                          >
                            <Checkbox
                              id={`model-${model.id}`}
                              checked={selectedModels.includes(model.id)}
                              onCheckedChange={() => handleModelToggle(model.id)}
                            />
                            <div className="flex-1">
                              <label
                                htmlFor={`model-${model.id}`}
                                className="text-sm font-medium cursor-pointer block"
                              >
                                {model.name.split('-').join(' ')}
                              </label>
                              <span className="text-xs text-gray-500">
                                {model.id}
                              </span>
                            </div>
                            {model.contextWindow && (
                              <span className="px-2 py-1 bg-gray-50 rounded-md text-xs text-gray-600">
                                {model.contextWindow}k
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                      
                      {/* 保存模型按钮 */}
                      <div className="p-4 border-t border-gray-100">
                        <Button
                          onClick={handleSaveModels}
                          disabled={loading || selectedModels.length === 0}
                          className="bg-green-500 hover:bg-green-600 text-white w-full"
                        >
                          {loading ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              保存中...
                            </>
                          ) : (
                            `保存已选 (${selectedModels.length})`
                          )}
                        </Button>
                      </div>
                    </div>
                  ) : loading ? (
                    <div className="p-8 text-center">
                      <Loader2 className="h-8 w-8 mx-auto animate-spin text-gray-400" />
                      <p className="mt-2 text-sm text-gray-500">获取模型列表中...</p>
                    </div>
                  ) : (
                    <div className="p-8 text-center">
                      <p className="text-sm text-gray-500">暂无可用模型</p>
                      <p className="text-xs text-gray-400 mt-1">请检查API地址和密钥是否正确</p>
                    </div>
                  )}
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* 错误和成功消息 */}
          {error && (
            <div className="py-2 text-center">
              <p className="text-red-500">{error}</p>
            </div>
          )}

          {successMessage && (
            <div className="py-2 text-center">
              <p className="text-green-500">{successMessage}</p>
            </div>
          )}


          <div>
            <h2 className="text-sm font-medium mb-2 flex items-center">
              已保存模型
              <Search className="ml-2 h-4 w-4 text-gray-400" />
            </h2>

            <div className="space-y-2">
              {selectedProvider.models.length > 0 ? (
                selectedProvider.models.map((model) => (
                  <div
                    key={model.id}
                    className="flex items-center space-x-2 p-3 hover:bg-gray-50 rounded-lg border border-gray-100"
                  >
                    <div className="flex-1">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-100 to-blue-100 flex items-center justify-center mr-3">
                          <span className="text-purple-600 font-medium">
                            {model.id.split('-')[0][0].toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium">
                            {model.id.split('-').join(' ')}
                          </div>
                          <span className="text-xs text-gray-500">
                            {model.id}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {model.contextWindow && (
                        <span className="px-2 py-1 bg-gray-50 rounded-md text-xs text-gray-600">
                          {model.contextWindow}k tokens
                        </span>
                      )}
                      <button
                        onClick={() => handleRemoveModel(model.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors"
                        title="移除模型"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  暂无已保存的模型，请先获取并保存模型
                </div>
              )}
            </div>
          </div>

          <div className="flex space-x-2">
            {/* <ModelManageDialog /> */}
            <button
              className="px-4 py-2 border border-gray-200 rounded-lg text-sm flex items-center"
              onClick={() => fetchProviders()}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              刷新
            </button>
          </div>
        </div>
      </>
    );
  };

  // 渲染空状态
  const renderEmptyState = () => {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-2rem)] text-center">
        <div className="w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center mb-4">
          <Settings className="h-8 w-8 text-gray-400" />
        </div>
        <h2 className="text-xl font-medium mb-2">请选择或添加一个提供商</h2>
        <p className="text-gray-500 mb-6 max-w-md">
          从左侧列表选择一个现有的提供商，或点击底部的"添加"按钮创建一个新的提供商。
        </p>
        <AddProviderDialog />
      </div>
    );
  };

  return (
    <div className="flex h-full bg-gray-50 dark:bg-neutral-900">
      {/* Left Sidebar */}
      <div className="w-64 border-r border-gray-200 bg-white overflow-y-auto relative">
        <div className="p-4">
          <div className="relative">
            <input
              type="text"
              placeholder="搜索提供商"
              className="w-full pl-8 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-gray-300"
            />
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
          </div>
        </div>

        <div className="space-y-1 pb-16">
          {providers.map((provider) => (
            <div
              key={provider.id}
              className={`flex items-center justify-between px-4 py-2 hover:bg-gray-100 cursor-pointer ${
                provider.id === selectedProvider?.id ? "bg-gray-100" : ""
              }`}
              onClick={async () => {
                // 先刷新数据，然后再切换提供商
                await fetchProviders();
                // 从刷新后的数据中找到对应的提供商
                const refreshedProviders = useLLMStore.getState().providers;
                const refreshedProvider = refreshedProviders.find(p => p.id === provider.id);
                if (refreshedProvider) {
                  setSelectedProvider(refreshedProvider);
                } else {
                  // 如果找不到刷新后的提供商（可能已被删除），使用原始数据
                  setSelectedProvider(provider);
                }
              }}
            >
              <div className="flex items-center">
                <div className={`w-6 h-6 rounded flex items-center justify-center text-white text-xs ${provider.color}`}>
                  {provider.icon}
                </div>
                <span className="ml-3 text-sm">{provider.name}</span>
              </div>
              <svg
                viewBox="0 0 24 24"
                className={`w-4 h-4 ${provider.enabled ? 'text-green-500' : 'text-gray-300'}`}
                fill="currentColor"
              >
                <circle cx="12" cy="12" r="4" />
              </svg>
            </div>
          ))}
        </div>

        <div className="absolute bottom-4 left-4 right-4 flex space-x-2">
          <AddProviderDialog />
        </div>
      </div>

      {/* Right Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          {selectedProvider ? renderProviderDetails() : renderEmptyState()}
        </div>
      </div>
    </div>
  );
}
