import { useState, useEffect } from 'react';
import { API } from '../../config/api';
import axios from 'axios';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Trash2, Edit, Plus, Users, MessageSquare } from 'lucide-react';

type Channel = {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  icon?: string;
  color?: string;
  is_active: boolean;
  is_default: boolean;
  post_count: number;
  member_count: number;
  created_at: string;
  created_by: string;
  moderator_id?: string;
};

type User = {
  id: string;
  username: string;
  display_name?: string;
  email: string;
  thumbnail?: string;
};

type NewChannel = {
  name: string;
  display_name: string;
  description: string;
  color: string;
  is_active: boolean;
  moderator_id?: string;
};

export function ChannelManagement() {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingChannel, setEditingChannel] = useState<Channel | null>(null);
  const [newChannel, setNewChannel] = useState<NewChannel>({
    name: '',
    display_name: '',
    description: '',
    color: '#3b82f6',
    is_active: true,
    moderator_id: undefined
  });

  // 获取频道列表
  const fetchChannels = async () => {
    try {
      const response = await axios.get(API.CHANNELS.LIST, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      setChannels(response.data);
    } catch (error) {
      console.error('获取频道列表失败:', error);
      toast.error('获取频道列表失败');
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await axios.get(API.USER.ALL, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      setUsers(response.data);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      toast.error('获取用户列表失败');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchChannels(), fetchUsers()]);
      setLoading(false);
    };
    loadData();
  }, []);

  // 创建频道
  const handleCreateChannel = async () => {
    if (!newChannel.name.trim() || !newChannel.display_name.trim()) {
      toast.error('请填写频道名称和显示名称');
      return;
    }

    try {
      await axios.post(API.CHANNELS.LIST, newChannel, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      toast.success('频道创建成功');
      setIsCreateDialogOpen(false);
      setNewChannel({
        name: '',
        display_name: '',
        description: '',
        color: '#3b82f6',
        is_active: true,
        moderator_id: undefined
      });
      fetchChannels();
    } catch (error) {
      console.error('创建频道失败:', error);
      toast.error('创建频道失败');
    }
  };

  // 更新频道
  const handleUpdateChannel = async () => {
    if (!editingChannel) return;

    try {
      const updateData = {
        display_name: editingChannel.display_name,
        description: editingChannel.description,
        icon: editingChannel.icon,
        color: editingChannel.color,
        is_active: editingChannel.is_active,
        moderator_id: editingChannel.moderator_id
      };

      await axios.put(`${API.CHANNELS.LIST}/${editingChannel.id}`, updateData, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      toast.success('频道更新成功');
      setIsEditDialogOpen(false);
      setEditingChannel(null);
      fetchChannels();
    } catch (error) {
      console.error('更新频道失败:', error);
      toast.error('更新频道失败');
    }
  };

  // 删除频道
  const handleDeleteChannel = async (channelId: string) => {
    if (!confirm('确定要删除这个频道吗？此操作不可撤销。')) {
      return;
    }

    try {
      await axios.delete(`${API.CHANNELS.LIST}/${channelId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      toast.success('频道删除成功');
      fetchChannels();
    } catch (error) {
      console.error('删除频道失败:', error);
      toast.error('删除频道失败');
    }
  };

  // 切换频道状态
  const toggleChannelStatus = async (channel: Channel) => {
    try {
      await axios.put(`${API.CHANNELS.LIST}/${channel.id}`, {
        display_name: channel.display_name,
        description: channel.description,
        icon: channel.icon,
        color: channel.color,
        is_active: !channel.is_active
      }, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      toast.success(`频道已${!channel.is_active ? '启用' : '禁用'}`);
      fetchChannels();
    } catch (error) {
      console.error('切换频道状态失败:', error);
      toast.error('切换频道状态失败');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">板块管理</h2>
          <p className="text-muted-foreground">管理系统中的所有板块频道</p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              创建板块
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>创建新板块</DialogTitle>
              <DialogDescription>
                填写板块信息来创建一个新的讨论板块
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  板块名称
                </Label>
                <Input
                  id="name"
                  value={newChannel.name}
                  onChange={(e) => setNewChannel({ ...newChannel, name: e.target.value })}
                  className="col-span-3"
                  placeholder="例如: technology"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="display_name" className="text-right">
                  显示名称
                </Label>
                <Input
                  id="display_name"
                  value={newChannel.display_name}
                  onChange={(e) => setNewChannel({ ...newChannel, display_name: e.target.value })}
                  className="col-span-3"
                  placeholder="例如: 科技讨论"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  描述
                </Label>
                <Textarea
                  id="description"
                  value={newChannel.description}
                  onChange={(e) => setNewChannel({ ...newChannel, description: e.target.value })}
                  className="col-span-3"
                  placeholder="板块描述..."
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="color" className="text-right">
                  主题色
                </Label>
                <Input
                  id="color"
                  type="color"
                  value={newChannel.color}
                  onChange={(e) => setNewChannel({ ...newChannel, color: e.target.value })}
                  className="col-span-3 h-10"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="moderator" className="text-right">
                  版主
                </Label>
                <Select
                  value={newChannel.moderator_id || ''}
                  onValueChange={(value) => setNewChannel({ ...newChannel, moderator_id: value || undefined })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="选择版主（可选）" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.display_name || user.username}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="is_active" className="text-right">
                  启用状态
                </Label>
                <Switch
                  id="is_active"
                  checked={newChannel.is_active}
                  onCheckedChange={(checked) => setNewChannel({ ...newChannel, is_active: checked })}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleCreateChannel}>
                创建板块
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4">
        {channels.map((channel) => (
          <Card key={channel.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: channel.color || '#3b82f6' }}
                  />
                  <div>
                    <CardTitle className="text-lg">r/{channel.name}</CardTitle>
                    <CardDescription>{channel.display_name}</CardDescription>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={channel.is_active ? "default" : "secondary"}>
                    {channel.is_active ? "启用" : "禁用"}
                  </Badge>
                  {channel.is_default && (
                    <Badge variant="outline">默认</Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {channel.description || "暂无描述"}
                </p>

                <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>{channel.member_count} 成员</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageSquare className="h-4 w-4" />
                    <span>{channel.post_count} 帖子</span>
                  </div>
                  <div>
                    创建时间: {channel.created_at ? new Date(channel.created_at).toLocaleDateString() : '未知'}
                  </div>
                </div>
                <div className="flex items-center mt-2 text-sm text-muted-foreground">
                  <span className="font-medium">版主:</span>
                  <span className="ml-2">
                    {channel.moderator_id && users.find(user => user.id === channel.moderator_id)
                      ? (users.find(user => user.id === channel.moderator_id)?.display_name ||
                        users.find(user => user.id === channel.moderator_id)?.username)
                      : '暂无版主'}
                  </span>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={channel.is_active}
                      onCheckedChange={() => toggleChannelStatus(channel)}
                    />
                    <Label className="text-sm">
                      {channel.is_active ? "启用" : "禁用"}
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingChannel(channel);
                        setIsEditDialogOpen(true);
                      }}
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      编辑
                    </Button>
                    {!channel.is_default && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteChannel(channel.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        删除
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 编辑对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>编辑板块</DialogTitle>
            <DialogDescription>
              修改板块信息
            </DialogDescription>
          </DialogHeader>
          {editingChannel && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit_name" className="text-right">
                  板块名称
                </Label>
                <Input
                  id="edit_name"
                  value={editingChannel.name}
                  onChange={(e) => setEditingChannel({ ...editingChannel, name: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit_display_name" className="text-right">
                  显示名称
                </Label>
                <Input
                  id="edit_display_name"
                  value={editingChannel.display_name}
                  onChange={(e) => setEditingChannel({ ...editingChannel, display_name: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit_description" className="text-right">
                  描述
                </Label>
                <Textarea
                  id="edit_description"
                  value={editingChannel.description || ''}
                  onChange={(e) => setEditingChannel({ ...editingChannel, description: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit_color" className="text-right">
                  主题色
                </Label>
                <Input
                  id="edit_color"
                  type="color"
                  value={editingChannel.color || '#3b82f6'}
                  onChange={(e) => setEditingChannel({ ...editingChannel, color: e.target.value })}
                  className="col-span-3 h-10"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit_moderator" className="text-right font-medium">
                  版主
                </Label>
                <div className="col-span-3">
                  <Select
                    value={editingChannel.moderator_id || ''}
                    onValueChange={(value) => setEditingChannel({ ...editingChannel, moderator_id: value || undefined })}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="选择版主（可选）" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.display_name || user.username}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button type="submit" onClick={handleUpdateChannel}>
              保存更改
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}