import { Button } from "@/components/ui/button";
import { useState } from "react";

export function About() {
  const [changelogOpen, setChangelogOpen] = useState(false);
  return (
    <div className="space-y-6">
      {/* 头部信息 */}
      <div className="flex items-center gap-4">
        {/* <img
          src="public/3stooges.jpg"
          alt="3Stooges Logo"
          className="h-20 rounded-lg"
        /> */}
        <div className="ml-4">
          <h2 className="text-2xl font-bold relative inline-block">
            3Stooges Thinking
            <span className="absolute -top-2 -right-12 px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">
              v0.4.0
            </span>
          </h2>
          <p className="text-gray-600 dark:text-neutral-400 mt-2">学无止境...</p>
        </div>
      </div>


      {/* 链接列表 */}
      <div className="space-y-4">
        <a
          href="#"
          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-neutral-900 rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800 transition-colors"
        >
          <div className="flex items-center gap-3">
            <svg className="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
            </svg>
            <span>意见反馈</span>
          </div>
          <span className="text-gray-400">反馈</span>
        </a>

        <div className="bg-gray-50 dark:bg-neutral-900 rounded-lg overflow-hidden">
          <div
            className="flex items-center justify-between p-4 hover:bg-gray-100 dark:hover:bg-neutral-800 transition-colors cursor-pointer"
            onClick={() => setChangelogOpen(!changelogOpen)}
          >
            <div className="flex items-center gap-3">
              <svg className="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M3 15v4c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2v-4M17 8l-5-5-5 5M12 4.2v10.3"/>
              </svg>
              <span>更新日志</span>
            </div>
            <span className="text-gray-400">查看</span>
          </div>

          <div className={`${changelogOpen ? '' : 'hidden'} p-4 border-t border-gray-200 dark:border-neutral-800 space-y-4 max-h-80 overflow-y-auto`}>
          <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">v0.5.0</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">2025-08-08</span>
              </div>
              <ul className="text-sm space-y-1 list-disc list-inside ml-2">
                <li>0.8.0 released</li>
                <li>Deep Research!</li>
              </ul>
            </div>
          
          <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">v0.4.0</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">2025-07-21</span>
              </div>
              <ul className="text-sm space-y-1 list-disc list-inside ml-2">
                <li>0.4.0 released</li>
                <li>Reddit Style</li>
              </ul>
            </div>

          <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">v0.3.1</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">2025-07-15</span>
              </div>
              <ul className="text-sm space-y-1 list-disc list-inside ml-2">
                <li>0.3.1 released</li>
                <li>weibo @</li>
              </ul>
            </div>
          <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">v0.3.0</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">2025-07-15</span>
              </div>
              <ul className="text-sm space-y-1 list-disc list-inside ml-2">
                <li>0.3.0 released</li>
                <li>Context engineering inside</li>
                <li>documents, weibo, thoughts</li>
              </ul>
            </div>
          <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">v0.2.1</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">2025-06-04</span>
              </div>
              <ul className="text-sm space-y-1 list-disc list-inside ml-2">
                <li>Docker Compose部署</li>
                <li>修复模型管理</li>
                <li>添加Agentic Memory</li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">v0.1.32</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">2025-05-15</span>
              </div>
              <ul className="text-sm space-y-1 list-disc list-inside ml-2">
                <li>添加 Agent Popover 组件，支持用户选择不同的代理</li>
                <li>修复 Anthropic 代理执行问题</li>
                <li>优化图片预览功能，支持鼠标放缩</li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">v0.1.31</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">2025-05-10</span>
              </div>
              <ul className="text-sm space-y-1 list-disc list-inside ml-2">
                <li>添加微博图片预览功能</li>
                <li>优化 UI 组件透明度和悬停效果</li>
                <li>修复 API 请求错误处理</li>
              </ul>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="px-2 py-0.5 text-xs bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 rounded">v0.1.30</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">2025-05-05</span>
              </div>
              <ul className="text-sm space-y-1 list-disc list-inside ml-2">
                <li>添加版本号上标显示</li>
                <li>优化对话界面响应速度</li>
                <li>添加 Qwen 模型支持</li>
                <li>修复暗色模式下的显示问题</li>
              </ul>
            </div>
          </div>
        </div>

        <a
          href="#"
          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-neutral-900 rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-800 transition-colors"
        >
          <div className="flex items-center gap-3">
            <svg className="size-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Z"/>
              <path d="M12 8v4M12 16h.01"/>
            </svg>
            <span>A software of LAOBAO (Vibe Coding only One)</span>
          </div>
          <span className="text-gray-400">查看</span>
        </a>
      </div>

    </div>
  );
}