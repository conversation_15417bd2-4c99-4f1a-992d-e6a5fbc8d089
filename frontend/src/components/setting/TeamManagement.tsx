import { useState, useEffect } from 'react';
import { API } from '../../config/api';
import axios from 'axios';

type User = {
  id: string;
  email: string;
  username: string;
  display_name: string;
  role: string;
  is_active: boolean;
  created_at: string;
  last_login: string | null;
  thumbnail?: string; // 改为thumbnail字段
};

export function TeamManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updateStatus, setUpdateStatus] = useState<{ userId: string; status: string } | null>(null);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  // 获取所有用户
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(API.USER.ALL, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      // 添加调试日志 - 检查API返回的数据结
      
      setUsers(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || '获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };


  // 生成随机头像URL
  const generateRandomAvatar = () => {
    // 直接生成随机头像，不考虑当前是否已有头像
    const avatarId = Math.floor(Math.random() * 70) + 1; // pravatar.cc 有70个头像
    return `https://i.pravatar.cc/150?img=${avatarId}`;
  };

  // 更新用户头像
  const updateUserThumbnail = async (userId: string, thumbnailUrl: string) => {
    try {
      setUpdateStatus({ userId, status: '处理中...' });
      const token = localStorage.getItem('token');
      await axios.patch(
        `${API.USER.BASE}/${userId}/info`,
        { thumbnail: thumbnailUrl },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setUpdateStatus({ userId, status: '成功' });
      // 更新本地用户列表
      setUsers(users.map(user => 
        user.id === userId ? { ...user, thumbnail: thumbnailUrl } : user
      ));
      // 如果正在编辑这个用户，也更新编辑状态
      if (editingUser && editingUser.id === userId) {
        setEditingUser({ ...editingUser, thumbnail: thumbnailUrl });
      }
      setTimeout(() => setUpdateStatus(null), 2000);
    } catch (err: any) {
      setUpdateStatus({ userId, status: '失败' });
      setError(err.response?.data?.detail || '更新用户头像失败');
      setTimeout(() => setUpdateStatus(null), 2000);
    }
  };

  // 更新用户状态（激活/停用）
  const updateUserStatus = async (userId: string, isActive: boolean) => {
    try {
      setUpdateStatus({ userId, status: '处理中...' });
      const token = localStorage.getItem('token');
      await axios.patch(
        API.USER.UPDATE_STATUS(userId),
        { is_active: isActive },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setUpdateStatus({ userId, status: '成功' });
      // 更新本地用户列表
      setUsers(users.map(user => 
        user.id === userId ? { ...user, is_active: isActive } : user
      ));
      setTimeout(() => setUpdateStatus(null), 2000);
    } catch (err: any) {
      setUpdateStatus({ userId, status: '失败' });
      setError(err.response?.data?.detail || '更新用户状态失败');
      setTimeout(() => setUpdateStatus(null), 2000);
    }
  };

  // 更新用户角色
  const updateUserRole = async (userId: string, role: string) => {
    try {
      setUpdateStatus({ userId, status: '处理中...' });
      const token = localStorage.getItem('token');
      await axios.patch(
        API.USER.UPDATE_ROLE(userId),
        { role },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      setUpdateStatus({ userId, status: '成功' });
      // 更新本地用户列表
      setUsers(users.map(user => 
        user.id === userId ? { ...user, role } : user
      ));
      setTimeout(() => setUpdateStatus(null), 2000);
    } catch (err: any) {
      setUpdateStatus({ userId, status: '失败' });
      setError(err.response?.data?.detail || '更新用户角色失败');
      setTimeout(() => setUpdateStatus(null), 2000);
    }
  };

  // 组件加载时获取用户列表
  useEffect(() => {
    fetchUsers();
  }, []);

  // 打开编辑用户信息模态框
  const openEditModal = (user: User) => {
    setEditingUser(user);
    setShowEditModal(true);
  };

  // 关闭编辑模态框
  const closeEditModal = () => {
    setEditingUser(null);
    setShowEditModal(false);
  };

  // 更新用户信息
  const updateUserInfo = async (userId: string, userInfo: { display_name: string; thumbnail?: string; role?: string }) => {
    try {
      setUpdateStatus({ userId, status: '处理中...' });
      const token = localStorage.getItem('token');
      const response = await axios.patch(
        API.USER.UPDATE_INFO(userId), // 使用新的API端点
        userInfo,
        { headers: { Authorization: `Bearer ${token}` } }
      );
      
      setUpdateStatus({ userId, status: '成功' });
      
      // 更新本地用户列表
      setUsers(users.map(user => 
        user.id === userId ? { ...user, ...userInfo } : user
      ));
      
      // 显示成功toast
      setToast({ message: '用户信息更新成功！', type: 'success' });
      
      // 关闭对话框
      closeEditModal();
      
      // 清除状态
      setTimeout(() => {
        setUpdateStatus(null);
        setToast(null);
      }, 3000);
    } catch (err: any) {
      setUpdateStatus({ userId, status: '失败' });
      const errorMessage = err.response?.data?.detail || '更新用户信息失败';
      setError(errorMessage);
      setToast({ message: errorMessage, type: 'error' });
      
      setTimeout(() => {
        setUpdateStatus(null);
        setToast(null);
      }, 3000);
    }
  };

    // 获取用户头像URL
  // 获取用户头像URL（用于显示）
  const getAvatarUrl = (user: User): string => {
  // 添加调试日志
  // console.log('getAvatarUrl called for user:', user.id, 'thumbnail:', user.thumbnail);
  
  if (user.thumbnail) {
    return user.thumbnail;
  }
  
  // 如果没有thumbnail，生成基于用户ID的固定头像
  const hash = user.id.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  const avatarId = Math.abs(hash) % 70 + 1;
  return `https://i.pravatar.cc/150?img=${avatarId}`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h2 className="text-3xl font-bold mb-4 text-gray-800 dark:text-white">用户管理</h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* 编辑用户信息侧边面板 */}
      {showEditModal && editingUser && (
        <>
          {/* 背景遮罩 */}
          <div 
            className="fixed inset-0 bg-transparent z-40 transition-opacity duration-300"
            onClick={closeEditModal}
          ></div>
          
          {/* 侧边面板 */}
          <div className="fixed inset-y-0 right-0 w-96 bg-white dark:bg-gray-800 shadow-xl z-50 transform transition-transform duration-300 ease-in-out">
            <div className="flex flex-col h-full">
              {/* 头部 */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  编辑用户信息
                </h3>
                <button
                  onClick={closeEditModal}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              {/* 内容区域 */}
              <div className="flex-1 overflow-y-auto p-6">
                {/* 用户头像预览 - 可点击更换 */}
                <div className="flex flex-col items-center mb-6">
                  <div 
                    className="w-20 h-20 rounded-full overflow-hidden mb-3 cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all"
                    onClick={() => {
                      const newAvatar = generateRandomAvatar();
                      // 只更新本地状态，不调用后台API
                      setEditingUser({...editingUser, thumbnail: newAvatar});
                      // 同时更新用户列表中的对应用户
                      setUsers(users.map(user => 
                        user.id === editingUser.id ? { ...user, thumbnail: newAvatar } : user
                      ));
                    }}
                    title="点击更换随机头像"
                  >
                    {getAvatarUrl(editingUser) ? (
                      <img 
                        src={getAvatarUrl(editingUser)} 
                        alt={editingUser.display_name || editingUser.username}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // 如果图片加载失败，显示默认的字母头像
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.nextElementSibling?.classList.remove('hidden');
                        }}
                      />
                    ) : null}
                    <div className={`w-full h-full bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center ${getAvatarUrl(editingUser) ? 'hidden' : ''}`}>
                      <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    {editingUser.email}<br/>
                    <span className="text-blue-500">点击头像更换随机图标</span>
                  </p>
                </div>
                
                <form 
                  id="edit-user-form"
                  key={editingUser.id}
                  onSubmit={(e) => {
                    e.preventDefault();
                    const formData = new FormData(e.target as HTMLFormElement);
                    const display_name = formData.get('display_name') as string;
                    const thumbnail = formData.get('thumbnail') as string;
                    const role = formData.get('role') as string;
                    
                    updateUserInfo(editingUser.id, { 
                      display_name, 
                      thumbnail: thumbnail || undefined,
                      role 
                    });
                  }} 
                  className="space-y-6"
                >
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                      显示名称
                    </label>
                    <input
                      type="text"
                      name="display_name"
                      defaultValue={editingUser.display_name || editingUser.username}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-xs"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                      头像URL（可选）
                    </label>
                    <input
                      type="url"
                      name="thumbnail"
                      value={editingUser.thumbnail || ''}
                      onChange={(e) => {
                        setEditingUser({...editingUser, thumbnail: e.target.value});
                      }}
                      placeholder="https://example.com/avatar.jpg"
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-xs"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                      用户角色
                    </label>
                    <select
                      name="role"
                      defaultValue={editingUser.role}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors text-xs"
                    >
                      <option value="user">普通用户</option>
                      <option value="admin">管理员</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                      账户状态
                    </label>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
                          <img 
                            src={getAvatarUrl(editingUser)} 
                            alt={editingUser.display_name || editingUser.username}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // 如果图片加载失败，显示默认的字母头像
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                          <div className="w-full h-full bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center hidden">
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                              {(editingUser.display_name || editingUser.username).charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                          editingUser.is_active 
                            ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' 
                            : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                        }`}>
                          {editingUser.is_active ? '已激活' : '未激活'}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          注册时间：{new Date(editingUser.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          updateUserStatus(editingUser.id, !editingUser.is_active);
                          // 更新本地状态
                          setEditingUser({...editingUser, is_active: !editingUser.is_active});
                        }}
                        className={`
                          inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ease-in-out
                          ${editingUser.is_active 
                            ? 'bg-red-50 text-red-700 border border-red-200 hover:bg-red-100 hover:border-red-300 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/30' 
                            : 'bg-green-50 text-green-700 border border-green-200 hover:bg-green-100 hover:border-green-300 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/30'
                          }
                          focus:outline-none focus:ring-2 focus:ring-offset-2 
                          ${editingUser.is_active ? 'focus:ring-red-500' : 'focus:ring-green-500'}
                          disabled:opacity-50 disabled:cursor-not-allowed
                        `}
                        disabled={updateStatus?.userId === editingUser.id && updateStatus.status === '处理中...'}
                      >
                        {editingUser.is_active ? '停用账户' : '激活账户'}
                      </button>
                    </div>
                  </div>
                </form>
              </div>
              
              {/* 底部按钮 */}
              <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
                <button
                  type="button"
                  onClick={closeEditModal}
                  className="px-4 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  form="edit-user-form"
                  className="px-4 py-2 text-xs font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                >
                  保存更改
                </button>
              </div>
            </div>
          </div>
        </>
      )}
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="flex space-x-2 justify-center">
            <div className="animate-bounce bg-blue-500 dark:bg-blue-600 p-2 w-3 h-3 rounded-full"></div>
            <div className="animate-bounce bg-blue-500 dark:bg-blue-600 p-2 w-3 h-3 rounded-full animation-delay-200"></div>
            <div className="animate-bounce bg-blue-500 dark:bg-blue-600 p-2 w-3 h-3 rounded-full animation-delay-400"></div>
          </div>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">用户</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">角色</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">状态</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">注册时间</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">最近登录</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {users.map(user => (
                <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
                        {getAvatarUrl(user) ? (
                          <img 
                            src={getAvatarUrl(user)} 
                            alt={user.display_name || user.username}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // 如果图片加载失败，显示默认的字母头像
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              target.nextElementSibling?.classList.remove('hidden');
                            }}
                          />
                        ) : null}
                        <div className={`w-full h-full bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center ${getAvatarUrl(user) ? 'hidden' : ''}`}>
                          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                            {(user.display_name || user.username).charAt(0).toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {user.display_name || user.username}
                          {user.role === "admin" && (
                            <span className="ml-1 text-blue-500" title="管理员">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                              </svg>
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      user.role === 'admin' 
                        ? 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      {user.role === 'admin' ? '管理员' : '普通用户'}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      user.is_active 
                        ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' 
                        : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                    }`}>
                      {user.is_active ? '已激活' : '未激活'}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                    {new Date(user.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                    {user.last_login ? (
                      <div>
                        <div>{new Date(user.last_login).toLocaleDateString()}</div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          {new Date(user.last_login).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400 dark:text-gray-500">从未登录</span>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end">
                      <button
                        onClick={() => openEditModal(user)}
                        className="
                          inline-flex items-center px-2 py-1 text-xs font-medium rounded-md transition-all duration-200 ease-in-out
                          bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100 hover:border-blue-300
                          dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/30
                          focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                        "
                      >
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        设置
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Toast 提示 */}
      {toast && (
        <div className={`fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 ${
          toast.type === 'success' 
            ? 'bg-green-500 text-white' 
            : 'bg-red-500 text-white'
        }`}>
          <div className="flex items-center space-x-2">
            {toast.type === 'success' ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            )}
            <span className="text-sm font-medium">{toast.message}</span>
          </div>
        </div>
      )}
      
    </div>
  
  );
}



