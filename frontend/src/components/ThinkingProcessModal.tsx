import { useState, useEffect, useRef } from "react";
import {
  FullWidthDialog as Dialog,
  FullWidthDialogContent as DialogContent,
  FullWidthDialogHeader as DialogHeader,
  FullWidthDialogTitle as DialogTitle,
  FullWidthDialogClose as DialogClose,
} from "@/components/ui/full-width-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Brain, X, Code, Eye, ZoomIn, ZoomOut, Move, Maximize, Minimize, Loader2, Sparkles } from 'lucide-react';
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import { MarkdownComponents } from "@/components/markdown/MarkdownComponents";
import { useLLMStore } from "@/store/llmStore";
import { MermaidDiagram } from '@lightenna/react-mermaid-diagram';
import { systemPrompts } from '@/lib/prompts';
import plantumlEncoder from 'plantuml-encoder';
import { ModelManager } from '@/utils/modelUtils';

// 如果已安装 react-zoom-pan-pinch，取消下面的注释
// import { TransformWrapper, TransformComponent } from "react-zoom-pan-pinch";

// 为 Mermaid 添加类型声明
declare global {
  interface Window {
    mermaid: {
      initialize: (config: {
        startOnLoad: boolean;
        theme?: string;
        securityLevel?: string;
        logLevel?: number;
        flowchart?: object;
        sequence?: object;
        gantt?: object;
      }) => void;
      render: (id: string, code: string, callback: (svg: string) => void) => void;
    };
  }
}

interface ThinkingProcessModalProps {
  thinkContent: string | null;
  mainContent: string;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}

export function ThinkingProcessModal({ thinkContent, mainContent, isOpen, setIsOpen }: ThinkingProcessModalProps) {
  // 修改第62行，添加 defaultModels 的获取
  const { providers, defaultModels, fetchProviders, selectedModel, setSelectedModel } = useLLMStore();

  // 状态管理
  const [contentType, setContentType] = useState<"think" | "main">("think");
  const [selectedPrompt, setSelectedPrompt] = useState("default");
  const [diagramType, setDiagramType] = useState<"mermaid" | "plantuml">("mermaid");
  const [diagramCode, setDiagramCode] = useState("");
  const [currentPrompt, setCurrentPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [viewMode, setViewMode] = useState<"code" | "preview">("code");
  const [plantUmlSvg, setPlantUmlSvg] = useState<string>("");

  // 缩放和平移状态
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const diagramRef = useRef<HTMLDivElement>(null);

  // 全屏状态
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 生成完成状态
  const [generationComplete, setGenerationComplete] = useState(false);
  const diagramContainerRef = useRef<HTMLDivElement>(null);

  // 在生成完成时滚动到图表区域
  useEffect(() => {
    if (generationComplete && diagramContainerRef.current) {
      diagramContainerRef.current.scrollIntoView({ behavior: 'smooth' });
      setGenerationComplete(false); // 重置状态
    }
  }, [generationComplete]);

  // 获取模型列表
  useEffect(() => {
    if (isOpen && providers.length === 0) {
      fetchProviders();
    }
  }, [isOpen, providers.length, fetchProviders]);

  // 确保 Mermaid 库正确加载
  useEffect(() => {
    if (isOpen) {
      // console.log("对话框打开，检查 Mermaid 库");

      // 检查 Mermaid 库是否已加载
      if (typeof window !== 'undefined' && window.mermaid) {
        // console.log("Mermaid 库已加载");

        // 初始化 Mermaid
        try {
          window.mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose'
          });
          // console.log("Mermaid 初始化成功");
        } catch (error) {
          console.error("Mermaid 初始化失败:", error);
        }
      } else {
        console.error("Mermaid 库未加载");
      }

      // 设置默认 Prompt
      const defaultPrompt = "请分析下面AI助手的思考过程，该思考过程是一个完整的思维链，请挖掘推理过程的具体步骤（首先，接下来，然后，最后等，这些是体现思考的顺序），确定这些主要脉络后，再提取每个步骤的内部细节和关联，将推理的细枝末节用思维导图展示出来，生成Mermaid代码，样式使用垂直布局：";
      setCurrentPrompt(defaultPrompt);
      // console.log("设置默认 Prompt:", defaultPrompt);
    }
  }, [isOpen]);

  // 当 selectedPrompt 变化时更新 currentPrompt
  useEffect(() => {
    if (selectedPrompt) {
      let prompt = "";
      switch (selectedPrompt) {
        case "flowchart":
          prompt = "请分析下面研究内容，请挖掘其中主要研究脉络，再提取每个研究点的内部细节和学术点，将技术要点用思维导图展示出来，生成Mermaid代码，样式使用垂直布局：";
          break;
        case "mindmap":
          prompt = "请将以下思考过程转换为思维导图，使用 Mermaid 语法：";
          break;
        case "sequence":
          prompt = "请将以下思考过程转换为时序图，使用 Mermaid 语法：";
          break;
        default:
          prompt = "请分析下面AI助手的思考过程，该思考过程是一个完整的思维链，请挖掘推理过程的具体步骤（首先，接下来，然后，最后等，这些是体现思考的顺序），确定这些主要脉络后，再提取每个步骤的内部细节和关联，将推理的细枝末节用思维导图展示出来，生成Mermaid代码，样式使用垂直布局：";
      }
      setCurrentPrompt(prompt);
      // console.log("Prompt 选择变化，更新 currentPrompt:", prompt);
    }
  }, [selectedPrompt]);

  // 生成图表
  const generateDiagram = async (promptType: string) => {
    // 获取要处理的内容
    const contentToProcess = contentType === "think"
      ? thinkContent || "没有思考内容"
      : mainContent || "没有主要内容";

    // console.log(`开始生成 ${diagramType} 图`, {
    //   promptType,
    //   contentType,
    //   diagramType,
    //   contentLength: contentToProcess.length
    // });

    // 设置生成状态
    setIsGenerating(true);

    // 根据不同的 prompt 类型和图表类型生成不同的提示词
    let prompt = "";

    // 如果是 PlantUML，我们将使用系统提示词，这里只是为了显示
    if (diagramType === "plantuml") {
      prompt = "使用 PlantUML 专家系统提示词，输出符合 PlantUML 语法的代码：";
      console.log("使用 PlantUML 专家系统提示词");
      setCurrentPrompt(prompt);
    } else {
      // 对于 Mermaid，我们继续使用自定义提示词
      switch (promptType) {
        case "flowchart":
          prompt = `请将以下${contentType === "think" ? "思考过程" : "内容"}转换为流程图，使用 Mermaid 语法：`;
          break;
        case "mindmap":
          prompt = `请将以下${contentType === "think" ? "思考过程" : "内容"}转换为思维导图，使用 Mermaid 语法：`;
          break;
        case "sequence":
          prompt = `请将以下${contentType === "think" ? "思考过程" : "内容"}转换为时序图，使用 Mermaid 语法：`;
          break;
        case "academic":
          prompt = `
          请基于以下结构化要求对文本内容进行体系化解析，并准备可视化框架所需的结构化数据：
          内容萃取阶段
            核心流程识别：筛选具有明确输入输出节点的功能模块，剔除描述性/说明性内容
            交互关系提取：标注流程间的三类关联（数据依赖、控制触发、信息反馈）
            层级结构判定：建立树状拓扑体系（1个根节点→3-5个主模块→多级子流程）
          逻辑建模要求
            使用分层着色体系：核心层（红色）、支撑层（蓝色）、辅助层（灰色）
            标注4种连接关系：顺序执行（→）、双向交互（↔）、条件跳转（⇄）、并行处理（⇉）
            动态行为标注：在对应节点旁添加[循环][条件][缓冲]等状态标识
          输出规范：Mermaid语法`;
          break;
        default:
          prompt = "请分析下面AI助手的思考过程，该思考过程是一个完整的思维链，请挖掘推理过程的具体步骤（首先，接下来，然后，最后等，这些是体现思考的顺序），确定这些主要脉络后，再提取每个步骤的内部细节和关联，将推理的细枝末节用思维导图展示出来，生成Mermaid代码，样式使用垂直布局：";
      }

      console.log("使用的 Mermaid Prompt:", prompt);

      // 更新当前使用的 prompt
      setCurrentPrompt(prompt);
    }

    // 检查是否选择了模型
    if (!selectedModel) {
      console.error("未选择模型");
      setIsGenerating(false);
      // 使用默认示例代码
      const defaultDiagram = getDefaultDiagramCode(promptType, diagramType);
      setDiagramCode(defaultDiagram);
      renderDiagram(defaultDiagram, diagramType);
      return;
    }

    try {
      console.log(`使用模型生成 ${diagramType} 代码:`, {
        modelId: selectedModel.id,
        baseUrl: selectedModel.baseUrl,
        providerName: selectedModel.providerName,
        diagramType
      });

      // 构建消息数组
      let messages;

      if (diagramType === "plantuml") {
        // 使用 PlantUML 专家系统提示词
        messages = [
          { role: "system", content: systemPrompts.plantuml.content },
          { role: "user", content: `${contentToProcess}\n\n请只返回 PlantUML 代码，不要包含任何其他解释或说明。` }
        ];
      } else {
        // 构建完整的 Mermaid 提示词
        const fullPrompt = `${prompt}\n\n${contentToProcess}\n\n请只返回 Mermaid 代码，不要包含任何其他解释或说明。`;
        messages = [
          { role: "user", content: fullPrompt }
        ];
      }

      // 使用 ModelManager 创建请求配置
      const requestConfig = ModelManager.createRequestConfig(messages, {
        temperature: 0.7,
        max_tokens: 1000
      });
      
      if (!requestConfig) {
        throw new Error('模型配置错误');
      }

      // 调用 API 生成代码
      console.log("发送请求体:", requestConfig.requestBody);
      const response = await fetch(requestConfig.url, {
        method: 'POST',
        headers: requestConfig.headers,
        body: JSON.stringify(requestConfig.requestBody)
      });

      if (!response.ok) {
        throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("API 返回数据:", data);

      // 提取图表代码
      let extractedCode = "";

      if (data.choices && data.choices.length > 0) {
        extractedCode = data.choices[0].message.content || "";
      } else if (data.text) {
        // 兼容旧的 API 格式
        extractedCode = data.text;
      } else {
        throw new Error("API 返回数据格式不正确");
      }

      // 清理代码，移除可能的 markdown 代码块标记和解释文本
      if (diagramType === "mermaid") {
        extractedCode = extractedCode.replace(/```mermaid\n/g, '').replace(/```/g, '').trim();
      } else {
        // 对于 PlantUML，尝试提取 @startuml 到 @enduml 之间的内容
        const plantUmlRegex = /@startuml[\s\S]*?@enduml/;
        const match = extractedCode.match(plantUmlRegex);

        if (match) {
          // 如果找到了完整的 PlantUML 代码块，直接使用它
          extractedCode = match[0].trim();
        } else {
          // 否则，尝试移除 markdown 代码块标记
          extractedCode = extractedCode.replace(/```plantuml\n/g, '').replace(/```/g, '').trim();

          // 如果代码不以 @startuml 开头，可能需要添加
          if (!extractedCode.startsWith('@startuml')) {
            extractedCode = '@startuml\n' + extractedCode;
          }

          // 如果代码不以 @enduml 结尾，可能需要添加
          if (!extractedCode.endsWith('@enduml')) {
            extractedCode = extractedCode + '\n@enduml';
          }
        }
      }

      console.log(`提取的 ${diagramType} 代码:`, extractedCode);

      // 设置图表代码
      setDiagramCode(extractedCode);

      // 渲染图表
      renderDiagram(extractedCode, diagramType);

      // 设置生成完成状态，触发滚动
      setGenerationComplete(true);
    } catch (error) {
      console.error(`生成 ${diagramType} 代码时出错:`, error);

      // 出错时使用默认示例代码
      const fallbackCode = getDefaultDiagramCode(promptType, diagramType);
      setDiagramCode(fallbackCode);
      renderDiagram(fallbackCode, diagramType);

      // 即使出错也设置生成完成状态，触发滚动
      setGenerationComplete(true);
    } finally {
      setIsGenerating(false);
    }
  };

  // 获取默认的图表代码
  const getDefaultDiagramCode = (promptType: string, type: "mermaid" | "plantuml") => {
    if (type === "mermaid") {
      switch (promptType) {
        case "flowchart":
          return `
graph TD
    A[思考开始] --> B[分析问题]
    B --> C{是否理解问题?}
    C -->|是| D[制定解决方案]
    C -->|否| E[寻求更多信息]
    E --> B
    D --> F[实施解决方案]
    F --> G[评估结果]
    G --> H{结果是否满意?}
    H -->|是| I[完成]
    H -->|否| J[调整方案]
    J --> F
`;
        case "mindmap":
          return `
mindmap
  root((思考过程))
    思考步骤
      分析问题
      理解需求
      制定方案
    解决方案
      方案A
        优点
        缺点
      方案B
        优点
        缺点
    评估
      效果评估
      用户反馈
`;
        case "sequence":
          return `
sequenceDiagram
    participant 用户
    participant 系统
    participant 数据库

    用户->>系统: 发送请求
    系统->>数据库: 查询数据
    数据库-->>系统: 返回结果
    系统-->>用户: 显示结果
`;
        default:
          return `
graph TD
    A[思考开始] --> B[分析问题]
    B --> C{是否理解问题?}
    C -->|是| D[制定解决方案]
    C -->|否| E[寻求更多信息]
    E --> B
    D --> F[实施解决方案]
    F --> G[评估结果]
    G --> H{结果是否满意?}
    H -->|是| I[完成]
    H -->|否| J[调整方案]
    J --> F
`;
      }
    } else {
      // PlantUML 默认代码
      switch (promptType) {
        case "flowchart":
          return `
@startuml
skinparam backgroundColor #EEEBDC
skinparam handwritten true

title 思考流程

start
:分析问题;
if (理解问题?) then (是)
  :制定解决方案;
else (否)
  :寻求更多信息;
  :重新分析问题;
endif
:实施解决方案;
:评估结果;
if (结果满意?) then (是)
  :完成;
else (否)
  :调整方案;
  :重新实施;
endif
stop
@enduml
`;
        case "mindmap":
          return `
@startmindmap
* 思考过程
** 思考步骤
*** 分析问题
*** 理解需求
*** 制定方案
** 解决方案
*** 方案A
**** 优点
**** 缺点
*** 方案B
**** 优点
**** 缺点
** 评估
*** 效果评估
*** 用户反馈
@endmindmap
`;
        case "sequence":
          return `
@startuml
actor 用户
participant 系统
database 数据库

用户 -> 系统: 发送请求
系统 -> 数据库: 查询数据
数据库 --> 系统: 返回结果
系统 --> 用户: 显示结果
@enduml
`;
        default:
          return `
@startuml
skinparam backgroundColor #EEEBDC
skinparam handwritten true

title 思考流程

start
:分析问题;
if (理解问题?) then (是)
  :制定解决方案;
else (否)
  :寻求更多信息;
  :重新分析问题;
endif
:实施解决方案;
:评估结果;
if (结果满意?) then (是)
  :完成;
else (否)
  :调整方案;
  :重新实施;
endif
stop
@enduml
`;
      }
    }
  };

  // 渲染图表
  const renderDiagram = async (code: string, type: "mermaid" | "plantuml") => {
    console.log(`开始渲染 ${type} 图`);

    // 切换到预览模式
    setViewMode("preview");

    // 重置缩放和平移状态
    setScale(1);
    setPosition({ x: 0, y: 0 });

    if (type === "plantuml") {
      try {
        // 对 PlantUML 代码进行编码
        const encoded = encodePlantUmlCode(code);

        // 构建 PlantUML 服务器 URL
        const plantUmlServerUrl = `https://www.plantuml.com/plantuml/svg/${encoded}`;
        console.log("PlantUML 服务器 URL:", plantUmlServerUrl);

        // 获取 SVG
        const response = await fetch(plantUmlServerUrl);
        if (!response.ok) {
          throw new Error(`PlantUML 服务器响应错误: ${response.status}`);
        }

        const svgContent = await response.text();
        setPlantUmlSvg(svgContent);

        console.log("PlantUML SVG 已更新");
      } catch (error) {
        console.error("渲染 PlantUML 图时出错:", error);
        // 创建一个包含错误信息的 SVG
        const errorMessage = error instanceof Error ? error.message : String(error);
        setPlantUmlSvg(`<svg xmlns="http://www.w3.org/2000/svg" width="500" height="200">
          <rect width="100%" height="100%" fill="#fff8f8" stroke="#ffcdd2" stroke-width="2" rx="5" ry="5"/>
          <text x="20" y="40" fill="#d32f2f" font-family="sans-serif" font-size="16" font-weight="bold">PlantUML 渲染失败</text>
          <text x="20" y="70" fill="#555" font-family="sans-serif" font-size="12">错误信息: ${errorMessage.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')}</text>
          <text x="20" y="100" fill="#555" font-family="sans-serif" font-size="12">请检查 PlantUML 代码语法是否正确</text>
          <text x="20" y="130" fill="#555" font-family="sans-serif" font-size="12">确保代码以 @startuml 开始，以 @enduml 结束</text>
        </svg>`);
      }
    }

    // 对于 Mermaid，使用 MermaidDiagram 组件自动渲染
    console.log(`${type} 代码已更新，长度:`, code.length);
  };

  // PlantUML 编码函数
  const encodePlantUmlCode = (code: string): string => {
    // 使用 plantuml-encoder 库进行编码
    return plantumlEncoder.encode(code);
  };

  // 处理缩放
  const handleZoom = (delta: number) => {
    setScale(prevScale => {
      // 放大时增加更大的步长，以便更快地放大
      const step = delta > 0 ? (prevScale >= 2 ? 0.5 : 0.2) : 0.2;
      const newScale = prevScale + (delta > 0 ? step : -step);
      // 限制缩放范围，允许更大的缩放比例
      return Math.max(0.5, Math.min(15, newScale));
    });
  };

  // 处理鼠标滚轮缩放
  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    // 增加缩放灵敏度，根据滚轮速度调整缩放步长
    const scaleFactor = 0.15; // 增加缩放步长
    const delta = e.deltaY > 0 ? -scaleFactor : scaleFactor;
    handleZoom(delta);
  };

  // 处理拖动开始
  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.button === 0) { // 只响应左键
      setIsDragging(true);
      setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
    }
  };

  // 处理拖动
  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  // 处理拖动结束
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 处理鼠标离开
  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors relative"
        title="查看思考过程"
      >
        <Brain className="h-4 w-4" />
      </button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent
          className="h-[80vh] p-0"
          onInteractOutside={(e) => e.preventDefault()}>
        <div className="flex flex-col h-full">
          {/* 标题栏 */}
          <DialogHeader className="p-3 border-b bg-gray-50 flex flex-row items-center justify-between">
            <DialogTitle className="text-lg font-bold">生成图表</DialogTitle>
            <DialogClose className="p-1 rounded-full hover:bg-gray-200">
              <X className="h-4 w-4" />
            </DialogClose>
          </DialogHeader>

          {/* 内容区域 - 左右两栏布局 */}
          <div className="flex-1 overflow-auto p-4">
            <div className={`md:grid ${isFullscreen ? 'md:grid-cols-1' : 'md:grid-cols-2'} md:items-start md:gap-8 xl:gap-16`}>
              {/* 左侧 - Mermaid 图 */}
              <div
                ref={diagramContainerRef}
                className={`rounded-xl border p-4 bg-white ${isFullscreen ? 'h-[calc(85vh-120px)]' : 'h-[600px]'} flex flex-col ${isFullscreen ? 'w-full mx-auto max-w-[1400px]' : ''}`}>
                <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "code" | "preview")} className="flex flex-col h-full">
                  <div className="flex justify-between items-center mb-4">
                    <TabsList>
                      <TabsTrigger value="code" className="flex items-center gap-1">
                        <Code className="h-4 w-4" />
                        <span>代码</span>
                      </TabsTrigger>
                      <TabsTrigger value="preview" className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        <span>预览</span>
                      </TabsTrigger>
                    </TabsList>
                  </div>

                  <TabsContent value="code" className="mt-0 flex-1 flex flex-col">
                    <div className="space-y-2 flex-1 flex flex-col">
                      <textarea
                        value={diagramCode}
                        onChange={(e) => {
                          console.log(`${diagramType} 代码已更新`);
                          setDiagramCode(e.target.value);
                        }}
                        className="w-full flex-1 p-2 font-mono text-sm border rounded-md"
                        spellCheck="false"
                        placeholder={`在这里编辑 ${diagramType === "mermaid" ? "Mermaid" : "PlantUML"} 代码...`}
                      />
                      <Button
                        onClick={() => {
                          console.log(`手动渲染 ${diagramType} 图`);
                          // 渲染图表
                          renderDiagram(diagramCode, diagramType);
                          // 切换到预览模式
                          setViewMode("preview");
                          console.log("手动渲染成功");
                        }}
                        size="sm"
                        className="w-full"
                      >
                        渲染预览
                      </Button>
                    </div>
                  </TabsContent>

                  <TabsContent value="preview" className="mt-0 flex-1">
                    <div className="w-full h-full bg-white p-4 border rounded-md relative">
                      {/* 缩放控制按钮 */}
                      <div className="absolute top-2 right-2 flex gap-1 z-10">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                          onClick={() => handleZoom(0.1)}
                          title="放大"
                        >
                          <ZoomIn className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                          onClick={() => handleZoom(-0.1)}
                          title="缩小"
                        >
                          <ZoomOut className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                          onClick={() => {
                            setScale(1);
                            setPosition({ x: 0, y: 0 });
                          }}
                          title="重置视图"
                        >
                          <Move className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                          onClick={() => setIsFullscreen(!isFullscreen)}
                          title={isFullscreen ? "退出全屏" : "全屏显示"}
                        >
                          {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
                        </Button>
                      </div>

                      {/* 可缩放和平移的容器 */}
                      <div
                        ref={diagramRef}
                        className="w-full h-full overflow-hidden"
                        onWheel={handleWheel}
                        onMouseDown={handleMouseDown}
                        onMouseMove={handleMouseMove}
                        onMouseUp={handleMouseUp}
                        onMouseLeave={handleMouseLeave}
                        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
                      >
                        <div
                          style={{
                            transform: `scale(${scale}) translate(${position.x / scale}px, ${position.y / scale}px)`,
                            transformOrigin: 'center center',
                            transition: isDragging ? 'none' : 'transform 0.1s',
                            height: '100%',
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center'
                          }}
                        >
                          {diagramType === "mermaid" ? (
                            <MermaidDiagram className="mermaid-diagram [&_text]:!font-sans [&_text]:!fill-current [&_text]:!font-normal">
                              {diagramCode}
                            </MermaidDiagram>
                          ) : (
                            <div
                              className="plantuml-diagram"
                              dangerouslySetInnerHTML={{ __html: plantUmlSvg }}
                              style={{ maxWidth: '100%', maxHeight: '100%' }}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              {/* 右侧 - 思考内容 */}
              {!isFullscreen && <div className="h-[600px] flex flex-col overflow-hidden">
                <div className="flex flex-col h-full overflow-hidden">
                  {/* 标题 */}
                  <div className="space-y-2 md:space-y-4 flex-shrink-0">
                    {/* <h2 className="font-bold text-3xl lg:text-4xl text-gray-800 dark:text-neutral-200">
                      图表生成
                    </h2> */}
                    {currentPrompt && (
                      <div className="text-sm text-gray-700 bg-gray-100 p-2 rounded">
                        <strong>Prompt:</strong> {currentPrompt}
                      </div>
                    )}
                  </div>

                  {/* 内容类型选择 */}
                  <div className="mt-4 border rounded-md p-4">
                    {/* <h3 className="font-semibold text-lg mb-2">内容选择</h3> */}
                    <RadioGroup
                      value={contentType}
                      onValueChange={(value: string) => setContentType(value as "think" | "main")}
                      className="flex flex-col space-y-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="think" id="think" disabled={!thinkContent} />
                        <Label htmlFor="think" className={!thinkContent ? "text-gray-400" : ""}>
                          思考过程 {!thinkContent && "(不可用)"}
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="main" id="main" />
                        <Label htmlFor="main">主要内容</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  {/* 内容预览 - 添加滚动条 */}
                  <div className="flex-1 overflow-auto mt-4 pr-2 border rounded-md p-4">
                    {/* <h3 className="font-semibold text-lg mb-2">内容预览</h3> */}
                    <div className="prose prose-neutral dark:prose-invert max-w-none">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm, remarkMath]}
                        rehypePlugins={[rehypeKatex]}
                        components={MarkdownComponents}
                      >
                        {contentType === "think"
                          ? thinkContent || "没有思考内容"
                          : mainContent || "没有主要内容"}
                      </ReactMarkdown>
                    </div>
                  </div>

                  {/* 工具栏 */}
                  <div className="flex flex-col gap-4 pt-4 border-t mt-4 flex-shrink-0">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-sm">模型:</span>
                        <Select
                          value={selectedModel?.id || ""}
                          onValueChange={(value) => setSelectedModel(value)}
                        >
                          <SelectTrigger className="w-[240px] h-8">
                            <SelectValue placeholder="选择模型" />
                          </SelectTrigger>
                          <SelectContent>
                            {/* 显示默认模型 */}
                            {defaultModels?.map(provider =>
                              provider.models.map(model => (
                                <SelectItem key={`default-${model.id}`} value={model.id}>
                                  <span className="text-blue-600 text-xs">[默认]</span> {provider.name || provider.id} - {model.name}
                                </SelectItem>
                              ))
                            )}
                            {/* 显示用户模型 */}
                            {providers.map(provider =>
                              provider.models.map(model => (
                                <SelectItem key={model.id} value={model.id}>
                                  {provider.name} - {model.name}
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-sm">Prompt:</span>
                        <Select
                          value={selectedPrompt}
                          disabled={diagramType === "plantuml"}
                          onValueChange={(value) => {
                            console.log("选择了新的 Prompt 类型:", value);
                            setSelectedPrompt(value);
                          }}>
                          <SelectTrigger className="w-[80px] h-8">
                            <SelectValue placeholder={diagramType === "plantuml" ? "PlantUML专家" : "选择Prompt"} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="default">默认</SelectItem>
                            <SelectItem value="flowchart">流程图</SelectItem>
                            <SelectItem value="mindmap">思维导图</SelectItem>
                            <SelectItem value="sequence">时序图</SelectItem>
                            <SelectItem value="academic">学术用图</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center gap-2">
                        <span className="text-sm">图表类型:</span>
                        <Select
                          value={diagramType}
                          onValueChange={(value) => setDiagramType(value as "mermaid" | "plantuml")}
                        >
                          <SelectTrigger className="w-[100px] h-8">
                            <SelectValue placeholder="选择图表类型" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="mermaid">Mermaid</SelectItem>
                            <SelectItem value="plantuml">PlantUML</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Button
                        onClick={() => {
                          generateDiagram(selectedPrompt);
                        }}
                        disabled={isGenerating || (contentType === "think" && !thinkContent)}
                        className="ml-auto w-[150px]"
                      >
                        {isGenerating ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            生成中...
                          </>
                        ) : (
                          <>
                            <Sparkles className="mr-2 h-4 w-4" />
                            生成{diagramType === "mermaid" ? "Mermaid" : "PlantUML"}
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>}
            </div>
          </div>
        </div>
        </DialogContent>
      </Dialog>
    </>
  );
}