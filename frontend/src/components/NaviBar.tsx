import { useLocation } from 'wouter';
import { ThemeToggle } from "./ThemeToggle";
import { Images, Bird, Lightbulb, LibraryBig, Settings, Home, LogOut, GraduationCap, Github } from "lucide-react";
import { Button } from "./ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"


import { useHistoryStore } from "@/store/historyStore";
import { HistoryDialog } from "./chat/HistoryDialog";
import { useState, useEffect } from "react";
import { User } from "lucide-react";


export function NaviBar({ children }: { children?: React.ReactNode }) {
  // 使用真实认证数据
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  // const [user, setUser] = useState<{id: string, email: string, username: string} | null>(null);
const [user, setUser] = useState<{id: string, email: string, username: string, thumbnail?: string, avatar_url?: string, display_name?: string} | null>(null);
  const [location] = useLocation();

  useEffect(() => {
    // 从 localStorage 获取用户信息
    const token = localStorage.getItem('token');
    const userJson = localStorage.getItem('user');

    if (token && userJson) {
      try {
        const userData = JSON.parse(userJson);
        setUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('解析用户数据失败:', error);
      }
    }
  }, []);

  const logout = async () => {
    try {
      // 清除本地存储的认证信息
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // 更新状态
      setIsAuthenticated(false);
      setUser(null);

      // 跳转到登录页面并刷新
      setLocation('/');
      window.location.reload();
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  const [, setLocation] = useLocation();
  const { toggleDialog } = useHistoryStore();
  
  // 判断是否在Deep Research页面
  const isDeepResearchPage = location === '/deep-search';
  
  // 根据页面决定样式
  const navBarBg = isDeepResearchPage ? 'bg-neutral-800/90' : 'bg-background/80';
  const navBarBorder = isDeepResearchPage ? 'border-neutral-700' : 'border';
  const buttonHover = isDeepResearchPage ? 'hover:bg-neutral-700' : 'hover:bg-gray-100 dark:hover:bg-gray-700';
  const textColor = isDeepResearchPage ? 'text-neutral-300 hover:text-white' : '';

  return (
    <>
      <TooltipProvider>
        {/* 顶部导航栏，用于放置新闻图标等 */}
        {children && (
          <div className="fixed top-0 right-0 p-4 z-50">
            {children}
          </div>
        )}
        
        <div className={`fixed left-8 top-1/2 -translate-y-1/2
                      ${navBarBg} backdrop-blur-sm
                      border ${navBarBorder} rounded-lg shadow-lg
                      p-2 flex flex-col gap-2 z-999`}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`p-1.5 ${buttonHover} ${textColor} transition-colors`}
                onClick={() => {
                  setLocation('/');
                  // 强制刷新页面内容
                  window.location.reload();
                }}
              >
                <Home className="h-[1.2rem] w-[1.2rem]" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">首页</TooltipContent>
          </Tooltip>

          <hr />
          {/* Deep Research */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`p-1.5 ${buttonHover} ${textColor} transition-colors`}
                onClick={() => setLocation('/academic')}
              >
                <GraduationCap className={`h-[1.2rem] w-[1.2rem] ${isDeepResearchPage ? 'text-blue-400' : 'text-blue-600'}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">学术助手</TooltipContent>
          </Tooltip>


          {/* Research History */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`p-1.5 ${buttonHover} ${textColor} transition-colors`}
                onClick={() => setLocation('/deep-search')}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className={`stroke-[2] ${isDeepResearchPage ? 'text-sky-400' : 'text-sky-500'}`}>
                  <path d="M19.2987 8.84667C15.3929 1.86808 5.44409 5.76837 7.08971 11.9099C8.01826 15.3753 12.8142 14.8641 13.2764 12.8592C13.6241 11.3504 10.2964 12.3528 10.644 10.844C11.1063 8.839 15.9022 8.32774 16.8307 11.793C18.5527 18.2196 7.86594 22.4049 4.71987 15.2225" strokeWidth="5" strokeLinecap="round" className="stroke-black/10 dark:stroke-white/20 transition-[opacity,transform] duration-200 origin-center opacity-0 scale-0"></path>
                  <path d="M2 13.8236C4.5 22.6927 18 21.3284 18 14.0536C18 9.94886 11.9426 9.0936 10.7153 11.1725C9.79198 12.737 14.208 12.6146 13.2847 14.1791C12.0574 16.2581 6 15.4029 6 11.2982C6 3.68585 20.5 2.2251 22 11.0945" stroke="currentColor" className="transition-transform duration-200 eas-out origin-center rotate-0"></path>
                </svg>
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">深度探索</TooltipContent>
          </Tooltip>

          {/* weibo */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`p-1.5 ${buttonHover} ${textColor} transition-colors`}
                onClick={() => setLocation('/weibo')}
              >
                <Bird className={`h-[1.2rem] w-[1.2rem] ${isDeepResearchPage ? 'text-green-400' : 'text-green-700'}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">博客</TooltipContent>
          </Tooltip>


          {/* 随想 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`p-1.5 ${buttonHover} ${textColor} transition-colors`}
                onClick={() => {
                  setLocation('/thoughts');
                  // 强制刷新页面内容
                  window.location.reload();
                }}
              >
                <Lightbulb className={`h-[1.2rem] w-[1.2rem] ${isDeepResearchPage ? 'text-yellow-400' : 'text-yellow-500'}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">随想</TooltipContent>
          </Tooltip>

          {/* Papers */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`p-1.5 ${buttonHover} ${textColor} transition-colors`}
                onClick={() => setLocation('/documents')}
              >
                <LibraryBig className={`h-[1.2rem] w-[1.2rem] ${isDeepResearchPage ? 'text-purple-400' : 'text-purple-500'}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">论文（Top 100）</TooltipContent>
          </Tooltip>

          {/* 图库 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`p-1.5 ${buttonHover} ${textColor} transition-colors`}
                onClick={() => setLocation('/diagrams')}
              >
                <Images className={`h-[1.2rem] w-[1.2rem] ${isDeepResearchPage ? 'text-green-400' : 'text-green-500'}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">我的图库</TooltipContent>
          </Tooltip>


          {/* Open Source */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={`p-1.5 ${buttonHover} ${textColor} transition-colors`}
                onClick={() => setLocation('/open-source')}
              >
                <Github className={`h-[1.2rem] w-[1.2rem] ${isDeepResearchPage ? 'text-neutral-300' : 'text-gray-700 dark:text-gray-300'}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="right">开源项目</TooltipContent>
          </Tooltip>


          <hr />
          <Tooltip>

            <TooltipTrigger asChild>
              <div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`p-1.5 ${buttonHover} ${textColor} transition-colors relative`}
                    >
                      {(user?.thumbnail || user?.avatar_url) ? (
                        <div className="w-[1.2rem] h-[1.2rem] rounded-full overflow-hidden">
                          <img 
                            src={user.thumbnail || user.avatar_url} 
                            alt={user.display_name || user.username || '用户头像'}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // 如果图片加载失败，显示默认User图标
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const parent = target.parentElement;
                              if (parent) {
                                parent.innerHTML = '<svg class="h-[1.2rem] w-[1.2rem]" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>';
                              }
                            }}
                          />
                        </div>
                      ) : (
                        <User className="h-[1.2rem] w-[1.2rem]" />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    side="right"
                    align="start"
                    sideOffset={16}
                    alignOffset={-8}
                    className="w-56"
                  >
                    <div className="py-3 px-4 border-b border-gray-200 dark:border-neutral-700">
                      <p className="text-sm text-gray-500 dark:text-neutral-400">登录账号</p>
                      <p className="text-sm mt-1 font-medium text-green-600 dark:text-neutral-300">
                        {user?.email || user?.username || '未登录'}
                      </p>
                    </div>


                    <DropdownMenuItem
                      className="flex items-center gap-x-3.5 px-3 py-2 cursor-pointer"
                      onClick={() => setLocation('/settings')}
                    >
                      <Settings className="h-4 w-4" />
                      <span>设置</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive flex items-center gap-x-3.5 px-3 py-2 cursor-pointer"
                      onClick={async () => {
                        try {
                          await logout();
                        } catch (error) {
                          console.error('登出失败:', error);
                        }
                      }}
                    >
                      <LogOut className="h-4 w-4" />
                      <span>登出</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </TooltipTrigger>
            {/* <TooltipContent side="right">用户信息</TooltipContent> */}
          </Tooltip>
        </div>
      </TooltipProvider>
      <HistoryDialog />
    </>
  );
}