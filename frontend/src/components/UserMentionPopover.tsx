import { useState, useEffect } from "react";
import { Search, User, Loader2 } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";

interface MentionUser {
  id: string;
  display_name: string;
  username: string;
}

interface UserMentionPopoverProps {
  onUserSelect: (user: MentionUser) => void;
  children: React.ReactNode;
}

export function UserMentionPopover({ onUserSelect, children }: UserMentionPopoverProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [users, setUsers] = useState<MentionUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchFilter, setSearchFilter] = useState("");

  // 获取用户列表
  const fetchUsers = async () => {
    if (users.length > 0) return; // 避免重复请求
    
    setIsLoading(true);
    try {
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      const response = await fetch('/api/users/mention', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        console.error('获取用户列表失败:', response.status);
      }
    } catch (error) {
      console.error('获取用户列表出错:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 当弹窗打开时获取用户列表
  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  // 过滤用户列表
  const filteredUsers = users.filter(user =>
    user.display_name.toLowerCase().includes(searchFilter.toLowerCase()) ||
    user.username.toLowerCase().includes(searchFilter.toLowerCase())
  );

  const handleUserSelect = (user: MentionUser) => {
    onUserSelect(user);
    setIsOpen(false);
    setSearchFilter(""); // 重置搜索
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        {children}
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0">
        {/* Header */}
        <div className="border-b px-4 py-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 rounded-t-lg">
          <h4 className="text-sm font-medium text-center">选择要@的用户</h4>
        </div>

        {/* Content */}
        <div className="p-4 space-y-4">
          {/* 搜索框 */}
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              value={searchFilter}
              onChange={(e) => setSearchFilter(e.target.value)}
              placeholder="搜索用户..."
              className="w-full pl-8 pr-4 py-1 text-sm border rounded-md
                       focus:outline-none focus:ring-1 focus:ring-blue-500
                       dark:bg-neutral-800 dark:border-neutral-700
                       dark:text-neutral-200 dark:placeholder-neutral-400"
            />
          </div>

          {/* 用户列表 */}
          {isLoading ? (
            <div className="flex justify-center p-4">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : (
            <div className="max-h-[250px] overflow-y-auto space-y-2 pr-2 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
              {filteredUsers.map((user) => (
                <button
                  key={user.id}
                  onClick={() => handleUserSelect(user)}
                  className="w-full text-left p-2 text-sm rounded-lg hover:bg-gray-100
                           dark:hover:bg-neutral-800 flex items-center gap-2"
                >
                  <User className="h-4 w-4 text-gray-500" />
                  <div className="flex flex-col">
                    <span className="font-medium">{user.display_name}</span>
                    <span className="text-xs text-gray-500">@{user.username}</span>
                  </div>
                </button>
              ))}
              
              {filteredUsers.length === 0 && !isLoading && (
                <div className="text-center text-gray-500 py-4">
                  {searchFilter ? "没有找到匹配的用户" : "暂无用户"}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t px-4 py-2 bg-gray-50 dark:bg-gray-800 dark:border-gray-700 rounded-b-lg">
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            选择用户后将在输入框中插入@提及
          </p>
        </div>
      </PopoverContent>
    </Popover>
  );
}