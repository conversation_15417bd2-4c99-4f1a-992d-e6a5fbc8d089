import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Brain, Send, StopCircle } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLLMStore } from '@/store/llmStore';

// Updated InputFormProps
interface InputFormProps {
  onSubmit: (inputValue: string, effort: string, model: string) => void;
  onCancel: () => void;
  isLoading: boolean;
  hasHistory: boolean;
}

export const InputForm: React.FC<InputFormProps> = ({
  onSubmit,
  onCancel,
  isLoading,
  hasHistory,
}) => {
  const [internalInputValue, setInternalInputValue] = useState("");
  const [effort, setEffort] = useState("medium");
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const lastSubmitTime = useRef(0);

  // 从 LLM Store 获取提供商和默认模型信息
  const { providers, defaultModels, fetchProviders } = useLLMStore();

  // 在组件挂载时获取模型列表
  useEffect(() => {
    fetchProviders();
  }, []);

  // 获取所有已启用的提供商的模型列表（不包括默认模型）
  const availableModels = providers
    .filter(provider => provider.enabled && !provider.isDefault)
    .flatMap(provider => provider.models.map(model => ({
      ...model,
      providerName: provider.name,
      providerColor: provider.color,
      baseUrl: provider.baseUrl
    })));

  // 获取默认模型列表
  const availableDefaultModels = defaultModels
    .filter(provider => provider.enabled)
    .flatMap(provider => provider.models.map(model => ({
      ...model,
      providerName: provider.name,
      providerColor: provider.color,
      baseUrl: provider.baseUrl,
      isDefault: true
    })));

  // 自动选择第一个可用的默认模型
  useEffect(() => {
    if (!selectedModel && availableDefaultModels.length > 0) {
      const firstDefaultModel = availableDefaultModels[0];
      setSelectedModel(firstDefaultModel.id);
    }
  }, [selectedModel, availableDefaultModels]);

  const handleInternalSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!internalInputValue.trim() || isLoading || isSubmitting) return;

    // 防止快速重复提交（1秒内只能提交一次）
    const now = Date.now();
    if (now - lastSubmitTime.current < 1000) return;
    lastSubmitTime.current = now;

    setIsSubmitting(true);
    try {
      onSubmit(internalInputValue, effort, selectedModel || "");
      setInternalInputValue("");
    } finally {
      // 添加短暂延迟防止快速重复点击
      setTimeout(() => setIsSubmitting(false), 1000);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Submit with Ctrl+Enter (Windows/Linux) or Cmd+Enter (Mac)
    if (e.key === "Enter" && (e.ctrlKey || e.metaKey) && !isLoading && !isSubmitting) {
      e.preventDefault();
      handleInternalSubmit();
    }
  };

  const isSubmitDisabled = !internalInputValue.trim() || isLoading || isSubmitting;

  return (
    <div className="flex justify-center p-4 bg-neutral-900">
      <form
        onSubmit={handleInternalSubmit}
        className={`flex flex-col gap-3 w-full max-w-4xl`}
      >
        <div
          className={`flex flex-col text-white rounded-3xl rounded-bl-sm ${hasHistory ? "rounded-br-sm" : ""
            } break-words min-h-7 bg-neutral-700 px-4 pt-3 `}
        >
          <div className="flex flex-row items-center justify-between">
            <Textarea
              value={internalInputValue}
              onChange={(e) => setInternalInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="数字孪生的本质是什么?"
              className={`w-full text-neutral-100 placeholder-neutral-500 resize-none border-0 focus:outline-none focus:ring-0 outline-none focus-visible:ring-0 shadow-none
                          md:text-base  min-h-[56px] max-h-[200px]`}
              rows={1}
            />
            <div className="-mt-3">
              {isLoading ? (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="text-red-500 hover:text-red-400 hover:bg-red-500/10 p-2 cursor-pointer rounded-full transition-all duration-200"
                  onClick={onCancel}
                >
                  <StopCircle className="h-5 w-5" />
                </Button>
              ) : (
                <Button
                  type="submit"
                  variant="ghost"
                  className={`${isSubmitDisabled
                    ? "text-neutral-500"
                    : "text-orange-200 hover:text-orange-400 hover:bg-orange-500/10"
                    } p-2 cursor-pointer rounded-full transition-all duration-200 text-base`}
                  disabled={isSubmitDisabled}
                >
                  {isSubmitting ? "Submitting..." : "Research"}
                  <Send className="h-5 w-5" />
                </Button>
              )}
            </div>
          </div>
          <div className="flex items-center justify-between pb-2">
            <div className="flex items-center gap-4 text-xs text-neutral-400">
              <div className="flex items-center gap-2">
                <Brain className="h-3 w-3" />
                <span>Effort:</span>
                <Select value={effort} onValueChange={setEffort}>
                  <SelectTrigger className="w-[70px] h-6 bg-transparent border-none text-xs p-0 cursor-pointer">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-neutral-700 text-neutral-300 cursor-pointer">
                    <SelectItem
                      value="low"
                      className="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer text-xs"
                    >
                      Low
                    </SelectItem>
                    <SelectItem
                      value="medium"
                      className="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer text-xs"
                    >
                      Medium
                    </SelectItem>
                    <SelectItem
                      value="high"
                      className="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer text-xs"
                    >
                      High
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-2">
                <span>Model:</span>
                <Select value={selectedModel || ""} onValueChange={setSelectedModel}>
                  <SelectTrigger className="w-[120px] h-6 bg-transparent border-none text-xs p-0 cursor-pointer">
                    <SelectValue placeholder="选择模型" />
                  </SelectTrigger>
                  <SelectContent className="bg-neutral-700 text-neutral-300 cursor-pointer max-h-60 overflow-y-auto">
                    {/* 系统默认模型 */}
                    {availableDefaultModels.length > 0 && (
                      <>
                        <div className="px-2 py-1.5 text-xs font-semibold text-emerald-400 bg-emerald-950/30 border-b border-emerald-800">
                          🤖 系统默认模型
                        </div>
                        {availableDefaultModels.map((model) => (
                          <SelectItem
                            key={`default-${model.id}`}
                            value={model.id}
                            className="hover:bg-emerald-950/20 focus:bg-emerald-950/20 cursor-pointer text-xs text-emerald-300"
                          >
                            {model.name}
                          </SelectItem>
                        ))}

                        {/* 分隔线 */}
                        {availableModels.length > 0 && (
                          <div className="h-px my-1 bg-neutral-600" />
                        )}
                      </>
                    )}

                    {/* 用户自定义模型 */}
                    {availableModels.length > 0 && (
                      <>
                        <div className="px-2 py-1.5 text-xs font-semibold text-blue-400 bg-blue-950/30 border-b border-blue-800">
                          👤 用户模型
                        </div>
                        {(() => {
                          // 按提供商名称对模型进行分组
                          const modelsByProvider: Record<string, typeof availableModels> = {};

                          availableModels.forEach(model => {
                            if (!modelsByProvider[model.providerName]) {
                              modelsByProvider[model.providerName] = [];
                            }
                            modelsByProvider[model.providerName].push(model);
                          });

                          // 渲染分组后的模型列表
                          return Object.entries(modelsByProvider).map(([providerName, models], index) => (
                            <div key={providerName}>
                              {/* 提供商分组标题 */}
                              <div className="px-2 py-1 text-xs font-medium text-neutral-400 bg-neutral-800/50">
                                {providerName}
                              </div>

                              {/* 该提供商下的模型列表 */}
                              {models.map((model) => (
                                <SelectItem
                                  key={model.id}
                                  value={model.id}
                                  className="hover:bg-neutral-600 focus:bg-neutral-600 cursor-pointer text-xs pl-4"
                                >
                                  {model.name}
                                </SelectItem>
                              ))}

                              {/* 提供商之间的分隔线 */}
                              {index < Object.entries(modelsByProvider).length - 1 && (
                                <div className="h-px my-1 bg-neutral-600" />
                              )}
                            </div>
                          ));
                        })()}
                      </>
                    )}

                    {/* 如果没有任何模型 */}
                    {availableDefaultModels.length === 0 && availableModels.length === 0 && (
                      <div className="px-2 py-4 text-center text-xs text-neutral-400">
                        暂无可用模型
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
            {hasHistory && (
              <Button
                className="bg-neutral-600 hover:bg-neutral-500 text-neutral-300 cursor-pointer rounded-lg h-6 px-2 text-xs"
                variant="default"
                onClick={() => window.location.reload()}
              >
                <SquarePen size={12} className="mr-1" />
                New Search
              </Button>
            )}
          </div>
        </div>

      </form>
    </div>
  );
};
