import { InputForm } from "./InputForm";

interface WelcomeScreenProps {
  handleSubmit: (
    submittedInputValue: string,
    effort: string,
    model: string
  ) => void;
  onCancel: () => void;
  isLoading: boolean;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  handleSubmit,
  onCancel,
  isLoading,
}) => (
  <div className="h-full flex flex-col bg-neutral-900">
    <div className="flex-1 flex flex-col items-center justify-center text-center px-4">
      <div>
        <h1 className="text-5xl md:text-6xl font-semibold text-neutral-100 mb-3">
          3Stooges.
        </h1>
        <p className="text-xl md:text-2xl text-neutral-400">
          Deep Research
        </p>
      </div>
      <p className="text-xs text-neutral-500 mt-8">
        Powered by 3Stooges,LangGraph.
      </p>
    </div>

    {/* 底部输入框 */}
    <div className="bg-neutral-900">
      <InputForm
        onSubmit={handleSubmit}
        isLoading={isLoading}
        onCancel={onCancel}
        hasHistory={false}
      />
    </div>
  </div>
);
