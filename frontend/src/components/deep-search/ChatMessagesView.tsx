import type React from "react";
import type { Message } from "@langchain/langgraph-sdk";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChevronDown, ChevronRight } from "lucide-react";
import { InputForm } from "@/components/deep-search/InputForm";
import { Button } from "@/components/ui/button";
import { useState, ReactNode } from "react";
import ReactMarkdown from "react-markdown";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";


// Markdown component props type from former ReportView
type MdComponentProps = {
  className?: string;
  children?: ReactNode;
  [key: string]: any;
};

// Function to extract and separate think content from message
const extractThinkContent = (content: string) => {
  const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
  const thinkMatches = [];
  let match;
  
  while ((match = thinkRegex.exec(content)) !== null) {
    thinkMatches.push(match[1].trim());
  }
  
  const finalContent = content.replace(thinkRegex, '').trim();
  
  return {
    thinkContent: thinkMatches,
    finalContent: finalContent
  };
};

// Collapsible Think Section Component
const ThinkSection: React.FC<{ thinkContent: string[]; messageId: string }> = ({ 
  thinkContent, 
  messageId 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  if (thinkContent.length === 0) return null;
  
  return (
    <div className="mb-4 border border-neutral-600 rounded-lg bg-neutral-800/50">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-3 text-left hover:bg-neutral-700/50 rounded-t-lg"
      >
        <span className="text-sm font-medium text-neutral-300">
          思考过程 ({thinkContent.length} 段)
        </span>
        {isExpanded ? (
          <ChevronDown className="h-4 w-4 text-neutral-400" />
        ) : (
          <ChevronRight className="h-4 w-4 text-neutral-400" />
        )}
      </button>
      {isExpanded && (
        <div className="border-t border-neutral-600 p-3 bg-neutral-900/30">
          {thinkContent.map((think, index) => (
            <div key={index} className="mb-3 last:mb-0">
              <div className="text-xs text-neutral-400 mb-1">思考 {index + 1}:</div>
              <div className="text-sm text-neutral-200 whitespace-pre-wrap font-mono bg-neutral-800 p-2 rounded">
                {think}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Markdown components (from former ReportView.tsx)
const mdComponents = {
  h1: ({ className, children, ...props }: MdComponentProps) => (
    <h1 className={cn("text-2xl font-bold mt-4 mb-2", className)} {...props}>
      {children}
    </h1>
  ),
  h2: ({ className, children, ...props }: MdComponentProps) => (
    <h2 className={cn("text-xl font-bold mt-3 mb-2", className)} {...props}>
      {children}
    </h2>
  ),
  h3: ({ className, children, ...props }: MdComponentProps) => (
    <h3 className={cn("text-lg font-bold mt-3 mb-1", className)} {...props}>
      {children}
    </h3>
  ),
  p: ({ className, children, ...props }: MdComponentProps) => (
    <p className={cn("mb-3 leading-7", className)} {...props}>
      {children}
    </p>
  ),
  a: ({ className, children, href, ...props }: MdComponentProps) => (
    <Badge className="text-xs mx-0.5">
      <a
        className={cn("text-blue-400 hover:text-blue-300 text-xs", className)}
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    </Badge>
  ),
  ul: ({ className, children, ...props }: MdComponentProps) => (
    <ul className={cn("list-disc pl-6 mb-3", className)} {...props}>
      {children}
    </ul>
  ),
  ol: ({ className, children, ...props }: MdComponentProps) => (
    <ol className={cn("list-decimal pl-6 mb-3", className)} {...props}>
      {children}
    </ol>
  ),
  li: ({ className, children, ...props }: MdComponentProps) => (
    <li className={cn("mb-1", className)} {...props}>
      {children}
    </li>
  ),
  blockquote: ({ className, children, ...props }: MdComponentProps) => (
    <blockquote
      className={cn(
        "border-l-4 border-neutral-600 pl-4 italic my-3 text-sm",
        className
      )}
      {...props}
    >
      {children}
    </blockquote>
  ),
  code: ({ className, children, ...props }: MdComponentProps) => (
    <code
      className={cn(
        "bg-neutral-900 rounded px-1 py-0.5 font-mono text-xs",
        className
      )}
      {...props}
    >
      {children}
    </code>
  ),
  pre: ({ className, children, ...props }: MdComponentProps) => (
    <pre
      className={cn(
        "bg-neutral-900 p-3 rounded-lg overflow-x-auto font-mono text-xs my-3",
        className
      )}
      {...props}
    >
      {children}
    </pre>
  ),
  hr: ({ className, ...props }: MdComponentProps) => (
    <hr className={cn("border-neutral-600 my-4", className)} {...props} />
  ),
  table: ({ className, children, ...props }: MdComponentProps) => (
    <div className="my-3 overflow-x-auto">
      <table className={cn("border-collapse w-full", className)} {...props}>
        {children}
      </table>
    </div>
  ),
  th: ({ className, children, ...props }: MdComponentProps) => (
    <th
      className={cn(
        "border border-neutral-600 px-3 py-2 text-left font-bold",
        className
      )}
      {...props}
    >
      {children}
    </th>
  ),
  td: ({ className, children, ...props }: MdComponentProps) => (
    <td
      className={cn("border border-neutral-600 px-3 py-2", className)}
      {...props}
    >
      {children}
    </td>
  ),
  // Enhanced math formula styling
  span: ({ className, children, ...props }: MdComponentProps) => {
    // Check if this is a KaTeX span
    if (props['data-katex'] !== undefined || className?.includes('katex')) {
      return (
        <span 
          className={cn("katex-enhanced", className)} 
          style={{ fontSize: '1.1em', lineHeight: '1.4' }}
          {...props}
        >
          {children}
        </span>
      );
    }
    return <span className={className} {...props}>{children}</span>;
  },
  div: ({ className, children, ...props }: MdComponentProps) => {
    // Check if this is a KaTeX display math div
    if (className?.includes('katex-display')) {
      return (
        <div 
          className={cn("katex-display-enhanced my-4 text-center", className)}
          style={{ fontSize: '1.2em' }}
          {...props}
        >
          {children}
        </div>
      );
    }
    return <div className={className} {...props}>{children}</div>;
  },
};

// Props for HumanMessageBubble
interface HumanMessageBubbleProps {
  message: Message;
  mdComponents: typeof mdComponents;
}

// HumanMessageBubble Component
const HumanMessageBubble: React.FC<HumanMessageBubbleProps> = ({
  message,
  mdComponents,
}) => {
  return (
    <div
      className={`text-white rounded-xl break-words min-h-7 bg-blue-600 w-full px-4 pt-3 pb-3 shadow-sm`}
    >
      <ReactMarkdown
        components={mdComponents}
        remarkPlugins={[remarkMath]}
        rehypePlugins={[rehypeKatex]}
      >
        {typeof message.content === "string"
          ? message.content
          : JSON.stringify(message.content)}
      </ReactMarkdown>
    </div>
  );
};

// Props for AiMessageBubble
interface AiMessageBubbleProps {
  message: Message;
  mdComponents: typeof mdComponents;
  handleCopy: (text: string, messageId: string) => void;
  copiedMessageId: string | null;
}

// AiMessageBubble Component
const AiMessageBubble: React.FC<AiMessageBubbleProps> = ({
  message,
  mdComponents,
  handleCopy,
  copiedMessageId,
}) => {
  // Extract think content and final content
  const messageContent = typeof message.content === "string" 
    ? message.content 
    : JSON.stringify(message.content);
  
  const { thinkContent, finalContent } = extractThinkContent(messageContent);

  return (
    <div className={`relative break-words flex flex-col w-full bg-neutral-800 text-neutral-100 rounded-xl p-4 shadow-sm`}>
      {/* Think Section - Collapsible */}
      <ThinkSection thinkContent={thinkContent} messageId={message.id!} />
      
      {/* Final Content */}
      <div className="mb-3">
        <ReactMarkdown
          components={mdComponents}
          remarkPlugins={[remarkMath]}
          rehypePlugins={[rehypeKatex]}
        >
          {finalContent}
        </ReactMarkdown>
      </div>
      
      {/* Copy Button */}
      <div className="flex justify-end gap-2 mt-2">
        <Button
          variant="outline"
          size="sm"
          className="bg-neutral-700 border-neutral-600 text-neutral-300 hover:bg-neutral-600 hover:text-white"
          onClick={() => handleCopy(finalContent, message.id!)}
        >
          {copiedMessageId === message.id ? (
            <>
              <CopyCheck className="h-3 w-3 mr-1" />
              {/* 已复制 */}
            </>
          ) : (
            <>
              <Copy className="h-3 w-3 mr-1" />
              {/* 复制 */}
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

interface ChatMessagesViewProps {
  messages: Message[];
  isLoading: boolean;
  scrollAreaRef: React.RefObject<HTMLDivElement | null>;
  onSubmit: (inputValue: string, effort: string, model: string) => void;
  onCancel: () => void;
}

export function ChatMessagesView({
  messages,
  isLoading,
  scrollAreaRef,
  onSubmit,
  onCancel,
}: ChatMessagesViewProps) {
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);

  const handleCopy = async (text: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };
  return (
    <div className="flex flex-col h-full bg-neutral-900">
      {/* 消息滚动区域 */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full" ref={scrollAreaRef}>
          <div className="flex justify-center p-4 md:p-6 pb-4">
            <div className="w-full max-w-4xl space-y-4">
              {messages.map((message, index) => (
                <div key={message.id || `msg-${index}`}>
                  {message.type === "human" ? (
                    <HumanMessageBubble
                      message={message}
                      mdComponents={mdComponents}
                    />
                  ) : (
                    <AiMessageBubble
                      message={message}
                      mdComponents={mdComponents}
                      handleCopy={handleCopy}
                      copiedMessageId={copiedMessageId}
                    />
                  )}
                </div>
              ))}
              {isLoading &&
                (messages.length === 0 ||
                  messages[messages.length - 1].type === "human") && (
                  <div className="relative rounded-xl p-4 shadow-sm break-words bg-neutral-800 text-neutral-100">
                    <div className="flex items-center justify-start h-full">
                      <Loader2 className="h-5 w-5 animate-spin text-neutral-400 mr-2" />
                      <span>Processing...</span>
                    </div>
                  </div>
                )}
            </div>
          </div>
        </ScrollArea>
      </div>
      
      {/* 固定在底部的输入框 */}
      <div className="flex-shrink-0 bg-neutral-900 border-t border-neutral-800">
        <InputForm
          onSubmit={onSubmit}
          isLoading={isLoading}
          onCancel={onCancel}
          hasHistory={messages.length > 0}
        />
      </div>
    </div>
  );
}
