
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Loader2,
  Activity,
  Info,
  Search,
  Brain,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { useEffect, useState } from "react";

export interface ProcessedEvent {
  title: string;
  data: any;
  nodeType: 'main' | 'researcher' | 'supervisor';  // 区分不同类型的节点
  status: 'start' | 'complete' | 'error' | 'running';         // 节点状态
  timestamp: number;                              // 用于排序和显示时间线
  subgraphType?: 'supervisor' | 'researcher';     // 表明节点属于哪个子图
  parentNode?: string;                           // 父节点名称
  depth: number;                                 // 节点层级深度（主图=0，子图=1，etc）
}

interface ActivityTimelineProps {
  processedEvents: ProcessedEvent[];
  isLoading: boolean;
}

export function ActivityTimeline({
  processedEvents,
  isLoading,
}: ActivityTimelineProps) {
  const [isTimelineCollapsed, setIsTimelineCollapsed] =
    useState<boolean>(false);
  const getEventIcon = (event: ProcessedEvent, index: number) => {
    // 如果正在加载且没有事件
    if (index === 0 && isLoading && processedEvents.length === 0) {
      return <Loader2 className="h-4 w-4 text-neutral-400 animate-spin" />;
    }

    // 根据节点类型返回不同的图标
    if (event.status === 'error') {
      return <Info className="h-4 w-4 text-red-400" />;
    }

    switch (event.nodeType) {
      case 'main':
        return <Activity className={`h-4 w-4 ${
          event.status === 'complete' ? 'text-green-400' : 
          event.status === 'running' ? 'text-blue-400 animate-pulse' : 
          'text-blue-400'
        }`} />;
      case 'researcher':
        return <Search className={`h-4 w-4 ${
          event.status === 'complete' ? 'text-green-400' : 
          event.status === 'running' ? 'text-yellow-400 animate-pulse' : 
          'text-yellow-400'
        }`} />;
      case 'supervisor':
        return <Brain className={`h-4 w-4 ${
          event.status === 'complete' ? 'text-green-400' : 
          event.status === 'running' ? 'text-purple-400 animate-pulse' : 
          'text-purple-400'
        }`} />;
      default:
        return <Activity className="h-4 w-4 text-neutral-400" />;
    }

  };

  // 移除自动折叠逻辑，让用户手动控制
  // useEffect(() => {
  //   if (!isLoading && processedEvents.length !== 0) {
  //     setIsTimelineCollapsed(true);
  //   }
  // }, [isLoading, processedEvents]);

  return (
    <div className="h-full flex flex-col bg-neutral-700 rounded-lg border border-neutral-600 m-4">
      {/* 固定的标题栏 */}
      <div className="flex-shrink-0 p-4 border-b border-neutral-600">
        <div
          className="flex items-center justify-start text-sm w-full cursor-pointer gap-2 text-neutral-100"
          onClick={() => setIsTimelineCollapsed(!isTimelineCollapsed)}
        >
          <span className="font-medium">Research Timeline</span>
          {isTimelineCollapsed ? (
            <ChevronDown className="h-4 w-4 ml-auto" />
          ) : (
            <ChevronUp className="h-4 w-4 ml-auto" />
          )}
        </div>
      </div>
      
      {/* 可滚动的内容区域 */}
      {!isTimelineCollapsed && (
        <div className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-4">
              {isLoading && processedEvents.length === 0 && (
                <div className="relative pl-8 pb-4">
                  <div className="absolute left-3 top-3.5 h-full w-0.5 bg-neutral-800" />
                  <div className="absolute left-0.5 top-2 h-5 w-5 rounded-full bg-neutral-800 flex items-center justify-center ring-4 ring-neutral-900">
                    <Loader2 className="h-3 w-3 text-neutral-400 animate-spin" />
                  </div>
                  <div>
                    <p className="text-sm text-neutral-300 font-medium">
                      Searching...
                    </p>
                  </div>
                </div>
              )}
              {processedEvents.length > 0 ? (
                <div className="space-y-0">
                  {processedEvents.map((eventItem, index) => (
                    <div 
                      key={index} 
                      className={`relative pb-4 ${
                        eventItem.depth > 0 ? 'ml-8 border-l-2 border-dotted border-neutral-700' : ''
                      }`}
                      style={{
                        paddingLeft: eventItem.depth === 0 ? '2rem' : '3rem'
                      }}
                    >
                      {index < processedEvents.length - 1 ||
                      (isLoading && index === processedEvents.length - 1) ? (
                        <div className={`absolute left-3 top-3.5 h-full w-0.5 ${
                          eventItem.depth === 0 ? 'bg-neutral-600' : 'bg-neutral-700'
                        }`} />
                      ) : null}
                      <div className={`absolute left-0.5 top-2 h-6 w-6 rounded-full 
                        ${eventItem.depth === 0 ? 'bg-neutral-600' : 'bg-neutral-700'} 
                        flex items-center justify-center ring-4 
                        ${eventItem.depth === 0 ? 'ring-neutral-700' : 'ring-neutral-800'}`}
                        style={{
                          left: eventItem.depth === 0 ? '0.125rem' : '1.125rem'
                        }}
                      >
                        {getEventIcon(eventItem, index)}
                      </div>
                      <div>
                        <p className={`text-sm font-medium mb-0.5 ${
                          eventItem.depth === 0 ? 'text-neutral-200' : 'text-neutral-300'
                        }`}>
                          {eventItem.title}
                          {eventItem.subgraphType && (
                            <span className="ml-2 text-xs text-neutral-400">
                              ({eventItem.subgraphType} 子图)
                            </span>
                          )}
                        </p>
                        <p className="text-xs text-neutral-300 leading-relaxed">
                          {typeof eventItem.data === "string"
                            ? eventItem.data
                            : Array.isArray(eventItem.data)
                            ? (eventItem.data as string[]).join(", ")
                            : JSON.stringify(eventItem.data)}
                        </p>
                      </div>
                    </div>
                  ))}
                  {isLoading && processedEvents.length > 0 && (
                    <div className="relative pl-8 pb-4">
                      <div className="absolute left-0.5 top-2 h-5 w-5 rounded-full bg-neutral-600 flex items-center justify-center ring-4 ring-neutral-700">
                        <Loader2 className="h-3 w-3 text-neutral-400 animate-spin" />
                      </div>
                      <div>
                        <p className="text-sm text-neutral-300 font-medium">
                          研究中 (可能要花5分钟之久) ...
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              ) : !isLoading ? ( // Only show "No activity" if not loading and no events
                <div className="flex flex-col items-center justify-center h-full text-neutral-500 pt-10">
                  <Info className="h-6 w-6 mb-3" />
                  <p className="text-sm">No activity to display.</p>
                  <p className="text-xs text-neutral-600 mt-1">
                    Timeline will update during processing.
                  </p>
                </div>
              ) : null}
            </div>
          </ScrollArea>
        </div>
      )}
    </div>
  );
}
