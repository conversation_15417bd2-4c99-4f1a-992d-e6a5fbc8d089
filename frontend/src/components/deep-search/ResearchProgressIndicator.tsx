import { useState, useEffect } from "react";
import { Progress } from "@/components/ui/progress";
import { Brain, Loader2 } from "lucide-react";

interface ResearchProgressIndicatorProps {
  isVisible: boolean;
  currentStage?: string;
}

export function ResearchProgressIndicator({
  isVisible,
  currentStage = 'analysis'
}: ResearchProgressIndicatorProps) {
  const [progress, setProgress] = useState(0);
  const [statusText, setStatusText] = useState("正在分析研究需求...");

  // 根据阶段更新状态文本
  useEffect(() => {
    switch (currentStage) {
      case 'analysis':
        setStatusText("正在分析研究需求...");
        break;
      case 'search':
        setStatusText("正在搜索相关资料...");
        break;
      case 'processing':
        setStatusText("正在处理和分析信息...");
        break;
      case 'synthesis':
        setStatusText("正在生成研究报告...");
        break;
      default:
        setStatusText("深度研究进行中...");
    }
  }, [currentStage]);

  // 模拟进度更新
  useEffect(() => {
    if (!isVisible) {
      setProgress(0);
      return;
    }

    const interval = setInterval(() => {
      setProgress(prev => {
        // 根据阶段设置不同的进度范围
        let maxProgress = 90;
        switch (currentStage) {
          case 'analysis':
            maxProgress = 25;
            break;
          case 'search':
            maxProgress = 60;
            break;
          case 'processing':
            maxProgress = 85;
            break;
          case 'synthesis':
            maxProgress = 95;
            break;
        }

        return Math.min(prev + Math.random() * 2, maxProgress);
      });
    }, 1500);

    return () => clearInterval(interval);
  }, [isVisible, currentStage]);

  if (!isVisible) return null;

  return (
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40 bg-neutral-900/95 backdrop-blur-sm border border-neutral-700 rounded-lg shadow-lg p-4 min-w-80">
      <div className="flex items-center space-x-3">
        <div className="relative">
          <Brain className="h-5 w-5 text-blue-400" />
          <Loader2 className="h-3 w-3 text-blue-400 animate-spin absolute -top-1 -right-1" />
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-white">{statusText}</span>
            <span className="text-xs text-blue-400">{Math.round(progress)}%</span>
          </div>
          <Progress
            value={progress}
            className="h-1.5 bg-neutral-800"
          />
        </div>
      </div>
    </div>
  );
}
