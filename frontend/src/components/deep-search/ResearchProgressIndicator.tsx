import { useState, useEffect } from "react";
import { Progress } from "@/components/ui/progress";
import { Brain, Loader2 } from "lucide-react";

interface ResearchProgressIndicatorProps {
  isVisible: boolean;
  currentStage?: string;
  fastForwardProgress?: boolean;
}

export function ResearchProgressIndicator({
  isVisible,
  currentStage = 'analysis',
  fastForwardProgress = false
}: ResearchProgressIndicatorProps) {
  const [progress, setProgress] = useState(0);
  const [statusText, setStatusText] = useState("正在分析研究需求...");

  // 根据阶段更新状态文本
  useEffect(() => {
    switch (currentStage) {
      case 'analysis':
        setStatusText("正在分析研究需求...");
        break;
      case 'search':
        setStatusText("正在搜索相关资料...");
        break;
      case 'processing':
        setStatusText("正在处理和分析信息...");
        break;
      case 'synthesis':
        setStatusText("正在生成研究报告...");
        break;
      case 'supervisor_active':
        setStatusText("研究监督系统正在执行...");
        break;
      case 'arxiv_search':
        setStatusText("正在执行arXiv学术搜索...");
        break;
      case 'research_complete':
        setStatusText("研究任务即将完成...");
        break;
      default:
        setStatusText("深度研究进行中...");
    }
  }, [currentStage]);

  // 快进进度条到95%
  useEffect(() => {
    if (fastForwardProgress) {
      setProgress(95);
    }
  }, [fastForwardProgress]);

  // 重置进度
  useEffect(() => {
    if (!isVisible) {
      setProgress(0);
      return;
    }
  }, [isVisible]);

  // 模拟进度更新 - 改善预测逻辑
  useEffect(() => {
    if (!isVisible || fastForwardProgress) {
      return;
    }

    const interval = setInterval(() => {
      setProgress(prev => {
        // 根据阶段设置不同的进度范围和增长速度
        let maxProgress = 90;
        let increment = Math.random() * 2;

        switch (currentStage) {
          case 'analysis':
            // 分析阶段：持续推进到40%，速度适中
            maxProgress = 40;
            increment = Math.random() * 1.5 + 0.5; // 0.5-2.0
            break;
          case 'search':
            maxProgress = 60;
            increment = Math.random() * 1.2 + 0.3; // 0.3-1.5
            break;
          case 'processing':
            maxProgress = 75;
            increment = Math.random() * 1.0 + 0.2; // 0.2-1.2
            break;
          case 'supervisor_active':
            maxProgress = 80;
            increment = Math.random() * 0.8 + 0.2; // 0.2-1.0
            break;
          case 'arxiv_search':
            maxProgress = 85;
            increment = Math.random() * 0.5 + 0.1; // 0.1-0.6
            break;
          case 'research_complete':
            maxProgress = 90;
            increment = Math.random() * 0.3 + 0.1; // 0.1-0.4
            break;
          case 'synthesis':
            maxProgress = 95;
            increment = Math.random() * 0.2 + 0.1; // 0.1-0.3
            break;
        }

        return Math.min(prev + increment, maxProgress);
      });
    }, 1200); // 稍微加快更新频率

    return () => clearInterval(interval);
  }, [isVisible, currentStage, fastForwardProgress]);

  if (!isVisible) return null;

  return (
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40 bg-neutral-900/95 backdrop-blur-sm border border-neutral-700 rounded-lg shadow-lg p-4 min-w-80">
      <div className="flex items-center space-x-3">
        <div className="relative">
          <Brain className="h-5 w-5 text-blue-400" />
          <Loader2 className="h-3 w-3 text-blue-400 animate-spin absolute -top-1 -right-1" />
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-white">{statusText}</span>
            <span className="text-xs text-blue-400">{Math.round(progress)}%</span>
          </div>
          <Progress
            value={progress}
            className="h-1.5 bg-neutral-800"
          />
        </div>
      </div>
    </div>
  );
}
