import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { 
  Brain, 
  Search, 
  FileText, 
  Lightbulb, 
  Clock,
  Loader2,
  CheckCircle,
  ArrowRight
} from "lucide-react";

interface ResearchStage {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  estimatedTime: string;
  status: 'pending' | 'active' | 'completed';
}

interface ResearchProgressIndicatorProps {
  isVisible: boolean;
  currentStage?: string;
  onClose?: () => void;
}

export function ResearchProgressIndicator({ 
  isVisible, 
  currentStage = 'analysis',
  onClose 
}: ResearchProgressIndicatorProps) {
  const [progress, setProgress] = useState(0);
  const [currentStageIndex, setCurrentStageIndex] = useState(0);
  const [animationPhase, setAnimationPhase] = useState(0);

  const stages: ResearchStage[] = [
    {
      id: 'analysis',
      title: '需求分析',
      description: '分析研究问题，制定研究策略',
      icon: <Brain className="h-5 w-5" />,
      estimatedTime: '30-60秒',
      status: 'pending'
    },
    {
      id: 'search',
      title: '信息搜索',
      description: '搜索相关文献和资料',
      icon: <Search className="h-5 w-5" />,
      estimatedTime: '1-2分钟',
      status: 'pending'
    },
    {
      id: 'processing',
      title: '内容处理',
      description: '分析和整理收集的信息',
      icon: <FileText className="h-5 w-5" />,
      estimatedTime: '2-3分钟',
      status: 'pending'
    },
    {
      id: 'synthesis',
      title: '结果综合',
      description: '整合分析结果，生成报告',
      icon: <Lightbulb className="h-5 w-5" />,
      estimatedTime: '1-2分钟',
      status: 'pending'
    }
  ];

  // 模拟进度更新
  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = Math.min(prev + Math.random() * 3, 95);
        
        // 根据进度更新当前阶段
        const stageIndex = Math.floor((newProgress / 100) * stages.length);
        setCurrentStageIndex(Math.min(stageIndex, stages.length - 1));
        
        return newProgress;
      });
    }, 2000);

    return () => clearInterval(interval);
  }, [isVisible, stages.length]);

  // 动画效果
  useEffect(() => {
    if (!isVisible) return;

    const animationInterval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 3);
    }, 1000);

    return () => clearInterval(animationInterval);
  }, [isVisible]);

  if (!isVisible) return null;

  const getStageStatus = (index: number): 'pending' | 'active' | 'completed' => {
    if (index < currentStageIndex) return 'completed';
    if (index === currentStageIndex) return 'active';
    return 'pending';
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-neutral-900 border-neutral-700 shadow-2xl">
        <CardContent className="p-8">
          {/* 标题区域 */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="relative">
                <Brain className="h-12 w-12 text-blue-400" />
                <div className="absolute -top-1 -right-1">
                  <Loader2 className="h-4 w-4 text-blue-400 animate-spin" />
                </div>
              </div>
            </div>
            <h2 className="text-2xl font-bold text-white mb-2">
              深度研究进行中
            </h2>
            <p className="text-neutral-400">
              AI 研究助手正在执行多轮迭代分析，请耐心等待...
            </p>
          </div>

          {/* 进度条 */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-neutral-400">整体进度</span>
              <span className="text-sm text-blue-400 font-medium">
                {Math.round(progress)}%
              </span>
            </div>
            <Progress 
              value={progress} 
              className="h-2 bg-neutral-800"
            />
          </div>

          {/* 阶段列表 */}
          <div className="space-y-4 mb-8">
            {stages.map((stage, index) => {
              const status = getStageStatus(index);
              return (
                <div 
                  key={stage.id}
                  className={`flex items-center p-4 rounded-lg border transition-all duration-500 ${
                    status === 'active' 
                      ? 'bg-blue-500/10 border-blue-500/30 shadow-lg' 
                      : status === 'completed'
                      ? 'bg-green-500/10 border-green-500/30'
                      : 'bg-neutral-800/50 border-neutral-700'
                  }`}
                >
                  <div className={`flex-shrink-0 mr-4 ${
                    status === 'active' ? 'animate-pulse' : ''
                  }`}>
                    {status === 'completed' ? (
                      <CheckCircle className="h-5 w-5 text-green-400" />
                    ) : status === 'active' ? (
                      <div className="relative">
                        {stage.icon}
                        <div className="absolute -top-1 -right-1">
                          <div className={`w-2 h-2 rounded-full bg-blue-400 ${
                            animationPhase === index % 3 ? 'animate-ping' : ''
                          }`} />
                        </div>
                      </div>
                    ) : (
                      <div className="text-neutral-500">
                        {stage.icon}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className={`font-medium ${
                        status === 'active' ? 'text-blue-400' : 
                        status === 'completed' ? 'text-green-400' : 'text-neutral-400'
                      }`}>
                        {stage.title}
                      </h3>
                      {status === 'active' && (
                        <div className="flex items-center text-xs text-neutral-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {stage.estimatedTime}
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-neutral-500 mt-1">
                      {stage.description}
                    </p>
                  </div>

                  {status === 'active' && (
                    <ArrowRight className="h-4 w-4 text-blue-400 ml-2 animate-pulse" />
                  )}
                </div>
              );
            })}
          </div>

          {/* 提示信息 */}
          <div className="bg-neutral-800/50 rounded-lg p-4 border border-neutral-700">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
              </div>
              <div className="text-sm text-neutral-400">
                <p className="mb-2">
                  <strong className="text-white">当前状态：</strong>
                  {stages[currentStageIndex]?.title} - {stages[currentStageIndex]?.description}
                </p>
                <p>
                  研究过程可能需要 3-8 分钟，具体时间取决于问题复杂度和资料丰富程度。
                  您可以在右侧时间线中查看详细的执行进度。
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
