import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Search, User } from "lucide-react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    TooltipProvider,
} from "@/components/ui/tooltip";
import { ThemeToggle } from "./ThemeToggle";
import { Logo } from "./Logo";
import { NotificationBell } from "./weibo/NotificationBell";

export function RedditHeader() {
    const [, setLocation] = useLocation();
    const [searchQuery, setSearchQuery] = useState("");
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [user, setUser] = useState<{
        id: string;
        email: string;
        username: string;
        thumbnail?: string;
        avatar_url?: string;
        display_name?: string;
    } | null>(null);

    // 创建一个自定义事件，用于在搜索时通知其他组件
    const searchEvent = new CustomEvent('postSearch', {
        detail: { query: '' }
    });

    useEffect(() => {
        const token = localStorage.getItem('token');
        const userJson = localStorage.getItem('user');

        if (token && userJson) {
            try {
                const userData = JSON.parse(userJson);
                setUser(userData);
                setIsAuthenticated(true);
            } catch (error) {
                console.error('解析用户数据失败:', error);
            }
        }
    }, []);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            // 创建一个自定义事件，用于在当前页面过滤帖子
            const searchEvent = new CustomEvent('postSearch', {
                detail: { query: searchQuery.trim() }
            });
            // 触发事件
            window.dispatchEvent(searchEvent);

            // 如果当前不在微博页面，则跳转到微博页面并带上搜索参数
            if (!window.location.pathname.includes('/weibo')) {
                setLocation(`/weibo?search=${encodeURIComponent(searchQuery.trim())}`);
            }
        }
    };

    const logout = async () => {
        try {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            setIsAuthenticated(false);
            setUser(null);
            setLocation('/');
            window.location.reload();
        } catch (error) {
            console.error('登出失败:', error);
        }
    };

    return (
        <TooltipProvider>
            <header className="w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 flex-shrink-0">
                <div className="w-full flex justify-between items-center h-14 px-4">
                    {/* 左侧：Logo */}
                    <div className="flex items-center justify-start w-64 flex-shrink-0">
                        <div className="flex items-center space-x-2 cursor-pointer h-12 my-1" onClick={() => setLocation('/')}>
                            <Logo className="h-full w-auto" />
                            {/* <span className="hidden font-bold sm:inline-block text-xl whitespace-nowrap">学术微博</span> */}
                        </div>
                    </div>
                    {/* 中央：搜索栏 */}
                    <div className="flex-1 flex justify-center px-4">
                        <form onSubmit={handleSearch} className="relative w-full max-w-md">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                                type="search"
                                placeholder="搜索学术微博..."
                                className="pl-10 pr-4 h-9 bg-muted/50 border-0 focus-visible:ring-1 w-full"
                                value={searchQuery}
                                onChange={(e) => {
                                    const newValue = e.target.value;
                                    setSearchQuery(newValue);

                                    // 如果清空了搜索框，触发清除搜索事件
                                    if (!newValue.trim() && searchQuery.trim()) {
                                        const clearEvent = new CustomEvent('postSearch', {
                                            detail: { query: '' }
                                        });
                                        window.dispatchEvent(clearEvent);
                                    }
                                }}
                            />
                        </form>
                    </div>
                    {/* 右侧：操作区 */}
                    <div className="flex items-center justify-end space-x-2 w-64 flex-shrink-0">
                        {isAuthenticated ? (
                            <>
                                {/* 通知铃铛 */}
                                <NotificationBell />

                                {/* 用户菜单 */}
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" className="h-9 px-2 space-x-2">
                                            {(user?.thumbnail || user?.avatar_url) ? (
                                                <div className="w-6 h-6 rounded-full overflow-hidden">
                                                    <img
                                                        src={user.thumbnail || user.avatar_url}
                                                        alt={user.display_name || user.username || '用户头像'}
                                                        className="w-full h-full object-cover"
                                                        onError={(e) => {
                                                            const target = e.target as HTMLImageElement;
                                                            target.style.display = 'none';
                                                            const parent = target.parentElement;
                                                            if (parent) {
                                                                parent.innerHTML = '<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>';
                                                            }
                                                        }}
                                                    />
                                                </div>
                                            ) : (
                                                <User className="h-6 w-6" />
                                            )}
                                            <span className="hidden sm:inline text-sm font-medium">
                                                {user?.display_name || user?.username || '用户'}
                                            </span>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end" className="w-56">
                                        <div className="px-3 py-2">
                                            <p className="text-sm text-muted-foreground">登录为</p>
                                            <p className="text-sm font-medium">
                                                {user?.email || user?.username}
                                            </p>
                                        </div>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem onClick={() => setLocation('/profile')}>
                                            我的资料
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => setLocation('/notifications')}>
                                            通知中心
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => setLocation('/settings')}>
                                            设置
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem onClick={logout} className="text-red-600">
                                            登出
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </>
                        ) : (
                            /* 未登录状态 */
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setLocation('/login')}
                                >
                                    登录
                                </Button>
                                <Button
                                    size="sm"
                                    onClick={() => setLocation('/register')}
                                >
                                    注册
                                </Button>
                            </div>
                        )}

                        {/* 主题切换 */}
                        <ThemeToggle />

                    </div>
                </div>
            </header>
        </TooltipProvider>
    );
}