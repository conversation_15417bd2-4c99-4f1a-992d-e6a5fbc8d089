import { useEffect } from 'react';
import { useLocation } from 'wouter';
import { Logo } from '@/components/Logo';
import { Footer } from '@/components/Footer';
import { NaviBar } from "@/components/NaviBar";
import { useSloganStore } from '@/store/sloganStore';
import { useSearchEngineStore } from '@/store/searchEngineStore';
import { useHistoryStore } from '@/store/historyStore';
import { ToolBox } from '@/components/ToolBox';
import { ChatInput } from '@/components/ChatInput';
import { useLLMStore } from '@/store/llmStore';
import { ResizablePanelGroup, ResizablePanel } from '@/components/ui/resizable';
// RSS functionality disabled
// import { NewsPanel } from '@/components/news/NewsPanel';
// import { Rss } from 'lucide-react';
// import { useNewsPanelStore } from '@/store/newsPanelStore';
// import { Button } from '@/components/ui/button';
// import { Tooltip } from '@/components/ui/tooltip';
// import { TooltipContent } from '@radix-ui/react-tooltip';
// import { TooltipTrigger } from '@radix-ui/react-tooltip';

export function Home() {
  const [, setLocation] = useLocation();
  const { slogan, fetchSlogan } = useSloganStore();
  const { fetchEngines } = useSearchEngineStore();
  const { startNewConversation } = useHistoryStore();
  const { selectedModel, fetchProviders } = useLLMStore();
  // RSS functionality disabled

  useEffect(() => {
    fetchEngines().then(() => {
      // console.log('可用的搜索引擎:', useSearchEngineStore.getState().engines);
    });
    fetchSlogan();
    fetchProviders(); // 加载模型信息
  }, [fetchSlogan, fetchProviders]);

  // RSS functionality disabled
  // useEffect(() => {
  //   setRightPanelSize(0);
  // }, [isOpen]);


  return (
    <div className="min-h-screen flex flex-col bg-background">
      <NaviBar>
        {/* RSS button functionality disabled as requested
        <div className="flex items-center ml-auto">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggle}
                className={`rounded-full hover:bg-orange-800 hover:text-white ${isOpen ? 'text-primary' : ''}`}
              >
                <Rss className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>新动向</TooltipContent>
          </Tooltip>
        </div>
        */}
      </NaviBar>

      <div className="flex-1 flex">
        <ResizablePanelGroup direction="horizontal" className="w-full">
          {/* 主内容区域 */}
          <ResizablePanel defaultSize={100} minSize={50}>
            <div className="min-h-screen flex flex-col items-center justify-center bg-background">
              <div className="w-full max-w-3xl px-4 animate-fade-in">
                <div className="flex flex-col items-center mb-8">
                  <Logo className="h-24 w-auto mb-4" />
                  <p className="text-lg text-gray-400 dark:text-gray-400 mt-2 text-center">
                    {slogan}
                  </p>
                </div>

                <ChatInput
                  onSubmit={(message, modelInfo) => {
                    const trimmedMessage = message.trim();
                    if (trimmedMessage) {
                      const conversationId = startNewConversation();
                      if (conversationId) {
                        const state = useHistoryStore.getState();
                        state.addMessage(conversationId, {
                          type: 'user',
                          content: trimmedMessage
                        });
                      }

                      // 构建URL参数，包括模型信息
                      let url = `/academic?prompt=${encodeURIComponent(trimmedMessage)}&initial=true`;

                      // 如果有选择的模型，添加模型信息
                      if (modelInfo) {
                        url += `&model=${encodeURIComponent(modelInfo.model)}&baseUrl=${encodeURIComponent(modelInfo.baseUrl)}`;
                      } else if (selectedModel) {
                        // 如果没有直接选择模型，但有全局选择的模型，使用全局选择的模型
                        url += `&model=${encodeURIComponent(selectedModel.id)}&baseUrl=${encodeURIComponent(selectedModel.baseUrl)}`;
                      }

                      setLocation(url);
                    }
                  }}
                  placeholder="来探索未知...（Ctrl+Enter）"
                  disabled={false}
                  hideSearchToggle={true}  // 在首页显式设置为 true
                />

                <Footer />
              </div>
              <ToolBox />
            </div>
          </ResizablePanel>

          {/* RSS functionality disabled
          {isOpen && (
            <ResizableHandle withHandle />
          )}

          <ResizablePanel
            defaultSize={rightPanelSize}
            minSize={0}
            maxSize={40}
            className={`${rightPanelSize === 0 ? 'hidden' : ''}`}
          >
            <NewsPanel onClose={close} />
          </ResizablePanel>
          */}
        </ResizablePanelGroup>
      </div>
    </div>
  );
}
