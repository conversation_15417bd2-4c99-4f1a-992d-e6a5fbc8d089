import { useEffect, useState } from 'react';
import { useLocation, useSearch } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useHistoryStore } from '@/store/historyStore';
import { useToast } from '@/hooks/use-toast';
import { SearchInput } from '@/components/search/SearchInput';
import { SearchResults } from '@/components/search/SearchResults';
import { SearchForm } from '@/components/search/SearchForm';
import { FollowUpInput } from '@/components/FollowUpInput';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Clock } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { SourceList } from '@/components/search/SourceList';
import { HistoryDialog } from '@/components/chat/HistoryDialog';
import { NaviBar } from '@/components/NaviBar';
import { ToolBox } from '@/components/ToolBox';
import API from '@/config/api';
// import { useSearchParams } from 'react-router-dom';

interface SearchResult {
  summary: string;
  sources: any[];
}

export function Search() {
  const [, setLocation] = useLocation();
  const params = new URLSearchParams(window.location.search);
  const query = params.get('q') || '';
  const engine = params.get('engine') || 'google';
  const isFromHome = !params.get('conversationId');
  const { startNewConversation } = useHistoryStore();

  useEffect(() => {
    if (isFromHome && query) {
      // 如果是从主页来的新搜索，开始新对话
      startNewConversation();
    }
  }, [query, startNewConversation]);

  const [sessionId, setSessionId] = useState<string | null>(null);
  const [currentResults, setCurrentResults] = useState<SearchResult | null>(null);
  const [originalQuery, setOriginalQuery] = useState<string | null>(null);
  const [isFollowUp, setIsFollowUp] = useState(false);
  const [followUpQuery, setFollowUpQuery] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [searchEngines, setSearchEngines] = useState([]);

  const historyStore = useHistoryStore.getState();

  useEffect(() => {
    setIsOpen(historyStore.isOpen);
  }, [historyStore.isOpen]);

  // Extract query from URL, handling both initial load and subsequent navigation
  const getQueryFromUrl = () => {
    const searchParams = new URLSearchParams(window.location.search);
    return searchParams.get('q') || '';
  };
  
  const [searchQuery, setSearchQuery] = useState(getQueryFromUrl);
  const [refetchCounter, setRefetchCounter] = useState(0);

  const { data, isLoading, error: queryError } = useQuery<string | Error | undefined>({
    queryKey: ['search', searchQuery, refetchCounter],
    queryFn: async () => {
      if (!searchQuery) return null;
      // 使用正确的API端点
      const response = await fetch(`${API.SEARCH.BY_ENGINE(engine)}?q=${encodeURIComponent(searchQuery)}`);
      if (!response.ok) throw new Error('Search failed');
      const result = await response.json();
      // console.log('Search API Response:', JSON.stringify(result, null, 2));
      if (result.sessionId) {
        setSessionId(result.sessionId);
        setCurrentResults(result);
        if (!originalQuery) {
          setOriginalQuery(searchQuery);
        }
        setIsFollowUp(false);
      }
      return result;
    },
    enabled: !!searchQuery,
  });

  // Follow-up mutation
  const { toast } = useToast();
  const followUpMutation = useMutation({
    mutationFn: async (followUpQuery: string) => {
      toast({
        title: '正在加载中...',
        description: '请稍候，我们正在处理您的请求',
        duration: Infinity, // Keep open until manually dismissed
      });
      if (!sessionId) {
        // 使用正确的API端点
        const response = await fetch(`${API.SEARCH.BY_ENGINE(engine)}?q=${encodeURIComponent(followUpQuery)}`);
        if (!response.ok) throw new Error('Search failed');
        const result = await response.json();
        // console.log('New Search API Response:', JSON.stringify(result, null, 2));
        if (result.sessionId) {
          setSessionId(result.sessionId);
          setOriginalQuery(searchQuery);
          setIsFollowUp(false);
        }
        return result;
      }

      // 使用正确的API端点
      const followUpEndpoint = `${API.SEARCH.BY_ENGINE(engine)}/follow-up`;
      const response = await fetch(followUpEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          query: followUpQuery,
          previousSummary: currentResults?.summary || ''
        }),
      });
      
      if (!response.ok) {
        if (response.status === 404) {
          // 使用正确的API端点
          const newResponse = await fetch(`${API.SEARCH.BY_ENGINE(engine)}?q=${encodeURIComponent(followUpQuery)}`);
          if (!newResponse.ok) throw new Error('Search failed');
          const result = await newResponse.json();
          // console.log('Fallback Search API Response:', JSON.stringify(result, null, 2));
          if (result.sessionId) {
            setSessionId(result.sessionId);
            setOriginalQuery(searchQuery);
            setIsFollowUp(false);
          }
          return result;
        }
        
        const errorData = await response.json();
        throw new Error(errorData.message || 'Follow-up failed');
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let result = { summary: '', sources: [] };
      
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          try {
            const partialResult = JSON.parse(chunk);
            result = { ...result, ...partialResult };
            setCurrentResults(result);
          } catch (error) {
            console.error('Error parsing streaming response:', error);
          }
        }
      }
      
      // console.log('Follow-up API Response:', JSON.stringify(result, null, 2));
      return result;
    },
    onSuccess: (result) => {
      setCurrentResults(result);
      setIsFollowUp(true);
      // toast.dismiss();
    },
    onError: (error) => {
      console.error('Follow-up error:', error);
      // toast.dismiss();
    },
  });

  const handleSearch = async (newQuery: string) => {
    if (newQuery === searchQuery) {
      // If it's the same query, increment the refetch counter to trigger a new search
      setRefetchCounter(c => c + 1);
      return;
    }

    setSessionId(null); // Clear session on new search
    setOriginalQuery(null); // Clear original query
    setIsFollowUp(false); // Reset follow-up state
    setSearchQuery(newQuery);
    setCurrentResults(null); // 重置当前结果
    
    // Update URL without triggering a page reload
    const newUrl = `/search?q=${encodeURIComponent(newQuery)}&engine=${encodeURIComponent(engine)}`;
    window.history.pushState({}, '', newUrl);

    try {
      // 使用正确的API端点
      const response = await fetch(`${API.SEARCH.BY_ENGINE(engine)}?q=${encodeURIComponent(newQuery)}`);
      if (!response.ok) throw new Error('Search failed');

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let result = { summary: '', sources: [] };
      
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          try {
            const partialResult = JSON.parse(chunk);
            result = { ...result, ...partialResult };
            setCurrentResults(result);
          } catch (error) {
            console.error('Error parsing streaming response:', error);
          }
        }
      }

      // Add to history
      // console.log('Adding to history:', newQuery, result.summary);
      const conversationId = useHistoryStore.getState().startNewConversation();
      useHistoryStore.getState().addMessage(conversationId, {
        type: 'user',
        content: newQuery
      });
      useHistoryStore.getState().addMessage(conversationId, {
        type: 'system',
        content: result.summary
      });
      
      // 更新会话的 summary
      const state = useHistoryStore.getState();
      state.updateSummary(conversationId, result.summary);
      // currentConversationId is automatically set by startNewConversation

    } catch (error) {
      console.error('Search error:', error);
      setError(error instanceof Error ? error.message : 'Search failed');
    }
  };

  const handleFollowUp = async (newFollowUpQuery: string) => {
    // console.log('Handling follow-up:', newFollowUpQuery);

    if (!currentResults?.summary) {
      console.error('No current results or summary found');
      // console.log('Current results:', currentResults);
      throw new Error('Previous summary is required for follow-up questions');
    }

    setFollowUpQuery(newFollowUpQuery);
    const state = useHistoryStore.getState();
    let currentId = state.currentConversationId;

    try {
      // 使用正确的API端点
      const followUpEndpoint = `${API.SEARCH.BY_ENGINE(engine)}/follow-up`;
      const response = await fetch(followUpEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId,
          query: newFollowUpQuery,
          previousSummary: currentResults.summary
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Follow-up failed');
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let result = { summary: '', sources: [] };
      
      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          try {
            const partialResult = JSON.parse(chunk);
            result = { ...result, ...partialResult };
            setCurrentResults(result);
          } catch (error) {
            console.error('Error parsing streaming response:', error);
          }
        }
      }

    // Add to history
    if (!currentId) {
      // If no current conversation exists, create a new one
      currentId = state.startNewConversation();
      
      // Add original query if available
      if (originalQuery) {
        state.addMessage(currentId, {
          type: 'user',
          content: originalQuery
        });
        if (currentResults?.summary) {
          state.addMessage(currentId, {
            type: 'system',
            content: currentResults.summary
          });
        }
      }
    }

    // console.log('Adding follow-up to history:', newFollowUpQuery, result.summary);
    state.addMessage(currentId, {
      type: 'user',
      content: newFollowUpQuery
    });
    state.addMessage(currentId, {
      type: 'system',
      content: result.summary
    });
    
    // 更新会话的 summary
    state.updateSummary(currentId, result.summary);

    } catch (error) {
      console.error('Follow-up error:', error);
      throw error;
    }
  };

  // Automatically start search when component mounts or URL changes
  useEffect(() => {
    const query = getQueryFromUrl();
    if (query && query !== searchQuery) {
      setSessionId(null); // Clear session on URL change
      setOriginalQuery(null); // Clear original query
      setIsFollowUp(false); // Reset follow-up state
      setSearchQuery(query);
    }
  }, [location]);

  // Use currentResults if available, otherwise fall back to data from useQuery
  const displayResults = currentResults || data;

  return (
    <div className="relative min-h-screen">
      <NaviBar />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
        className={`min-h-screen bg-background ${isOpen ? 'pr-[320px]' : ''}`}
      >
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4 }}
          className="w-[90%] mx-auto p-4"
        >
          <motion.div 
            className="flex items-center gap-4 mb-6"
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setLocation('/')}
              className="hidden sm:flex"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>

            <div className="w-full max-w-2xl">
              <SearchForm
                onSearch={(query, selectedEngine) => {
                  // 更新当前选择的引擎
                  if (selectedEngine !== engine) {
                    params.set('engine', selectedEngine);
                    setLocation(`/search?${params.toString()}`);
                  }
                  handleSearch(query);
                }}
                initialQuery={searchQuery}
                className=""
              />
            </div>

            {/* <Button
              variant="ghost"
              size="icon"
              onClick={() => historyStore.toggleDialog()}
              className="hidden sm:flex"
            >
              <Clock className="h-4 w-4" />
            </Button> */}
          </motion.div>

          <AnimatePresence mode="wait">
            <motion.div
              key={searchQuery}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="flex flex-col items-stretch w-full"
            >
              <SearchResults
                query={isFollowUp ? (followUpQuery || '') : searchQuery}
                results={displayResults}
                isLoading={isLoading || followUpMutation.isPending}
                error={error || followUpMutation.error || undefined}
                // isFollowUp={isFollowUp}
                // originalQuery={originalQuery || ''}
                // followUpQuery={followUpQuery || ''}
                key={String(isLoading || followUpMutation.isPending)}
              />

              {displayResults && !isLoading && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  className="mt-6 max-w-2xl"
                >
                  <FollowUpInput
                    onSubmit={handleFollowUp}
                    isLoading={followUpMutation.isPending}
                  />
                </motion.div>
              )}
            </motion.div>
          </AnimatePresence>
        </motion.div>
        <HistoryDialog />
        <div className="fixed bottom-0 left-0 right-0 p-4 bg-background/80 backdrop-blur-sm border-t">
          <div className="max-w-4xl mx-auto">
            <FollowUpInput
              onSubmit={handleFollowUp}
              isLoading={followUpMutation.isPending}
            />
          </div>
        </div>
      </motion.div>
      <ToolBox />
    </div>
  );
}
