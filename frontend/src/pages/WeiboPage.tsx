
import { RedditHeader } from "@/components/RedditHeader"
import { ContentTabs } from "@/components/weibo/ContentTabs"
import { LeftSidebar } from "@/components/weibo/LeftSidebar"
import { RedditStyleLayout } from "@/components/weibo/RedditStyleLayout"
import { PostDetailPage } from "@/components/weibo/PostDetailPage"
import { ChatDialog } from "@/components/weibo/ChatDialog"
import { ImagePreview } from "@/components/weibo/ImagePreview"
import { CreatePostForm } from "@/components/weibo/CreatePostForm"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useState, useEffect } from "react"
import API from "@/config/api"
import { ChannelBanner } from "@/components/weibo/ChannelBanner"
import { RightPanel } from "@/components/ui/RightPanel"
import { useRightPanelStore } from "@/store/rightPanelStore"

export function WeiboPage() {
  // 修改状态管理
  const [posts, setPosts] = useState<any[]>([])
  const [filteredPosts, setFilteredPosts] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalPosts, setTotalPosts] = useState(0)
  // 添加板块选择状态
  const [selectedChannelId, setSelectedChannelId] = useState<string | undefined>(undefined)
  // 添加搜索状态
  const [searchQuery, setSearchQuery] = useState<string>("")

  const [selectedPost, setSelectedPost] = useState<any>(null)
  
  // 新建帖子状态
  const [showCreatePost, setShowCreatePost] = useState(false)

  // 其他对话框状态
  const [chatDialogOpen, setChatDialogOpen] = useState(false)
  const [previewImage, setPreviewImage] = useState<{ src: string; alt?: string } | null>(null)
  const [showImagePreview, setShowImagePreview] = useState(false)
  const [user, setUser] = useState<{ id: string, email: string, username: string } | null>(null)

  const { isOpen: isRightPanelOpen, postId: rightPanelPostId, close: closeRightPanel } = useRightPanelStore()

  const PAGE_SIZE = 5

  // 修改分页获取微博列表函数
  async function fetchPosts(page = 1) {
    setLoading(true)
    try {
      const token = localStorage.getItem('token')
      const params = new URLSearchParams()
      params.append('skip', ((page - 1) * PAGE_SIZE).toString())
      params.append('limit', PAGE_SIZE.toString())

      // 确定API端点
      let apiEndpoint = API.POSTS.LIST
      
      // 如果是热门帖子
      if (selectedChannelId === 'hot') {
        apiEndpoint = API.POSTS.HOT
      } 
      // 如果选择了普通板块，添加板块筛选参数
      else if (selectedChannelId) {
        params.append('channel_id', selectedChannelId)
      }

      const response = await fetch(`${apiEndpoint}?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      })

      // console.log('微博分页接口响应状态:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        // console.log('微博分页接口返回', data);

        // 适配新的API响应格式
        const normalPosts = (data.items || []).filter(
          (post: any) => post.post?.type === 'NORMAL'
        );

        setPosts(normalPosts);
        setCurrentPage(data.page || page);
        setTotalPages(data.total_pages || 0);
        setTotalPosts(data.total || 0);
      } else {
        // 添加错误处理
        const errorText = await response.text();
        // console.error('微博分页接口请求失败:', {
        //   status: response.status,
        //   statusText: response.statusText,
        //   error: errorText
        // });

        // 可以根据具体错误状态码进行不同处理
        if (response.status === 401) {
          // console.error('认证失败，可能需要重新登录');
          // 可以添加重定向到登录页的逻辑
        } else if (response.status === 403) {
          // console.error('权限不足');
        } else if (response.status >= 500) {
          // console.error('服务器内部错误');
        }
      }
    } catch (error) {
      // console.error('获取微博列表失败:', error)
      // 添加网络错误的具体信息
      if (error instanceof TypeError && error.message.includes('fetch')) {
        // console.error('网络连接失败，请检查网络连接或后端服务是否正常运行');
      }
    } finally {
      setLoading(false)
    }
  }

  // 获取特定帖子
  const fetchTargetPost = async (postId: string) => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`${API.API_PATH}/posts/${postId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'accept': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()


        // 将目标帖子添加到帖子列表的最前面
        setPosts(prevPosts => {
          // 检查帖子是否已经在列表中
          const exists = prevPosts.some(post => post.post.id === data.post.id)
          if (exists) {
            return prevPosts
          }
          return [data, ...prevPosts]
        })

        // 设置一个短暂的延时，确保DOM已经更新
        setTimeout(() => {
          const postElement = document.getElementById(`post-${postId}`)
          if (postElement) {
            postElement.scrollIntoView({ behavior: 'smooth' })
            // 添加高亮效果
            postElement.classList.add('bg-orange-50', 'dark:bg-orange-900/20')
            // 3秒后移除高亮效果
            setTimeout(() => {
              postElement.classList.remove('bg-orange-50', 'dark:bg-orange-900/20')
            }, 3000)
          }
        }, 100)
      }
    } catch (error) {
      // console.error('获取目标帖子失败:', error)
    }
  }

  // 当板块选择改变时重新获取帖子
  useEffect(() => {
    fetchPosts(1)  // 重置到第一页
  }, [selectedChannelId])
  
  // 当搜索查询改变时搜索帖子
  useEffect(() => {
    if (!searchQuery) {
      // 如果没有搜索查询，显示所有帖子
      setFilteredPosts([]);
      // 清除URL中的search参数
      const url = new URL(window.location.href);
      url.searchParams.delete('search');
      window.history.replaceState({}, '', url);
      return;
    }
    
    // 从后端搜索所有NORMAL类型的帖子
    const searchPosts = async () => {
      setLoading(true);
      try {
        const token = localStorage.getItem('token');
        const params = new URLSearchParams();
        params.append('skip', '0');
        params.append('limit', '100'); // 搜索时获取更多帖子
        
        // 调用后端API获取所有NORMAL类型的帖子
        const response = await fetch(`${API.POSTS.LIST}?${params.toString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'accept': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          // console.log('搜索API返回', data);

          // 在前端过滤搜索结果
          const filtered = (data.items || []).filter((post: any) => {
            const content = post.post?.content?.toLowerCase() || '';
            const title = post.post?.title?.toLowerCase() || '';
            const username = post.owner?.username?.toLowerCase() || '';
            const displayName = post.owner?.display_name?.toLowerCase() || '';
            const query = searchQuery.toLowerCase();
            
            return (
              content.includes(query) || 
              title.includes(query) || 
              username.includes(query) || 
              displayName.includes(query)
            );
          });
          
          setFilteredPosts(filtered);
        } else {
          // console.error('搜索API请求失败:', response.status, response.statusText);
          // 如果API失败，回退到本地搜索
          const filtered = posts.filter(post => {
            const content = post.post?.content?.toLowerCase() || '';
            const title = post.post?.title?.toLowerCase() || '';
            const username = post.user?.username?.toLowerCase() || '';
            const displayName = post.user?.display_name?.toLowerCase() || '';
            const query = searchQuery.toLowerCase();
            
            return (
              content.includes(query) || 
              title.includes(query) || 
              username.includes(query) || 
              displayName.includes(query)
            );
          });
          setFilteredPosts(filtered);
        }
      } catch (error) {
        // console.error('搜索失败:', error);
        // 如果网络错误，回退到本地搜索
        const filtered = posts.filter(post => {
          const content = post.post?.content?.toLowerCase() || '';
          const title = post.post?.title?.toLowerCase() || '';
          const username = post.user?.username?.toLowerCase() || '';
          const displayName = post.user?.display_name?.toLowerCase() || '';
          const query = searchQuery.toLowerCase();
          
          return (
            content.includes(query) || 
            title.includes(query) || 
            username.includes(query) || 
            displayName.includes(query)
          );
        });
        setFilteredPosts(filtered);
      } finally {
        setLoading(false);
      }
    };
    
    // 执行搜索
    searchPosts();
    
    // 更新URL中的search参数
    const url = new URL(window.location.href);
    url.searchParams.set('search', searchQuery);
    window.history.replaceState({}, '', url);
  }, [searchQuery])

  // 获取用户信息
  useEffect(() => {
    const userJson = localStorage.getItem('user');
    if (userJson) {
      try {
        const userData = JSON.parse(userJson);
        setUser(userData);
      } catch (error) {
        // console.error('解析用户数据失败:', error);
      }
    }
  }, []);

  // 组件挂载时获取微博列表和处理URL参数
  useEffect(() => {
    fetchPosts(1)  // 修改：从页码1开始，而不是0

    // 检查URL中是否有参数
    const urlParams = new URLSearchParams(window.location.search)
    const postId = urlParams.get('post')
    const channelId = urlParams.get('channel')
    const commentId = urlParams.get('comment')
    const search = urlParams.get('search')
    
    // 如果有搜索参数，设置搜索查询
    if (search) {
      setSearchQuery(search)
    }
    
    // 如果有频道参数，先设置频道
    if (channelId) {
      setSelectedChannelId(channelId)
    }
    
    // 如果有帖子参数，获取并跳转到帖子
    if (postId) {
      // 如果有频道参数，等待频道设置完成后再获取帖子
      if (channelId) {
        setTimeout(() => fetchTargetPost(postId), 100);
      } else {
        fetchTargetPost(postId);
      }
    }

    // 添加自定义事件监听器，用于处理从通知点击的情况
    const handleLoadPost = (event: CustomEvent) => {
      const { postId, commentId } = event.detail;
      if (postId) {
        fetchTargetPost(postId);
        
        // 如果有评论ID，等待帖子加载后滚动到评论
        if (commentId) {
          setTimeout(() => {
            const commentElement = document.getElementById(`comment-${commentId}`);
            if (commentElement) {
              commentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
              commentElement.classList.add('bg-orange-50', 'dark:bg-orange-900/20', 'ring-2', 'ring-orange-500', 'ring-opacity-50');
              setTimeout(() => {
                commentElement.classList.remove('bg-orange-50', 'dark:bg-orange-900/20', 'ring-2', 'ring-orange-500', 'ring-opacity-50');
              }, 3000);
            }
          }, 1000);
        }
      }
    };

    // 添加搜索事件监听器
    const handleSearch = (event: CustomEvent) => {
      const { query } = event.detail;
      // 无论 query 是否为空，都设置搜索查询
      // 这样可以处理清空搜索的情况
      setSearchQuery(query);
    };

    // 添加事件监听器
    window.addEventListener('loadPost', handleLoadPost as EventListener);
    window.addEventListener('postSearch', handleSearch as EventListener);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('loadPost', handleLoadPost as EventListener);
      window.removeEventListener('postSearch', handleSearch as EventListener);
    };
  }, [])

  // 加载更多
  // 删除这些旧的状态变量（如果还存在的话）
  // const [hasMore, setHasMore] = useState(true)
  // const [offset, setOffset] = useState(0)

  // 删除这个旧的函数
  // function handleLoadMore() {
  //   if (loading || !hasMore) return
  //   fetchPosts(offset, true)
  // }

  // 分页处理函数
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && !loading) {
      fetchPosts(page)
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }

  const handlePrevPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1)
    }
  }

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1)
    }
  }

  // 生成页码数组
  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i)
    }
    return pages
  }

  // 渲染主要内容
  const renderMainContent = () => {
    // 如果显示新建帖子表单
    if (showCreatePost) {
      return (
        <div className="bg-card border rounded-lg shadow-sm">
          {/* 头部 */}
          <div className="flex items-center justify-between p-4 border-b">
            <h2 className="text-lg font-semibold">发布新帖子</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowCreatePost(false)}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </div>
          
          {/* 创建帖子表单 */}
          <CreatePostForm
            selectedChannelId={selectedChannelId}
            onPostSuccess={() => {
              setShowCreatePost(false);
              fetchPosts(1);
            }}
            onCancel={() => setShowCreatePost(false)}
          />
        </div>
      );
    }

    // 如果选择了帖子，显示帖子详情
    if (selectedPost) {
      return (
        <PostDetailPage
          post={selectedPost}
          user={user}
          onBack={() => setSelectedPost(null)}
          onImageClick={(src, alt) => {
            setPreviewImage({ src, alt });
            setShowImagePreview(true);
          }}
          onOpenChat={(post) => {
            setChatDialogOpen(true);
          }}
        />
      );
    }

    // 默认显示帖子列表
    return (
      <div className="space-y-4">
        
        {/* 内容标签 */}
        <ContentTabs
          posts={searchQuery ? filteredPosts : posts}
          loading={loading}
          onPostClick={(post) => {
            // 如果帖子有channel_id，跳转到该channel页面
            if (post.channel?.id) {
              setSelectedChannelId(post.channel.id);
              // 可选：设置一个延时，确保channel切换后再设置selectedPost
              setTimeout(() => {
                setSelectedPost(post);
              }, 100);
            } else {
              // 如果没有channel信息，直接显示帖子详情
              setSelectedPost(post);
            }
          }}
          onChatOpen={(post) => {
            setChatDialogOpen(true);
          }}
          onImageClick={(src, alt) => {
            setPreviewImage({ src, alt });
            setShowImagePreview(true);
          }}
        />
        
        {/* 显示搜索结果信息 */}
        {searchQuery && (
          <div className="flex items-center justify-between bg-muted/20 p-3 rounded-lg">
            <div className="text-sm">
              <span className="font-medium">搜索 "{searchQuery}" 的结果:</span> 
              <span className="ml-2 text-muted-foreground">{filteredPosts.length} 条微博</span>
            </div>
            <Button 
              variant="outline" 
              size="sm"
              className="text-xs"
              onClick={() => setSearchQuery("")}
            >
              清除搜索
            </Button>
          </div>
        )}

        {/* 分页控制器 */}
        {!searchQuery && totalPages > 1 && (
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-4 lg:space-y-0 py-6 px-4 bg-muted/30 rounded-lg">
            {/* 分页信息 */}
            <div className="text-sm text-muted-foreground lg:order-1">
              第 {currentPage} 页，共 {totalPages} 页 · 共 {totalPosts} 条微博
            </div>

            {/* 分页按钮 */}
            <div className="flex items-center justify-center space-x-2 lg:order-2">
              {/* 上一页 */}
              <button
                onClick={handlePrevPage}
                disabled={currentPage === 1 || loading}
                className="px-3 py-2 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"
              >
                上一页
              </button>

              {/* 页码 */}
              {getPageNumbers().map(page => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  disabled={loading}
                  className={`px-3 py-2 text-sm border rounded-md disabled:cursor-not-allowed transition-colors ${page === currentPage
                    ? 'bg-primary text-primary-foreground shadow-sm'
                    : 'hover:bg-muted'
                    }`}
                >
                  {page}
                </button>
              ))}

              {/* 下一页 */}
              <button
                onClick={handleNextPage}
                disabled={currentPage === totalPages || loading}
                className="px-3 py-2 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"
              >
                下一页
              </button>
            </div>

            {/* 快速跳转 */}
            <div className="flex items-center justify-center lg:justify-end space-x-2 text-sm lg:order-3">
              <span className="text-muted-foreground">跳转到</span>
              <input
                type="number"
                min="1"
                max={totalPages}
                placeholder={currentPage.toString()}
                className="w-16 px-2 py-1 text-center border rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    const page = parseInt((e.target as HTMLInputElement).value)
                    if (page >= 1 && page <= totalPages) {
                      handlePageChange(page)
                        ; (e.target as HTMLInputElement).value = ''
                    }
                  }
                }}
              />
              <span className="text-muted-foreground">页</span>
            </div>
          </div>
        )}
        
        {/* 搜索结果分页 */}
        {searchQuery && filteredPosts.length > 0 && (
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center space-y-4 lg:space-y-0 py-6 px-4 bg-muted/30 rounded-lg">
            <div className="text-sm text-muted-foreground lg:order-1">
              搜索结果：共 {filteredPosts.length} 条微博
            </div>
          </div>
        )}
      </div>
    );
  }

  // 获取当前选中的post对象
  const rightPanelPost = rightPanelPostId ? posts.find(p => p.post.id === rightPanelPostId) : null

  return (
    <div className="h-screen bg-background flex flex-col">
      <RightPanel>
        {rightPanelPost && (
          <ChatDialog open={isRightPanelOpen} onOpenChange={closeRightPanel} post={rightPanelPost} />
        )}
      </RightPanel>
      {/* Reddit风格Header - 固定高度 */}
      <RedditHeader />
      {/* 主内容区域 - 占据剩余高度 */}
      <div className="flex flex-1 overflow-hidden">
        {/* 左侧边栏 - 固定宽度，不可滚动 */}
        <div className="w-64 bg-card border-r border-border flex-shrink-0">
          <LeftSidebar
            selectedChannelId={selectedChannelId}
            onChannelSelect={(channelId) => {
              setSelectedChannelId(channelId);
              setSelectedPost(null); // 切换频道时关闭详情页
            }}
          />
        </div>
        {/* 主要内容区域 */}
        <div className="flex-1">
          {selectedChannelId ? (
            // 选择了板块时显示Reddit风格布局
            <div className="h-full mx-24">
              <RedditStyleLayout 
                channelId={selectedChannelId} 
                onCreatePost={selectedChannelId !== 'hot' ? () => setShowCreatePost(true) : undefined}
                hideBanner={!!selectedPost} // 如果是详情页则隐藏横幅
              >
                {renderMainContent()}
              </RedditStyleLayout>
            </div>
          ) : (
            // 首页显示传统布局
            <div className="h-full overflow-y-auto">
              <div className="max-w-6xl mx-auto px-8 py-6">
                {renderMainContent()}
              </div>
            </div>
          )}
        </div>
      </div>
      {/* 右侧固定导航栏 - 居中位置 */}
      {/* <div className="fixed right-4 top-1/2 -translate-y-1/2 z-40">
        <div className="bg-card/90 backdrop-blur-sm border border-border rounded-lg shadow-lg p-4 max-w-xs">
          <NaviBar />
        </div>
      </div> */}
      {/* 图片预览 */}
      {previewImage?.src && (
        <ImagePreview
          src={previewImage.src}
          alt={previewImage.alt}
          open={showImagePreview}
          onOpenChange={(open) => {
            setShowImagePreview(open);
            if (!open) {
              setPreviewImage(null);
            }
          }}
        />
      )}
    </div>
  )
}
