import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MessageCircle, Heart, AtSign, UserPlus, FileText, Bell, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { useNotifications } from '@/hooks/useNotifications';
import { useNotificationNavigator } from '@/components/weibo/NotificationNavigator';
import { NotificationStats } from '@/components/weibo/NotificationStats';
import { NotificationSettings } from '@/components/weibo/NotificationSettings';
import { NotificationType, type NotificationData } from '@/types/notification';

export function NotificationsPage() {
  const [activeTab, setActiveTab] = useState('all');
  const {
    notifications,
    unreadCount,
    loading,
    fetchNotifications,
    markAsRead,
    markAllAsRead
  } = useNotifications();
  const { handleNotificationClick: navigateToNotification } = useNotificationNavigator();

  // 获取通知类型的图标和描述
  const getNotificationInfo = (type: NotificationType) => {
    switch (type) {
      case NotificationType.MENTION:
        return {
          icon: <AtSign className="h-5 w-5 text-blue-500" />,
          action: '在帖子中提到了你',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20'
        };
      case NotificationType.COMMENT:
        return {
          icon: <MessageCircle className="h-5 w-5 text-green-500" />,
          action: '评论了你的帖子',
          color: 'text-green-600',
          bgColor: 'bg-green-50 dark:bg-green-900/20'
        };
      case NotificationType.LIKE:
        return {
          icon: <Heart className="h-5 w-5 text-red-500" />,
          action: '点赞了你的帖子',
          color: 'text-red-600',
          bgColor: 'bg-red-50 dark:bg-red-900/20'
        };
      case NotificationType.FOLLOW:
        return {
          icon: <UserPlus className="h-5 w-5 text-purple-500" />,
          action: '关注了你',
          color: 'text-purple-600',
          bgColor: 'bg-purple-50 dark:bg-purple-900/20'
        };
      case NotificationType.CHANNEL_POST:
        return {
          icon: <FileText className="h-5 w-5 text-orange-500" />,
          action: '在你关注的频道发布了新帖子',
          color: 'text-orange-600',
          bgColor: 'bg-orange-50 dark:bg-orange-900/20'
        };
      default:
        return {
          icon: <Bell className="h-5 w-5 text-gray-500" />,
          action: '发送了通知',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50 dark:bg-gray-900/20'
        };
    }
  };

  // 格式化时间
  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}天前`;
    
    return date.toLocaleDateString('zh-CN');
  };

  // 处理通知点击
  const handleNotificationClick = async (notification: NotificationData) => {
    try {
      await navigateToNotification(notification);
      if (!notification.is_read) {
        markAsRead(notification.id);
      }
    } catch (error) {
      console.error('处理通知点击失败:', error);
      toast.error('跳转失败，请稍后重试');
    }
  };

  // 处理标记所有通知为已读
  const handleMarkAllAsRead = async () => {
    const success = await markAllAsRead();
    if (success) {
      toast.success('所有通知已标记为已读');
    } else {
      toast.error('操作失败');
    }
  };

  // 根据标签页筛选通知
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.is_read;
    return notification.type === activeTab;
  });

  // 页面加载时获取通知
  useEffect(() => {
    fetchNotifications({ limit: 50, unreadOnly: false });
  }, [fetchNotifications]);

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">通知中心</h1>
          <p className="text-muted-foreground">
            {unreadCount > 0 ? `你有 ${unreadCount} 条未读通知` : '暂无未读通知'}
          </p>
        </div>
        
        {notifications.length > 0 && (
          <Button onClick={handleMarkAllAsRead} variant="outline">
            全部标记为已读
          </Button>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-8">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="unread">未读</TabsTrigger>
          <TabsTrigger value={NotificationType.MENTION}>@提醒</TabsTrigger>
          <TabsTrigger value={NotificationType.COMMENT}>评论</TabsTrigger>
          <TabsTrigger value={NotificationType.LIKE}>点赞</TabsTrigger>
          <TabsTrigger value={NotificationType.FOLLOW}>关注</TabsTrigger>
          <TabsTrigger value="stats">统计</TabsTrigger>
          <TabsTrigger value="settings">设置</TabsTrigger>
        </TabsList>

        {/* 通知列表标签页 */}
        {['all', 'unread', NotificationType.MENTION, NotificationType.COMMENT, NotificationType.LIKE, NotificationType.FOLLOW].map((tab) => (
          <TabsContent key={tab} value={tab} className="mt-6">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-muted-foreground">加载中...</p>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    {activeTab === 'unread' ? '暂无未读通知' : '暂无通知'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2">
                {filteredNotifications.map((notification) => {
                  const notificationInfo = getNotificationInfo(notification.type);
                  
                  return (
                    <Card
                      key={notification.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        !notification.is_read ? 'border-l-4 border-l-blue-500' : ''
                      }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-4">
                          {/* 用户头像 */}
                          <div className="relative flex-shrink-0">
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={notification.sender_avatar} />
                              <AvatarFallback>
                                {notification.sender_name?.slice(0, 2) || '?'}
                              </AvatarFallback>
                            </Avatar>
                            {/* 通知类型图标 */}
                            <div className={`absolute -bottom-1 -right-1 ${notificationInfo.bgColor} rounded-full p-1 border-2 border-background`}>
                              {notificationInfo.icon}
                            </div>
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            {/* 通知主要信息 */}
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="font-medium text-sm">
                                {notification.sender_name}
                              </span>
                              <span className={`${notificationInfo.color} text-sm`}>
                                {notificationInfo.action}
                              </span>
                              {!notification.is_read && (
                                <Badge variant="secondary" className="text-xs">
                                  未读
                                </Badge>
                              )}
                            </div>
                            
                            {/* 通知内容 */}
                            {notification.content && (
                              <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                                {notification.content}
                              </p>
                            )}
                            
                            {/* 时间 */}
                            <p className="text-xs text-muted-foreground">
                              {formatTimeAgo(notification.created_at)}
                            </p>
                          </div>
                          
                          {/* 操作按钮 */}
                          <div className="flex items-center space-x-2">
                            {!notification.is_read && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  markAsRead(notification.id);
                                }}
                                className="text-xs"
                              >
                                标记已读
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>
        ))}

        {/* 统计标签页 */}
        <TabsContent value="stats" className="mt-6">
          <NotificationStats />
        </TabsContent>

        {/* 设置标签页 */}
        <TabsContent value="settings" className="mt-6">
          <NotificationSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
}