import { useEffect, useState, useMemo, useRef } from "react";
// 移除注释掉的导入，直接从 store 导入 Diagram 类型
import { AlertCircle, ImageOff, Trash2, Plus } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { NaviBar } from "@/components/NaviBar";
import { ToolBox } from "@/components/ToolBox";
import { DiagramDetail } from "@/components/diagram/DiagramDetail";
import { useDiagramStore } from "@/store/diagramStore";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Jumbotron } from "@/components/Jumbotron";
// 从 diagramStore 导入 Diagram 类型
import type { Diagram } from "@/store/diagramStore";
import { Sheet, Sheet<PERSON>ontent, <PERSON><PERSON><PERSON>eader, SheetTitle } from "@/components/ui/sheet";
// 导入 URL 转换工具
import { convertSupabaseUrl } from "@/utils/url-utils";

// 使用 diagramStore 中的 DiagramType 类型
import { DiagramType } from "@/store/diagramStore";
import { AddDiagramDialog } from "@/components/diagram/AddDiagramDialog";

// 定义微博类型
interface Post {
  id: string;
  content: string;
  images?: string[];
  timestamp: string;
  owner_id: string;
}

export function Diagrams() {
  const {
    diagrams,
    isLoading,
    error,
    selectedType,
    setSelectedType,
    fetchDiagrams,
    deleteDiagram,
  } = useDiagramStore();

  // Set default type to user_uploads
  useEffect(() => {
    setSelectedType("user_uploads");
  }, [setSelectedType]);

  const [diagramToDelete, setDiagramToDelete] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showTypeDropdown, setShowTypeDropdown] = useState(false);
  const [showWeiboSheet, setShowWeiboSheet] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);

  // 添加下拉框引用，用于检测点击外部
  const dropdownRef = useRef<HTMLDivElement>(null);


  // 在文件顶部添加 posts 状态
  const [posts, setPosts] = useState<Post[]>([]);
  const [loadingPosts, setLoadingPosts] = useState(false);

  // 在 useEffect 中添加获取微博数据的逻辑
  useEffect(() => {
    const fetchPosts = async () => {
      setLoadingPosts(true);
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/posts/', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'accept': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setPosts(data.items || []);
        }
      } catch (error) {
        console.error('获取微博列表失败:', error);
      } finally {
        setLoadingPosts(false);
      }
    };

    // 当 Sheet 打开时获取数据
    if (showWeiboSheet) {
      fetchPosts();
    }
  }, [showWeiboSheet]);


  useEffect(() => {
    fetchDiagrams();
  }, []);

  // 添加点击外部关闭下拉框的功能
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowTypeDropdown(false);
      }
    }

    // 添加事件监听器
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // 清理事件监听器
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const diagramTypes = [
    { value: "user_uploads", label: "用户收藏" },
    { value: "all", label: "全部" },
  ];

  const renderImage = (diagram: Diagram) => {
    if (!diagram.image_url) {
      return (
        <div className="aspect-square flex items-center justify-center bg-gray-100 rounded-lg">
          <ImageOff className="h-8 w-8 text-gray-400" />
        </div>
      );
    }

    // 转换 Supabase 存储 URL
    const convertedImageUrl = convertSupabaseUrl(diagram.image_url);

    return (
      <img
        className="aspect-square w-full h-full object-cover rounded-lg transition-all duration-300 group-hover:opacity-75"
        src={convertedImageUrl}
        alt={diagram.name}
      />
    );
  };

  // 修改 handleDelete 函数参数类型
  const handleDelete = async (id: string) => {
    try {
      await deleteDiagram(id);
    } catch (error) {
      console.error("Error deleting diagram:", error);
    }
    setDiagramToDelete(null);
  };

  const filteredDiagrams = useMemo(() => {
    return diagrams
      .filter((d) => {
        // 处理筛选类型
        if (selectedType === "all") {
          return true; // 显示所有类型（包括微博图片）
        } else if (selectedType === "user_uploads") {
          // 用户上传类型不包括微博图片（type为"blog"）
          return d.type !== "blog";
        } else {
          // 特定类型
          return d.type === selectedType;
        }
      })
      .filter(
        (d) =>
          searchQuery === "" ||
          d.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          d.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          d.source?.toLowerCase().includes(searchQuery.toLowerCase())
      );
  }, [diagrams, selectedType, searchQuery]);

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // 在组件顶部添加分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const paginatedDiagrams = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredDiagrams.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredDiagrams, currentPage]);

  const totalPages = Math.ceil(filteredDiagrams.length / itemsPerPage);

  // TODO: 过滤类型
  return (
    <div className="min-h-screen bg-background">
      <NaviBar />
        <Jumbotron title="图库" subtitle="君子性非异也，善假于物也." />
      <div className="container max-w-screen-2xl mx-auto px-8 py-8">

        <div className="max-w-3xl mx-auto mb-8 flex items-start gap-4">
          <form className="flex-1">
            <div className="flex relative" ref={dropdownRef}>
              <button
                type="button"
                onClick={() => setShowTypeDropdown(!showTypeDropdown)}
                className="flex-shrink-0 z-10 inline-flex items-center py-2.5 px-4 text-sm font-medium text-center text-gray-900 bg-gray-100 border border-gray-300 rounded-s-lg hover:bg-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 dark:bg-gray-700 dark:hover:bg-gray-600 dark:focus:ring-gray-700 dark:text-white dark:border-gray-600"
              >
                {/* 显示当前选择的类型标签 */}
                {selectedType === "all"
                  ? "全部类型"
                  : selectedType === "user_uploads"
                    ? "用户收藏"
                    : diagramTypes.find((t) => t.value === selectedType)?.label || "全部类型"}
                <svg
                  className="w-2.5 h-2.5 ms-2.5"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 10 6"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m1 1 4 4 4-4"
                  />
                </svg>
              </button>

              {showTypeDropdown && (
                <div className="absolute top-full left-0 z-10 mt-1 bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                  <ul className="py-2 text-sm text-gray-700 dark:text-gray-200">
                    {/* 只显示全部和用户上传两个选项 */}
                    {diagramTypes.slice(0, 2).map((type) => (
                      <li key={type.value}>
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedType(type.value as DiagramType);
                            setShowTypeDropdown(false);
                          }}
                          className="inline-flex w-full px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white"
                        >
                          {type.label}
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="relative w-full">
                <input
                  type="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block p-2.5 w-full z-20 text-sm text-gray-900 bg-gray-50 rounded-e-lg border-s-gray-50 border-s-2 border border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-s-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:border-blue-500"
                  placeholder="搜索图表名称、描述或来源..."
                />
                <button
                  type="submit"
                  className="absolute top-0 end-0 p-2.5 text-sm font-medium h-full text-white bg-blue-700 rounded-e-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                >
                  <svg
                    className="w-4 h-4"
                    aria-hidden="true"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 20 20"
                  >
                    <path
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                    />
                  </svg>
                  <span className="sr-only">搜索</span>
                </button>
              </div>
            </div>
          </form>

          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => setShowAddDialog(true)}
              className="flex-shrink-0 p-2.5 h-[42px] text-white bg-blue-500 rounded-lg border border-blue-500 hover:bg-blue-600 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            >
              <Plus className="w-4 h-4" />
              <span className="sr-only">收藏图片</span>
            </button>

            <button
              type="button"
              onClick={() => setShowWeiboSheet(true)}
              className="flex-shrink-0 p-2.5 h-[42px] text-white bg-pink-500 rounded-lg border border-pink-500 hover:bg-pink-600 focus:ring-4 focus:outline-none focus:ring-pink-300 dark:bg-pink-600 dark:hover:bg-pink-700 dark:focus:ring-pink-800"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-4 h-4">
                <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                <line x1="7" y1="7" x2="7.01" y2="7"></line>
              </svg>
              <span className="sr-only">微博图片</span>
            </button>
          </div>
        </div>

        <div className="mt-4 grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-6 pl-16">
          {isLoading ? (
            <div className="col-span-full text-center py-8">
              <div className="animate-spin h-8 w-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto"></div>
              <p className="mt-4 text-gray-600">加载中...</p>
            </div>
          ) : filteredDiagrams.length === 0 ? (
            <div className="col-span-full text-center py-8 text-gray-600">
              未找到匹配的图表
            </div>
          ) : (
            <>
              {paginatedDiagrams.map((diagram) => (
                <DiagramDetail key={diagram.id} diagram={diagram}>
                  <div className="relative group cursor-pointer">
                    {/* Card Container with elegant styling */}
                    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm hover:shadow-xl transition-all duration-500 ease-out transform hover:-translate-y-1 overflow-hidden border border-gray-100 dark:border-gray-700">
                      {/* Image Container */}
                      <div className="aspect-square overflow-hidden relative">
                        {renderImage(diagram)}

                        {/* Overlay with gradient and controls */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300">
                          {/* Delete button - top right */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setDiagramToDelete(diagram.id);
                            }}
                            className="absolute top-3 right-3 p-2 rounded-full bg-white/10 backdrop-blur-sm hover:bg-red-500/20 transition-all duration-200 opacity-0 group-hover:opacity-100"
                          >
                            <Trash2 className="h-4 w-4 text-white hover:text-red-400" />
                          </button>

                          {/* Title overlay - bottom */}
                          <div className="absolute bottom-0 left-0 right-0 p-4">
                            <h3 className="text-white font-medium text-sm truncate drop-shadow-sm">
                              {diagram.name}
                            </h3>
                            {diagram.description && (
                              <p className="text-white/80 text-xs mt-1 line-clamp-2 drop-shadow-sm">
                                {diagram.description}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Card Footer */}
                      <div className="p-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700">
                        <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate mb-1">
                          {diagram.name}
                        </h3>
                        {diagram.source && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            来源: {diagram.source}
                          </p>
                        )}
                        <div className="flex items-center justify-between mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {diagram.type === 'user_uploads' ? '收藏' : diagram.type}
                          </span>
                          <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                        </div>
                      </div>
                    </div>

                    <AlertDialog
                      open={diagramToDelete === diagram.id}
                      onOpenChange={() => setDiagramToDelete(null)}
                    >
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>确认删除</AlertDialogTitle>
                          <AlertDialogDescription>
                            确定要删除这张图表吗？此操作无法撤销。
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>取消</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(diagram.id)}
                            className="bg-red-500 hover:bg-red-600"
                          >
                            删除
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </DiagramDetail>
              ))}

              {/* 分页控制 */}
              <div className="col-span-full px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-t border-gray-200 dark:border-neutral-700">
                <div>
                  <p className="text-sm text-gray-600 dark:text-neutral-400">
                    显示 {paginatedDiagrams.length} 条，共 {filteredDiagrams.length} 条
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {Array.from({ length: totalPages }, (_, i) => (
                    <button
                      key={i}
                      onClick={() => setCurrentPage(i + 1)}
                      className={`px-3 py-1 rounded-lg ${currentPage === i + 1
                        ? "bg-blue-600 text-white"
                        : "bg-gray-100 text-gray-600 dark:bg-neutral-700 dark:text-neutral-300"
                        }`}
                    >
                      {i + 1}
                    </button>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      <ToolBox />

      <AddDiagramDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
      />

      <Sheet open={showWeiboSheet} onOpenChange={setShowWeiboSheet}>
        <SheetContent side="right" className="w-[400px] sm:w-[540px] h-screen overflow-hidden flex flex-col">
          <SheetHeader>
            <SheetTitle>微博图片</SheetTitle>
          </SheetHeader>
          <div className="flex flex-col gap-4 mt-4 overflow-y-auto max-h-[calc(100vh-120px)]">
            {loadingPosts ? (
              <div className="flex items-center justify-center h-40">
                <div className="animate-spin h-8 w-8 border-4 border-pink-500 border-t-transparent rounded-full"></div>
                <p className="ml-3 text-gray-600">加载中...</p>
              </div>
            ) : posts.length === 0 ? (
              <div className="flex items-center justify-center h-40 text-gray-500">
                暂无图片
              </div>
            ) : (
              <>
                {posts.map((item: Post, index: number) =>
                  item.images?.map((image: string, imgIndex: number) => (
                    <div key={`${index}-${imgIndex}`} className="relative group cursor-pointer">
                      <img
                        src={convertSupabaseUrl(image)}
                        alt={`微博图片 ${imgIndex + 1}`}
                        className="w-full object-contain rounded-lg transition-all duration-300 group-hover:opacity-75"
                        onClick={() => {
                          window.open(convertSupabaseUrl(image), '_blank');
                        }}
                      />
                    </div>
                  ))
                )}
              </>
            )}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
