import { useEffect, useState, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import { NaviBar } from "@/components/NaviBar";
import { Jumbotron } from "@/components/Jumbotron";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Star,
  Eye,
  Calendar,
  Search,
  BookOpen,
  Plus,
  ChevronLeft,
  ChevronRight,
  ExternalLink
} from 'lucide-react';
import { useOpenSourceStore } from '@/store/openSourceStore';
import { AddOpenSourceProjectDialog } from '@/components/open-source/AddOpenSourceProjectDialog';
import { CommentsPopover } from '@/components/open-source/CommentsPopover';
import { AdminRateDialog } from '@/components/open-source/AdminRateDialog';
import { cn } from '@/lib/utils';
import { ScrollArea } from "@/components/ui/scroll-area";
import { HackerNewsDialog } from '@/components/hacker-news/HackerNewsDialog';
import { OpenSourceDetailDialog } from '../components/open-source/OpenSourceDetailDialog';
import { useAuth } from '@/utils/AuthContext';
import type { OpenSourceProject, Discussion } from '@/store/openSourceStore';

export function OpenSourcePage() {
  const [searchTerm, setSearchTerm] = useState('');
  const {
    projects,
    loading,
    error,
    currentPage,
    totalPages,
    totalItems,
    fetchProjects,
    fetchDiscussions,
    setPage
  } = useOpenSourceStore();
  const { user } = useAuth();
  
  // 调试用户权限
  // console.log('Current user:', user);
  // console.log('Is admin:', user?.is_admin);
  
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [detailOpen, setDetailOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<OpenSourceProject | null>(null);
  const [selectedComments, setSelectedComments] = useState<Discussion[]>([]);
  const [rateDialogOpen, setRateDialogOpen] = useState(false);
  const [selectedRateProject, setSelectedRateProject] = useState<OpenSourceProject | null>(null);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // 在 OpenSourcePage.tsx 中，可以简化 filteredProjects 的逻辑
  const filteredProjects = useMemo(() => {
    if (!searchTerm) return projects;
    
    return projects.filter(project =>
      project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    // 移除排序逻辑，因为后端已经排序了
  }, [projects, searchTerm]);

  // Use filtered results for pagination when searching
  const displayTotalPages = searchTerm ? 1 : totalPages;
  const displayTotalItems = searchTerm ? filteredProjects.length : totalItems;

  // 优化：打开详情时自动拉取评论
  const handleOpenDetail = async (project: OpenSourceProject) => {
    setSelectedProject(project);
    setDetailOpen(true);
    await fetchDiscussions(project.id);
    // 拉取后从最新 projects 获取评论
    const updated = useOpenSourceStore.getState().projects.find(p => p.id === project.id);
    setSelectedComments(updated?.discussions || []);
  };

  // 处理管理员点击评分
  const handleRateClick = (project: OpenSourceProject, e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();
    
    // console.log('Rate click triggered', { user: user?.is_admin, project: project.name });
    
    if (user?.is_admin) {
      setSelectedRateProject(project);
      setRateDialogOpen(true);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="text-muted-foreground">加载中...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <span className="text-destructive">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <NaviBar />
      <div className="sticky top-0 z-30 bg-background/95 backdrop-blur">
        <Jumbotron
          title="开源项目推荐"
          subtitle="发现最新、最热门的开源项目"
        />
      </div>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row md:items-center md:gap-4 mb-6 space-y-4 md:space-y-0">
            <div className="relative flex-1 min-w-[220px]">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="搜索项目..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="default"
              className="bg-orange-300 hover:bg-orange-500 text-slate w-16 h-9 text-base font-semibold flex items-center justify-center gap-2 shadow-md"
              onClick={() => setIsAddDialogOpen(true)}
            >
              <Plus className="h-5 w-5 text-slate" />
              {/* 添加项目 */}
            </Button>
          </div>

          <Tabs defaultValue="grid" className="space-y-6">
            <TabsList>
              <TabsTrigger value="grid">网格视图</TabsTrigger>
              <TabsTrigger value="list">列表视图</TabsTrigger>
            </TabsList>

            <TabsContent value="grid" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProjects.map((project) => {
                  const tags = project.image_url?.split(',').map(tag => tag.trim()).filter(Boolean) ?? [];
                  return (
                    <Card
                      key={project.id}
                      className={cn(
                        "transition-shadow flex flex-col",
                        project.rate && project.rate > 4
                          ? "border border-orange-150 bg-orange-50 hover:shadow-lg"
                          : "hover:shadow-lg"
                      )}
                    >
                      <CardHeader>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-lg mb-2">
                              <a
                                href={project.github_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:text-primary transition-colors"
                              >
                                {project.name}
                              </a>
                            </CardTitle>
                            {/* 标签展示 */}
                            {tags.length > 0 && (
                              <div className="flex flex-wrap gap-2 mb-2">
                                {tags.map(tag => (
                                  <Badge
                                    key={tag}
                                    className="bg-blue-50 text-blue-700 border border-blue-200 px-2 py-0.5 text-xs cursor-pointer hover:bg-blue-100 hover:text-blue-900 transition"
                                    onClick={() => setSearchTerm(tag)}
                                  >
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                            <ScrollArea className="h-24 pr-4">
                              <div className="text-sm text-muted-foreground prose prose-sm dark:prose-invert">
                                <ReactMarkdown components={{ p: ({ children }) => <>{children}</> }}>
                                  {project.description}
                                </ReactMarkdown>
                              </div>
                            </ScrollArea>
                          </div>
                          <ExternalLink className="h-5 w-5 text-muted-foreground" />
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4 flex-grow flex flex-col justify-between">
                        <div>
                          <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <div 
                              className={cn(
                                "flex items-center gap-2",
                                user?.is_admin && "cursor-pointer hover:text-primary transition-colors"
                              )}
                              onClick={() => handleRateClick(project)}
                              title={user?.is_admin ? "点击设置评分" : undefined}
                            >
                              <Star className="h-4 w-4" />
                              <span>{project.rate ?? 0}</span>
                              {project.rate && project.rate > 4 && (
                                <Badge className="border-none bg-gradient-to-r from-amber-500 to-orange-500 text-white">
                                  <Star className="mr-1 h-3 w-3" />
                                  推荐
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              <span>{project.created_at ? project.created_at.slice(0, 10) : ''}</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center justify-end gap-2 pt-2">
                          <CommentsPopover projectId={project.id} discussions={project.discussions || []} />
                          <Button size="sm" variant="ghost" onClick={() => handleOpenDetail(project)}>
                            <Eye className="h-4 w-4 mr-1" />
                            查看
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="list" className="space-y-4">
              {filteredProjects.map((project) => {
                const tags = project.image_url?.split(',').map(tag => tag.trim()).filter(Boolean) ?? [];
                return (
                  <Card
                    key={project.id}
                    className={cn(
                      "transition-shadow",
                      project.rate && project.rate > 4
                        ? "border border-black-400 hover:shadow-md"
                        : "hover:shadow-md"
                    )}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold">
                              <a
                                href={project.github_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:text-primary transition-colors"
                              >
                                {project.name}
                              </a>
                            </h3>
                            <ExternalLink className="h-4 w-4 text-muted-foreground" />
                          </div>
                          {/* 标签展示 */}
                          {tags.length > 0 && (
                            <div className="flex flex-wrap gap-2 mb-2">
                              {tags.map(tag => (
                                <Badge
                                  key={tag}
                                  className="bg-blue-50 text-blue-700 border border-blue-200 px-2 py-0.5 text-xs cursor-pointer hover:bg-blue-100 hover:text-blue-900 transition"
                                  onClick={() => setSearchTerm(tag)}
                                >
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                          <ScrollArea className="h-20 pr-4">
                            <div className="text-sm text-muted-foreground mb-3 prose prose-sm dark:prose-invert">
                              <ReactMarkdown components={{ p: ({ children }) => <>{children}</> }}>
                                {project.description}
                              </ReactMarkdown>
                            </div>
                          </ScrollArea>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div 
                              className={cn(
                                "flex items-center gap-2",
                                user?.is_admin && "cursor-pointer hover:text-primary transition-colors"
                              )}
                              onClick={() => handleRateClick(project)}
                              title={user?.is_admin ? "点击设置评分" : undefined}
                            >
                              <Star className="h-4 w-4" />
                              <span>{project.rate ?? 0}</span>
                              {project.rate && project.rate > 4 && (
                                <Badge className="border-none bg-gradient-to-r from-amber-500 to-orange-500 text-white">
                                  <Star className="mr-1 h-3 w-3" />
                                  推荐
                                </Badge>
                              )}
                            </div>
                            <span>{project.created_at ? project.created_at.slice(0, 10) : ''}</span>
                          </div>
                        </div>
                        <div className="flex flex-col items-end justify-between self-stretch">
                          <Button size="sm" variant="ghost" onClick={() => handleOpenDetail(project)}>
                            <Eye className="h-4 w-4 mr-1" />
                            查看
                          </Button>
                          <CommentsPopover projectId={project.id} discussions={project.discussions || []} />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </TabsContent>
          </Tabs>

          {/* Pagination Controls */}
          {displayTotalPages > 1 && (
            <div className="flex items-center justify-between mt-8">
              <div className="text-sm text-muted-foreground">
                显示 {((currentPage - 1) * 6) + 1} - {Math.min(currentPage * 6, displayTotalItems)} 项，共 {displayTotalItems} 项
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  上一页
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, displayTotalPages) }, (_, i) => {
                    let pageNum;
                    if (displayTotalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= displayTotalPages - 2) {
                      pageNum = displayTotalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? "default" : "outline"}
                        size="sm"
                        className="w-8 h-8 p-0"
                        onClick={() => setPage(pageNum)}
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(currentPage + 1)}
                  disabled={currentPage === displayTotalPages}
                >
                  下一页
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">暂无项目</h3>
              <p className="text-muted-foreground">
                {searchTerm ? '没有找到匹配的项目' : '还没有添加任何开源项目'}
              </p>
            </div>
          )}
        </div>
      </div>
      <AddOpenSourceProjectDialog
        open={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        onSuccess={fetchProjects}
      />
      <HackerNewsDialog />
      <OpenSourceDetailDialog
        open={detailOpen}
        onOpenChange={setDetailOpen}
        project={selectedProject}
        comments={selectedComments}
      />
      {selectedRateProject && (
        <AdminRateDialog
          open={rateDialogOpen}
          onOpenChange={setRateDialogOpen}
          projectId={selectedRateProject.id}
          projectName={selectedRateProject.name}
          currentRate={selectedRateProject.rate ?? 0}
        />
      )}
    </div>
  );
}