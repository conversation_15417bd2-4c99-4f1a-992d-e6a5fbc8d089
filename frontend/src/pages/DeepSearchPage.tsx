import { useState, useEffect, useRef } from "react";
import { useStream } from "@langchain/langgraph-sdk/react";
import type { Message } from "@langchain/langgraph-sdk";
import { ProcessedEvent } from "@/components/deep-search/ActivityTimeline";
import { ChatMessagesView } from "@/components/deep-search/ChatMessagesView";
import { WelcomeScreen } from "@/components/deep-search/WelcomeScreen";
import { ActivityTimeline } from "@/components/deep-search/ActivityTimeline";
import { ResearchProgressIndicator } from "@/components/deep-search/ResearchProgressIndicator";
import { NaviBar } from "@/components/NaviBar";



export function DeepSearchPage() {
  const [processedEventsTimeline, setProcessedEventsTimeline] = useState<ProcessedEvent[]>([]);
  const [isSubmittingRequest, setIsSubmittingRequest] = useState(false);
  const [showProgressIndicator, setShowProgressIndicator] = useState(false);
  const [currentResearchStage, setCurrentResearchStage] = useState<string>('analysis');
  const lastSubmissionTime = useRef(0);

  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // 从 sessionStorage 获取初始查询，然后清除
  const [initialQuery, setInitialQuery] = useState<string | null>(null);

  // 通用的提交处理函数，包含防重复提交逻辑
  const handleSubmitWithProtection = (inputValue: string, effort: string, model: string, isNewConversation: boolean = false) => {
    // 输入验证
    if (!inputValue.trim()) {
      // console.log('输入内容不能为空');
      return;
    }

    // 防止快速重复提交（3秒内只能提交一次，因为Deep Research耗时长）
    const now = Date.now();
    if (now - lastSubmissionTime.current < 3000) {
      // console.log('提交过于频繁，请稍后再试');
      return;
    }

    // 防止在加载或已经在提交时重复提交
    if (thread.isLoading || isSubmittingRequest) {
      // console.log('请等待当前请求完成');
      return;
    }

    lastSubmissionTime.current = now;
    setIsSubmittingRequest(true);

    // 显示进度指示器
    setShowProgressIndicator(true);
    setCurrentResearchStage('analysis');

    try {
      // Clear timeline for new search
      if (isNewConversation) {
        setProcessedEventsTimeline([]);
      }

      const newMessages = isNewConversation
        ? [{
          type: "human" as const,
          content: inputValue,
          id: Date.now().toString(),
        }]
        : [
          ...(thread.messages || []),
          {
            type: "human" as const,
            content: inputValue,
            id: Date.now().toString(),
          },
        ];

      // Convert effort to search parameters
      let initial_search_query_count = 3;
      let max_research_loops = 3;
      switch (effort) {
        case "low":
          initial_search_query_count = 1;
          max_research_loops = 1;
          break;
        case "medium":
          initial_search_query_count = 3;
          max_research_loops = 3;
          break;
        case "high":
          initial_search_query_count = 5;
          max_research_loops = 10;
          break;
      }

      // 添加开始事件到时间线
      const startEvent: ProcessedEvent = {
        title: "开始研究",
        data: `查询: ${inputValue}`,
        nodeType: "main",
        status: "start",
        timestamp: Date.now(),
        depth: 0
      };

      setProcessedEventsTimeline((prevEvents) => [
        ...prevEvents,
        startEvent,
      ]);

      thread.submit({
        messages: newMessages,
        initial_search_query_count,
        max_research_loops,
        reasoning_model: model,
      });
    } catch (error) {
      console.error('提交请求时发生错误:', error);
      setIsSubmittingRequest(false);

      // 添加错误事件
      const errorEvent: ProcessedEvent = {
        title: "提交失败",
        data: error instanceof Error ? error.message : "提交请求时发生未知错误",
        nodeType: "main",
        status: "error",
        timestamp: Date.now(),
        depth: 0
      };

      setProcessedEventsTimeline((prevEvents) => [
        ...prevEvents,
        errorEvent,
      ]);
    } finally {
      // 延迟重置提交状态，给系统时间处理请求
      setTimeout(() => setIsSubmittingRequest(false), 2000);
    }
  };

  useEffect(() => {
    const storedQuery = sessionStorage.getItem('deepSearchQuery');
    if (storedQuery) {
      setInitialQuery(storedQuery);
      // 立即清除 sessionStorage，防止重复使用
      sessionStorage.removeItem('deepSearchQuery');
    }
  }, []);

  const thread = useStream<{
    messages: Message[];
    initial_search_query_count: number;
    max_research_loops: number;
    reasoning_model: string;
  }>({
    apiUrl: import.meta.env.DOCKER_ENV ? "http://deep_search:2024" : (import.meta.env.DEV ? "http://localhost:2024" : "http://localhost:8123"),
    assistantId: "deep_researcher",
    messagesKey: "messages",
    onUpdateEvent: (event: any) => {
      // 当收到第一个事件时，重置提交状态
      if (isSubmittingRequest) {
        setIsSubmittingRequest(false);
      }

      // 根据事件类型更新研究阶段
      if (event.write_research_brief) {
        setCurrentResearchStage('analysis');
      } else if (event.research_supervisor || event.supervisor) {
        setCurrentResearchStage('search');
      } else if (event.write_research_report) {
        setCurrentResearchStage('synthesis');
        // 当开始输出 final report 时，隐藏进度条
        setTimeout(() => {
          setShowProgressIndicator(false);
        }, 1000);
      }

      let processedEvent: ProcessedEvent | null = null;
      // console.log('Received event:', event);  // 调试用
      // console.log('Event keys:', Object.keys(event)); // 调试事件的键

      // 辅助函数：检测是否为研究相关的活动
      const detectResearchActivity = (eventData: any): ProcessedEvent | null => {
        const eventStr = JSON.stringify(eventData).toLowerCase();

        // 检测supervisor的思考过程
        if (eventData.supervisor && eventData.supervisor.messages) {
          const messages = eventData.supervisor.messages;
          const lastMessage = messages[messages.length - 1];

          if (lastMessage && lastMessage.content) {
            const content = lastMessage.content.toLowerCase();

            // 检测研究规划阶段
            if (content.includes('plan') || content.includes('strategy') || content.includes('approach')) {
              return {
                title: "制定研究策略",
                data: "分析研究需求并制定执行策略",
                nodeType: "supervisor",
                status: "running",
                timestamp: Date.now(),
                depth: 1,
                subgraphType: "supervisor",
                parentNode: "research_supervisor"
              };
            }

            // 检测任务分解
            if (content.includes('break') || content.includes('divide') || content.includes('subtask')) {
              return {
                title: "分解研究任务",
                data: "将复杂研究任务分解为可执行的子任务",
                nodeType: "supervisor",
                status: "running",
                timestamp: Date.now(),
                depth: 1,
                subgraphType: "supervisor",
                parentNode: "research_supervisor"
              };
            }
          }
        }

        // 检测工具调用相关的活动
        if (eventStr.includes('tool_call') || eventStr.includes('function_call')) {
          return {
            title: "执行工具调用",
            data: "调用专业工具执行特定任务",
            nodeType: "supervisor",
            status: "running",
            timestamp: Date.now(),
            depth: 1,
            subgraphType: "supervisor",
            parentNode: "research_supervisor"
          };
        }

        return null;
      };

      // 主工作流节点
      if (event.clarify_with_user) {
        processedEvent = {
          title: "用户需求澄清",
          data: event.clarify_with_user.result || "正在与用户交互以明确需求",
          nodeType: "main",
          status: event.clarify_with_user.status || "start",
          timestamp: Date.now(),
          depth: 0
        };
      } else if (event.write_research_brief) {
        processedEvent = {
          title: "研究计划制定",
          data: event.write_research_brief.brief || "制定研究计划和策略",
          nodeType: "main",
          status: event.write_research_brief.status || "start",
          timestamp: Date.now(),
          depth: 0
        };
      } else if (event.research_supervisor) {
        // supervisor子图的主节点 - 解析详细的执行过程
        // console.log('Research supervisor event detected:', event.research_supervisor);

        const supervisorData = event.research_supervisor;

        // 主要的研究监督事件
        processedEvent = {
          title: "研究执行监督",
          data: "研究监督系统已启动并完成执行",
          nodeType: "supervisor",
          status: "complete",
          timestamp: Date.now(),
          depth: 0,
          subgraphType: "supervisor"
        };

        // 解析并添加详细的子事件
        setTimeout(() => {
          const detailedEvents: ProcessedEvent[] = [];

          // 1. 解析supervisor_messages中的工具调用和思考过程
          if (supervisorData.supervisor_messages && Array.isArray(supervisorData.supervisor_messages)) {
            supervisorData.supervisor_messages.forEach((msg: any, index: number) => {
              if (msg.type === 'ai' && msg.tool_calls) {
                msg.tool_calls.forEach((toolCall: any) => {
                  if (toolCall.name === 'think_tool') {
                    detailedEvents.push({
                      title: "策略思考",
                      data: `反思: ${toolCall.args?.reflection?.substring(0, 150) || '分析研究策略'}...`,
                      nodeType: "supervisor",
                      status: "complete",
                      timestamp: Date.now() + index * 100,
                      depth: 1,
                      subgraphType: "supervisor",
                      parentNode: "research_supervisor"
                    });
                  } else if (toolCall.name === 'ConductResearch') {
                    detailedEvents.push({
                      title: "启动研究任务",
                      data: `研究主题: ${toolCall.args?.research_topic || '未知主题'}`,
                      nodeType: "researcher",
                      status: "start",
                      timestamp: Date.now() + index * 100 + 50,
                      depth: 1,
                      subgraphType: "researcher",
                      parentNode: "research_supervisor"
                    });
                  }
                });
              } else if (msg.type === 'tool' && msg.name === 'ConductResearch') {
                // 工具调用结果
                detailedEvents.push({
                  title: "研究任务完成",
                  data: `研究结果: ${msg.content?.substring(0, 200) || '研究已完成'}...`,
                  nodeType: "researcher",
                  status: "complete",
                  timestamp: Date.now() + index * 100 + 75,
                  depth: 1,
                  subgraphType: "researcher",
                  parentNode: "research_supervisor"
                });
              }
            });
          }

          // 2. 解析raw_notes中的具体研究活动
          if (supervisorData.raw_notes && Array.isArray(supervisorData.raw_notes)) {
            supervisorData.raw_notes.forEach((note: string, index: number) => {
              // 解析arXiv搜索结果
              if (note.includes('arXiv search results')) {
                const queries = note.match(/=== QUERY \d+: ([^=]+)/g);
                queries?.forEach((query, qIndex) => {
                  const queryText = query.replace(/=== QUERY \d+: /, '').trim();
                  detailedEvents.push({
                    title: "执行arXiv搜索",
                    data: `搜索查询: ${queryText}`,
                    nodeType: "researcher",
                    status: "running",
                    timestamp: Date.now() + (index * 1000) + (qIndex * 200),
                    depth: 2,
                    subgraphType: "researcher",
                    parentNode: "research_supervisor"
                  });
                });

                // 统计搜索结果
                const resultCount = (note.match(/Title:/g) || []).length;
                detailedEvents.push({
                  title: "收集研究资料",
                  data: `从arXiv收集到 ${resultCount} 篇相关论文`,
                  nodeType: "researcher",
                  status: "complete",
                  timestamp: Date.now() + (index * 1000) + 500,
                  depth: 2,
                  subgraphType: "researcher",
                  parentNode: "research_supervisor"
                });
              }
            });
          }

          // 3. 解析notes中的反思和总结
          if (supervisorData.notes && Array.isArray(supervisorData.notes)) {
            supervisorData.notes.forEach((note: string, index: number) => {
              if (note.includes('Reflection recorded:')) {
                const reflection = note.replace('Reflection recorded: ', '');
                detailedEvents.push({
                  title: "研究反思",
                  data: `${reflection.substring(0, 150)}...`,
                  nodeType: "supervisor",
                  status: "complete",
                  timestamp: Date.now() + 2000 + (index * 300),
                  depth: 1,
                  subgraphType: "supervisor",
                  parentNode: "research_supervisor"
                });
              } else if (note.includes('**List of Queries and Tool Calls Made**')) {
                detailedEvents.push({
                  title: "工具调用总结",
                  data: `${note.substring(0, 200)}...`,
                  nodeType: "researcher",
                  status: "complete",
                  timestamp: Date.now() + 2000 + (index * 300),
                  depth: 2,
                  subgraphType: "researcher",
                  parentNode: "research_supervisor"
                });
              }
            });
          }

          // 按时间戳排序并添加到时间线
          detailedEvents.sort((a, b) => a.timestamp - b.timestamp);

          detailedEvents.forEach((event, index) => {
            setTimeout(() => {
              setProcessedEventsTimeline((prevEvents) => [
                ...prevEvents,
                event
              ]);
            }, index * 100); // 每100ms添加一个事件，创建流畅的动画效果
          });

        }, 500); // 延迟500ms开始解析，让主事件先显示
      } else if (event.supervisor) {
        // supervisor子图内的supervisor节点
        const action = event.supervisor.action || "执行监督决策";

        // 检查消息中是否包含工具调用结果（表示研究完成）
        const hasToolResults = event.supervisor.messages &&
          event.supervisor.messages.some((msg: any) =>
            msg.type === "tool" && msg.name === "ConductResearch"
          );

        // 检查是否包含研究结果，如果是则显示为研究完成
        if (hasToolResults || action.includes("研究结果") || action.includes("research results") ||
          event.supervisor.research_results || event.supervisor.completed_research) {

          processedEvent = {
            title: "研究任务完成",
            data: "研究子任务已完成，正在整合结果",
            nodeType: "researcher",
            status: "complete",
            timestamp: Date.now(),
            depth: 1,
            subgraphType: "researcher",
            parentNode: "research_supervisor"
          };
        } else {
          // 检查是否是思考工具调用
          const isThinking = event.supervisor.messages &&
            event.supervisor.messages.some((msg: any) =>
              msg.tool_calls && msg.tool_calls.some((call: any) => call.name === "think_tool")
            );

          if (isThinking) {
            processedEvent = {
              title: "策略思考",
              data: "分析当前进度并制定下一步策略",
              nodeType: "supervisor",
              status: "running",
              timestamp: Date.now(),
              depth: 1,
              subgraphType: "supervisor",
              parentNode: "research_supervisor"
            };
          } else {
            processedEvent = {
              title: "监督者决策",
              data: action,
              nodeType: "supervisor",
              status: event.supervisor.status || "running",
              timestamp: Date.now(),
              depth: 1,
              subgraphType: "supervisor",
              parentNode: "research_supervisor"
            };
          }
        }
      } else if (event.supervisor_tools) {
        // supervisor子图内的工具节点
        const toolName = event.supervisor_tools.tool_name || "使用监督工具";

        // 如果是conduct_research工具调用，显示为研究任务启动
        if (toolName === "conduct_research" || event.supervisor_tools.tool_name === "conduct_research" ||
          (event.supervisor_tools.args && event.supervisor_tools.args.research_topic)) {
          const researchTopic = event.supervisor_tools.args?.research_topic ||
            event.supervisor_tools.research_topic ||
            "未知主题";

          processedEvent = {
            title: "启动研究任务",
            data: `开始研究: ${researchTopic}`,
            nodeType: "researcher",
            status: "start",
            timestamp: Date.now(),
            depth: 1,
            subgraphType: "researcher",
            parentNode: "research_supervisor"
          };
        } else {
          processedEvent = {
            title: "监督工具使用",
            data: toolName,
            nodeType: "supervisor",
            status: event.supervisor_tools.status || "start",
            timestamp: Date.now(),
            depth: 1,
            subgraphType: "supervisor",
            parentNode: "research_supervisor"
          };
        }
      } else if (event.researcher) {
        // researcher子图的researcher节点
        processedEvent = {
          title: "研究执行",
          data: event.researcher.current_task || "执行研究任务",
          nodeType: "researcher",
          status: event.researcher.status || "start",
          timestamp: Date.now(),
          depth: 1,
          subgraphType: "researcher",
          parentNode: "research_supervisor"
        };
      } else if (event.researcher_tools) {
        // researcher子图内的工具节点
        processedEvent = {
          title: "研究工具使用",
          data: event.researcher_tools.tool_name || "使用研究工具",
          nodeType: "researcher",
          status: event.researcher_tools.status || "start",
          timestamp: Date.now(),
          depth: 1,
          subgraphType: "researcher",
          parentNode: "research_supervisor"
        };
      } else if (event.compress_research) {
        // researcher子图内的压缩节点
        processedEvent = {
          title: "研究结果压缩",
          data: event.compress_research.summary || "压缩和整理研究结果",
          nodeType: "researcher",
          status: event.compress_research.status || "start",
          timestamp: Date.now(),
          depth: 1,
          subgraphType: "researcher",
          parentNode: "research_supervisor"
        };
      } else if (event.final_report_generation) {
        processedEvent = {
          title: "最终报告生成",
          data: "生成最终研究报告",
          nodeType: "main",
          status: event.final_report_generation.status || "start",
          timestamp: Date.now(),
          depth: 0
        };
      }

      // 保留原有的事件处理，但添加新的属性
      else if (event.generate_query) {
        processedEvent = {
          title: "生成搜索查询",
          data: event.generate_query?.search_query?.join(", ") || "",
          nodeType: "researcher",
          status: "start",
          timestamp: Date.now(),
          depth: 1,
          subgraphType: "researcher",
          parentNode: "research_supervisor"
        };
      } else if (event.web_research) {
        const sources = event.web_research.sources_gathered || [];
        const numSources = sources.length;
        const uniqueLabels = [
          ...new Set(sources.map((s: any) => s.label).filter(Boolean)),
        ];
        const exampleLabels = uniqueLabels.slice(0, 3).join(", ");
        processedEvent = {
          title: "网络研究",
          data: `收集了 ${numSources} 个来源。相关主题: ${exampleLabels || "N/A"}`,
          nodeType: "researcher",
          status: "complete",
          timestamp: Date.now(),
          depth: 1,
          subgraphType: "researcher",
          parentNode: "research_supervisor"
        };
      } else if (event.reflection) {
        processedEvent = {
          title: "研究反思",
          data: "分析研究结果",
          nodeType: "researcher",
          status: "start",
          timestamp: Date.now(),
          depth: 1,
          subgraphType: "researcher",
          parentNode: "research_supervisor"
        };
      }

      // 如果没有匹配到具体事件，尝试检测研究活动
      if (!processedEvent) {
        processedEvent = detectResearchActivity(event);
      }

      // 如果仍然没有匹配到事件，记录未处理的事件
      if (!processedEvent) {
        // console.log('Unhandled event:', event);

        // 尝试从事件中提取任何可能的信息
        const eventKeys = Object.keys(event);
        if (eventKeys.length > 0) {
          const firstKey = eventKeys[0];
          const eventData = event[firstKey];

          // 检查是否是错误事件
          if (firstKey === 'error' || (eventData && eventData.error)) {
            processedEvent = {
              title: `执行错误`,
              data: `错误信息: ${JSON.stringify(eventData).substring(0, 200)}`,
              nodeType: "main",
              status: "error",
              timestamp: Date.now(),
              depth: 0
            };
          } else {
            processedEvent = {
              title: `调试事件: ${firstKey}`,
              data: JSON.stringify(eventData).substring(0, 100) + "...",
              nodeType: "supervisor",
              status: "start",
              timestamp: Date.now(),
              depth: 1,
              subgraphType: "supervisor",
              parentNode: "research_supervisor"
            };
          }
        }
      }

      if (processedEvent) {
        setProcessedEventsTimeline((prevEvents) => [
          ...prevEvents,
          processedEvent!,
        ]);
      }
    },
    onError: (error: any) => {
      console.error('Stream error:', error);
      // 发生错误时重置提交状态
      setIsSubmittingRequest(false);
      // 隐藏进度指示器
      setShowProgressIndicator(false);

      // 添加错误事件到时间线
      const errorEvent: ProcessedEvent = {
        title: "错误",
        data: error.message || "发生未知错误",
        nodeType: "main",
        status: "error",
        timestamp: Date.now(),
        depth: 0
      };

      setProcessedEventsTimeline((prevEvents) => [
        ...prevEvents,
        errorEvent,
      ]);
    },
  });

  // 监听研究完成状态
  useEffect(() => {
    if (!thread.isLoading && !isSubmittingRequest && showProgressIndicator) {
      // 延迟隐藏进度指示器，让用户看到完成状态
      const timer = setTimeout(() => {
        setShowProgressIndicator(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [thread.isLoading, isSubmittingRequest, showProgressIndicator]);

  // 如果有初始查询，自动开始搜索
  useEffect(() => {
    if (initialQuery && !thread.messages?.length && !thread.isLoading && !isSubmittingRequest) {
      handleSubmitWithProtection(initialQuery, "medium", "qwen3-30b-a3b-instruct-2507", true);
      // 提交后清除 initialQuery，防止重复提交
      setInitialQuery(null);
    }
  }, [initialQuery, thread.messages?.length, thread.isLoading, isSubmittingRequest]);

  return (
    <div className="h-screen flex flex-col bg-neutral-900">
      <NaviBar />

      {/* Fixed Jumbotron */}
      {/* <div className="sticky top-0 z-30 bg-neutral-900/95 backdrop-blur">
        <Jumbotron
          title="Deep Research"
          subtitle="AI-powered comprehensive research and analysis"
          bgClassName="dark:bg-neutral-900"
        />
      </div> */}

      {/* 研究进度指示器 */}
      <ResearchProgressIndicator
        isVisible={showProgressIndicator}
        currentStage={currentResearchStage}
      />

      <div className="flex-1 flex overflow-hidden">
        {/* 左侧主要内容区域 */}
        <div className="flex-1 flex flex-col bg-neutral-900 relative overflow-hidden mt-10">
          {(!thread.messages || thread.messages.length === 0) && !initialQuery ? (
            <WelcomeScreen
              handleSubmit={(inputValue, effort, model) => {
                handleSubmitWithProtection(inputValue, effort, model, true);
              }}
              onCancel={() => thread.stop()}
              isLoading={thread.isLoading || isSubmittingRequest}
            />
          ) : (
            <ChatMessagesView
              messages={thread.messages || []}
              isLoading={thread.isLoading || isSubmittingRequest}
              scrollAreaRef={scrollAreaRef}
              onSubmit={(inputValue, effort, model) => {
                handleSubmitWithProtection(inputValue, effort, model, false);
              }}
              onCancel={() => thread.stop()}
            />
          )}
        </div>

        {/* 右侧活动时间线面板 - 仅在有活动信息时显示 */}
        {processedEventsTimeline.length > 0 && (
          <div className="w-80 bg-neutral-800 border-l border-neutral-700 flex flex-col overflow-hidden">
            <ActivityTimeline
              processedEvents={processedEventsTimeline}
              isLoading={thread.isLoading}
            />
          </div>
        )}
      </div>
    </div>
  );
}