import { SettingContent } from "@/components/setting/SettingContent";
import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/utils/AuthContext";
import { toast } from "sonner";
import { Logo } from "@/components/Logo"; // <-- 添加 Logo 组件的导入

export function SettingsPage() {
  const [, setLocation] = useLocation();
  // 使用 AuthContext 替代 mockAuth
  const { user, isAuthenticated } = useAuth();
  const isAdmin = user?.role === 'admin';

  const [activeTab, setActiveTab] = useState("Dashboard");

  // 定义需要管理员权限的页面
  const adminOnlyTabs = ["用户管理", "团队管理", "板块管理"];

  useEffect(() => {
    if (!isAuthenticated) {
      toast.error("未登录", {
        description: "请先登录后再访问该页面"
      });
      setLocation("/login");
      return;
    }

    // 只在访问管理员页面时检查权限
    if (adminOnlyTabs.includes(activeTab) && !isAdmin) {
      toast.error("无权限", {
        description: "您没有权限访问该页面"
      });
      setActiveTab("Dashboard");
    }
  }, [isAuthenticated, isAdmin, activeTab, setLocation]);

  // 处理标签页切换
  const handleTabChange = (tab: string) => {
    if (adminOnlyTabs.includes(tab) && !isAdmin) {
      toast.error("无权限", {
        description: "您没有权限访问该页面"
      });
      return;
    }
    setActiveTab(tab);
  };
  return (
    <div className="container mx-auto py-8">
      {/* main content */}
      <div>
        <SettingContent activeTab={activeTab} setActiveTab={setActiveTab} />
      </div>

      {/* setting navbar */}
      <div className="sticky top-0 inset-x-0 z-20 bg-white border-y px-4 sm:px-6 lg:px-8 lg:hidden dark:bg-neutral-800 dark:border-neutral-700">
        <div className="flex items-center py-2">
          <button
            type="button"
            className="size-8 flex justify-center items-center gap-x-2 border border-gray-200 text-gray-800 hover:text-gray-500 rounded-lg focus:outline-none focus:text-gray-500 disabled:opacity-50 disabled:pointer-events-none dark:border-neutral-700 dark:text-neutral-200 dark:hover:text-neutral-500 dark:focus:text-neutral-500"
            aria-haspopup="dialog"
            aria-expanded="false"
            aria-controls="hs-application-sidebar"
            aria-label="Toggle navigation"
            data-hs-overlay="#hs-application-sidebar"
          >
            <span className="sr-only">Toggle Navigation</span>
            <svg
              className="shrink-0 size-8"
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect width="18" height="18" x="3" y="3" rx="2" />
              <path d="M15 3v18" />
              <path d="m8 9 3 3-3 3" />
            </svg>
          </button>
          <ol className="ms-3 flex items-center whitespace-nowrap">
            <li className="flex items-center text-sm text-gray-800 dark:text-neutral-400">
              Application Layout
              <svg
                className="shrink-0 mx-3 overflow-visible size-2.5 text-gray-400 dark:text-neutral-500"
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 1L10.6869 7.16086C10.8637 7.35239 10.8637 7.64761 10.6869 7.83914L5 14"
                  stroke="currentColor"
                  stroke-width="2"
                  strokeLinecap="round"
                />
              </svg>
            </li>
            <li
              className="text-sm font-semibold text-gray-800 truncate dark:text-neutral-400"
              aria-current="page"
            >
              Dashboard
            </li>
          </ol>
        </div>
      </div>
      <div
        id="hs-application-sidebar"
        className="hs-overlay  [--auto-close:lg]
                    hs-overlay-open:translate-x-0
                    -translate-x-full transition-all duration-300 transform
                    w-[260px] h-full
                    hidden
                    fixed inset-y-0 start-0 z-[60]
                    bg-white border-e border-gray-200
                    lg:block lg:translate-x-0 lg:end-auto lg:bottom-0
                    dark:bg-neutral-800 dark:border-neutral-700"
        role="dialog"
        tabIndex={-1}
        aria-label="Sidebar"
      >
        <div className="relative flex flex-col h-full max-h-full">
          <div className="px-6 pt-4 flex items-center">
            <button
              className="flex-none rounded-xl text-xl inline-block font-semibold focus:outline-none focus:opacity-80"
              onClick={() => {
                setLocation("/");
                window.location.reload();
              }}
              aria-label="3Stooges"
            >
              <Logo className="w-32 rounded-lg" /> {/* <-- 使用 Logo 组件 */}
            </button>
            <div className="hidden lg:block ms-2"></div>
          </div>

          <div className="h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
            <nav
              className="hs-accordion-group p-3 w-full flex flex-col flex-wrap"
              data-hs-accordion-always-open
            >
              <ul className="flex flex-col space-y-1">
                <li>
                  <a
                    onClick={() => setActiveTab("Dashboard")}
                    className={`flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg hover:bg-gray-100 focus:outline-none focus:bg-gray-100 ${activeTab === "Dashboard"
                        ? "bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white"
                        : "text-gray-800 dark:text-neutral-200 dark:hover:bg-neutral-900 dark:hover:text-neutral-300"
                      }`}
                    href="#"
                  >
                    <svg
                      className="shrink-0 size-4"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
                      <polyline points="9 22 9 12 15 12 15 22" />
                    </svg>
                    个人统计
                  </a>
                </li>

                {/* <li className="hs-accordion" id="users-accordion">
                  <a
                    onClick={() => handleTabChange("用户管理")}
                    className={`w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm text-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-900 dark:text-neutral-200 dark:hover:text-neutral-300 ${!isAdmin ? 'opacity-50 cursor-not-allowed' : ''}`}
                    href="#"
                  >
                    <svg
                      className="shrink-0 mt-0.5 size-4"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <circle cx="18" cy="15" r="3" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M10 15H6a4 4 0 0 0-4 4v2" />
                      <path d="m21.7 16.4-.9-.3" />
                      <path d="m15.2 13.9-.9-.3" />
                      <path d="m16.6 18.7.3-.9" />
                      <path d="m19.1 12.2.3-.9" />
                      <path d="m19.6 18.7-.4-1" />
                      <path d="m16.8 12.3-.4-1" />
                      <path d="m14.3 16.6 1-.4" />
                      <path d="m20.7 13.8 1-.4" />
                    </svg>
                    用户管理
                  </a>
                </li> */}

                <li className="hs-accordion" id="account-accordion">
                  <a
                    onClick={() => setActiveTab("团队管理")}
                    className={`w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg hover:bg-gray-100 ${activeTab === "团队管理"
                        ? "bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white"
                        : "text-gray-800 dark:text-neutral-200 dark:hover:bg-neutral-900 dark:hover:text-neutral-300"
                      }`}
                    href="#"
                  >
                    <svg
                      className="shrink-0 size-4"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                    团队管理
                  </a>
                </li>

                <li className="hs-accordion" id="channel-management">
                  <a
                    onClick={() => handleTabChange("板块管理")}
                    className={`w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg hover:bg-gray-100 ${activeTab === "板块管理"
                        ? "bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white"
                        : "text-gray-800 dark:text-neutral-200 dark:hover:bg-neutral-900 dark:hover:text-neutral-300"
                      } ${!isAdmin ? 'opacity-50 cursor-not-allowed' : ''}`}
                    href="#"
                  >
                    <svg
                      className="shrink-0 size-4"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M3 7V5a2 2 0 0 1 2-2h2" />
                      <path d="M17 3h2a2 2 0 0 1 2 2v2" />
                      <path d="M21 17v2a2 2 0 0 1-2 2h-2" />
                      <path d="M7 21H5a2 2 0 0 1-2-2v-2" />
                      <rect width="7" height="5" x="7" y="7" rx="1" />
                      <rect width="7" height="5" x="10" y="12" rx="1" />
                    </svg>
                    板块管理
                  </a>
                </li>

                {/* <li className="hs-accordion" id="storage-settings">
                  <a
                    onClick={() => setActiveTab("存储设置")}
                    className="w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm text-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-900 dark:text-neutral-200 dark:hover:text-neutral-300"
                    href="#"
                  >
                    <svg
                      className="shrink-0 size-4"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M2 20h20" />
                      <path d="M5 20a2 2 0 0 1-2-2V8h18v10a2 2 0 0 1-2 2" />
                      <path d="M6 8V6a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v2" />
                      <path d="M15 13v-3" />
                    </svg>
                    存储设置
                  </a>
                </li> */}

                <li className="hs-accordion" id="search-engine">
                  <a
                    onClick={() => setActiveTab("搜索引擎")}
                    className={`w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg hover:bg-gray-100 ${activeTab === "搜索引擎"
                        ? "bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white"
                        : "text-gray-800 dark:text-neutral-200 dark:hover:bg-neutral-900 dark:hover:text-neutral-300"
                      }`}
                    href="#"
                  >
                    <svg
                      className="shrink-0 size-4"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <circle cx="11" cy="11" r="8" />
                      <path d="m21 21-4.3-4.3" />
                    </svg>
                    搜索引擎
                  </a>
                </li>

                <li className="hs-accordion" id="projects-accordion">
                  <a
                    onClick={() => setActiveTab("模型服务")}
                    className={`w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg hover:bg-gray-100 ${activeTab === "模型服务"
                        ? "bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white"
                        : "text-gray-800 dark:text-neutral-200 dark:hover:bg-neutral-900 dark:hover:text-neutral-300"
                      }`}
                    href="#"
                  >
                    <svg
                      className="shrink-0 size-4 mt-1 text-gray-800 dark:text-neutral-200"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect width="7" height="7" x="14" y="3" rx="1" />
                      <path d="M10 21V8a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1H3" />
                    </svg>
                    模型服务
                  </a>
                </li>

                <li>
                  <a
                    onClick={() => setActiveTab("Agents")}
                    className={`w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg hover:bg-gray-100 ${activeTab === "Agents"
                        ? "bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white"
                        : "text-gray-800 dark:text-neutral-200 dark:hover:bg-neutral-900 dark:hover:text-neutral-300"
                      }`}
                    href="#"
                  >
                    <svg
                      className="shrink-0 size-4 mt-1 text-gray-800 dark:text-neutral-200"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <circle cx="12" cy="12" r="4" />
                      <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8" />
                    </svg>
                    智能代理
                  </a>
                </li>

                <li>
                  <a
                    onClick={() => setActiveTab("About")}
                    className={`w-full flex items-center gap-x-3.5 py-2 px-2.5 text-sm rounded-lg hover:bg-gray-100 ${activeTab === "About"
                        ? "bg-gray-100 text-gray-800 dark:bg-neutral-700 dark:text-white"
                        : "text-gray-800 dark:text-neutral-200 dark:hover:bg-neutral-900 dark:hover:text-neutral-300"
                      }`}
                    href="#"
                  >
                    <svg
                      className="shrink-0 size-4"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
                      <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
                    </svg>
                    关于
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
      <div className="w-full pt-10 px-4 sm:px-6 md:px-8 lg:ps-72"></div>
    </div>
  );
}
