import axios from 'axios';

// 使用Jina AI Reader解析网页内容
export async function parseWithJinaReader(url: string): Promise<any> {
    try {
        console.log(`正在使用Jina AI Reader解析网页: ${url}`);

        // 确保URL是完整的URL
        const fullUrl = url.startsWith('http') ? url : `https://${url}`;

        // 使用Jina AI Reader API解析网页内容
        console.log('调用Jina AI Reader API...');

        // 从环境变量获取API密钥，或者使用配置的密钥
        // 注意：实际使用时应该将API密钥存储在环境变量或安全的配置中
        const JINA_API_KEY = "jina_7a8f45255f9341cebefc877866111655YMgQtBlVbq0h6hQOU-XJGDPkbIeg" //import.meta.env.VITE_JINA_API_KEY;

        if (!JINA_API_KEY) {
            console.warn('未找到Jina API密钥，将回退到直接获取HTML内容');
            const response = await axios.get(fullUrl);
            return {
                content: response.data,
                url: fullUrl
            };
        }

        // 调用Jina Reader API - 使用官方推荐的格式
        const response = await axios.get(
            `https://r.jina.ai/${fullUrl}`,
            {
                headers: {
                    'Authorization': `Bearer ${JINA_API_KEY}`
                }
            }
        );

        console.log('Jina AI Reader解析成功');

        // 返回解析后的内容
        return {
            content: response.data,
            url: fullUrl
        };
    } catch (error) {
        console.error('Jina AI Reader解析失败:', error);

        // 如果API调用失败，尝试直接获取HTML内容作为备选方案
        console.log('尝试直接获取HTML内容作为备选方案...');
        try {
            const fullUrl = url.startsWith('http') ? url : `https://${url}`;
            const response = await axios.get(fullUrl);
            console.log('网页内容获取成功', response.status);
            return {
                content: response.data,
                url: fullUrl
            };
        } catch (fallbackError) {
            console.error('备选方案也失败:', fallbackError);
            throw error; // 抛出原始错误
        }
    }
}