import { MERMAID_EXPERT_PROMPT } from "./mermaid";
import { PLANTUML_EXPERT_PROMPT } from "./plantuml";
import { DRAWIO_EXPERT_PROMPT } from "./drawio";
import { DEEP_EXPERT_PROMPT } from "./deep";
import { ANTHROPIC_EXPERT_PROMPT } from "./anthropic";
import { DIGITAL_EXPERT_PROMPT } from "./digital";
import { STOOGES_EXPERT_PROMPT } from "./3stooges";
import { PROMPTOPT_EXPERT_PROMPT } from "./promptopt";

interface SystemPrompt {
  role: string;
  content: string;
}

export const systemPrompts = {
  default: {
    role: 'system',
    content: '你是一位专业的学术研究助手。请简洁、准确地回答问题，避免不必要的修饰。'
  },
  mermaid: {
    role: 'system',
    content: MERMAID_EXPERT_PROMPT
  },
  plantuml: {
    role: 'system',
    content: PLANTUML_EXPERT_PROMPT
  },
  drawio: {
    role: 'system',
    content: DRAWIO_EXPERT_PROMPT
  },
  deep: {
    role: 'system',
    content: DEEP_EXPERT_PROMPT
  },
  anthropic: {
    role: 'system',
    content: ANTHROPIC_EXPERT_PROMPT
  },
  digital: {
    role: 'system',
    content: DIGITAL_EXPERT_PROMPT
  },
  stooges: {
    role:'system',
    content: STOOGES_EXPERT_PROMPT
  },
  promptopt: {
    role: 'system',
    content: PROMPTOPT_EXPERT_PROMPT
  }
} as const;

export type SystemPromptKey = keyof typeof systemPrompts;