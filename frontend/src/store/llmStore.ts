import { create } from 'zustand'
import API from '@/config/api'


interface Model {
  id: string;
  name: string;
  contextWindow?: number;
}

// 添加数据库模型接口
interface DBModel {
  name: string;
  model_type: string;
  model_name: string;
  provider: string;
  credential: string;
  base_url: string;
  id: string;
  user_id: string;
  create_time: string;
  update_time: string;
}

interface LLMState {
  providers: Provider[];
  defaultModels: Provider[]; // 添加默认模型列表
  selectedModel: {
    id: string;
    providerName: string;
    baseUrl: string;
    isDefault?: boolean; // 标识是否为默认模型
  } | null;
  selectedProvider: Provider | null;
  loading: boolean;
  error: string | null;
  lastFetchTime: number; // 添加上次获取数据的时间戳
  setSelectedProvider: (provider: Provider) => void;
  toggleProvider: (id: string) => void;
  updateProvider: (id: string, updates: Partial<Provider>) => Promise<void>;
  addProvider: (provider: Partial<Provider>) => Promise<void>;
  setSelectedModel: (modelId: string | null, isDefault?: boolean) => void;
  fetchProviders: () => Promise<void>;
  deleteProvider: (providerId: string) => Promise<boolean>;
}

export interface LLMProvider {
  id: string;
  name: string;
  type: string;
  enabled: boolean;
  baseUrl: string;
  models: Model[];
  color: string;
  icon: string;
}

const getColorForProvider = (provider: string): string => {
  const providerColors: Record<string, string> = {
    'openai': 'bg-green-500',
    'deepseek': 'bg-blue-500',
    'qwen': 'bg-black',
    'default': 'bg-purple-500'
  };

  return providerColors[provider.toLowerCase()] || providerColors.default;
};

const groupModelsByProvider = (dbModels: DBModel[]): { userProviders: Provider[], defaultProviders: Provider[] } => {
  // 按提供商字段分组 - 使用数据库中的provider字段
  const groupedModels: Record<string, DBModel[]> = {};

  dbModels.forEach(model => {
    // 使用provider字段作为分组依据
    const providerName = model.provider;

    if (!groupedModels[providerName]) {
      groupedModels[providerName] = [];
    }
    groupedModels[providerName].push(model);
  });

  // 转换为Provider数组，并分离默认模型和用户模型
  const allProviders = Object.entries(groupedModels).map(([providerName, models]) => {
    const firstModel = models[0];
    // 修复：根据provider字段判断是否为默认模型
    const isDefault = firstModel.provider === 'default';
    
    return {
      id: firstModel.id,
      name: isDefault ? '系统默认' : providerName,
      type: isDefault ? 'default' as const : firstModel.model_type as 'openai' | 'deepseek' | 'qwen',
      enabled: true,
      baseUrl: firstModel.base_url,
      apiKey: firstModel.credential,
      models: models.map(m => ({
        id: m.model_name,
        name: m.model_name
      })),
      color: isDefault ? 'bg-gradient-to-r from-blue-500 to-purple-500' : getColorForProvider(firstModel.provider),
      icon: isDefault ? '🤖' : firstModel.provider.charAt(0).toUpperCase(),
      isDefault,
      readonly: isDefault
    };
  });

  // 分离默认模型和用户模型
  const defaultProviders = allProviders.filter(p => p.isDefault);
  const userProviders = allProviders.filter(p => !p.isDefault);

  // 添加调试日志
  // console.log('默认提供商:', defaultProviders);
  // console.log('用户提供商:', userProviders);

  return { userProviders, defaultProviders };
};

export const useLLMStore = create<LLMState>()((set, get) => ({
  providers: [],
  defaultModels: [],
  selectedProvider: null,
  selectedModel: null,
  loading: false,
  error: null,
  lastFetchTime: 0,

  deleteProvider: async (providerId: string) => {
    try {
      set({ loading: true, error: null });

      const state = get();
      // 检查用户模型中是否存在该提供商
      const provider = state.providers.find(p => p.id === providerId);
      // 检查默认模型中是否存在该提供商
      const defaultProvider = state.defaultModels.find(p => p.id === providerId);
  
      if (!provider && !defaultProvider) {
        // console.error('未找到要删除的提供商:', providerId);
        set({ loading: false });
        return false;
      }

      // 检查是否为默认模型，默认模型不允许删除
      if (defaultProvider || (provider && provider.isDefault)) {
        // console.error('默认模型不允许删除');
        set({ 
          loading: false,
          error: '系统默认模型不允许删除'
        });
        return false;
      }

      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌，请先登录');
      }

      // 获取现有模型列表
      const response = await fetch(API.MODELS.LIST, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const allModels = await response.json();

      // 找出属于当前提供商的所有模型 - 使用provider字段匹配
      const modelsToDelete = allModels.filter((model: DBModel) =>
        model.provider === provider.name &&
        model.base_url === provider.baseUrl &&
        model.credential === provider.apiKey &&
        model.model_type !== 'default' // 确保不删除默认模型
      );

      // console.log(`找到 ${modelsToDelete.length} 个需要删除的模型`);

      // 删除这些模型
      for (const model of modelsToDelete) {
        // console.log(`删除模型: ${model.model_name}`);
        await fetch(API.MODELS.DETAIL(model.id), {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      }

      // 更新本地状态
      set({
        providers: state.providers.filter(p => p.id !== providerId),
        selectedProvider: state.selectedProvider?.id === providerId ? null : state.selectedProvider,
        selectedModel: state.selectedModel?.providerName === provider.name ? null : state.selectedModel,
        loading: false
      });

      return true;
    } catch (error) {
      // console.error('删除提供商失败:', error);
      set({
        loading: false,
        error: error instanceof Error ? error.message : '未知错误'
      });
      return false;
    }
  },

  fetchProviders: async () => {
    // 检查是否已有数据且数据是否在5分钟内获取的
    const state = get();
    const now = Date.now();
    const fiveMinutesInMs = 5 * 60 * 1000;

    if (state.providers.length > 0 && now - state.lastFetchTime < fiveMinutesInMs) {
      // console.log('使用缓存的模型数据，上次获取时间:', new Date(state.lastFetchTime).toLocaleTimeString());
      return;
    }

    // 先设置加载状态，但不清空现有数据，避免闪烁
    set({
      loading: true,
      error: null,
    });

    try {
      // 获取认证令牌
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未找到认证令牌，请先登录');
      }

      // console.log('开始获取模型列表...');
      const response = await fetch(API.MODELS.LIST, {
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`获取模型列表失败: ${response.status} ${response.statusText}`);
      }

      const responseText = await response.text();

      let dbModels: DBModel[] = [];
      try {
        dbModels = JSON.parse(responseText);
        // console.log(`从数据库获取到 ${dbModels.length} 个模型`);
      } catch (parseError) {
        // console.error('解析响应JSON失败:', parseError);
        throw new Error('解析模型数据失败');
      }

      // 使用新的分组函数
      const { userProviders, defaultProviders } = groupModelsByProvider(dbModels);
      // console.log(`分组后得到 ${userProviders.length} 个用户提供商，${defaultProviders.length} 个默认提供商`);

      // 记录每个提供商的模型数量，用于调试
      // [...userProviders, ...defaultProviders].forEach(p => {
      //   console.log(`提供商 ${p.name} 有 ${p.models.length} 个模型`);
      // });

      // 优先选择默认模型，如果没有默认模型则选择第一个用户模型
      const preferredProvider = defaultProviders.length > 0 ? defaultProviders[0] : 
                               userProviders.length > 0 ? userProviders[0] : null;

      // 如果有首选提供商，同时设置默认模型
      const defaultModel = preferredProvider && preferredProvider.models.length > 0
        ? {
            id: preferredProvider.models[0].id,
            providerName: preferredProvider.name,
            baseUrl: preferredProvider.baseUrl,
            isDefault: preferredProvider.isDefault
          }
        : null;

      set({
        providers: userProviders,
        defaultModels: defaultProviders,
        loading: false,
        selectedProvider: preferredProvider,
        selectedModel: defaultModel,  // 设置默认模型
        lastFetchTime: Date.now() // 更新获取时间
      });

    } catch (error) {
      // console.error('获取模型列表失败:', error);
      set({
        loading: false,
        error: error instanceof Error ? error.message : '未知错误',
        // 保留现有数据，不清空
        lastFetchTime: state.lastFetchTime // 保持上次成功获取的时间
      });
    }
  },

  setSelectedProvider: (provider) => {
    // console.log(`切换到提供商: ${provider.name}`);

    // 设置选中的提供商
    set({ selectedProvider: provider });

    // 如果提供商有模型，设置默认选中的模型
    if (provider.models && provider.models.length > 0) {
      const defaultModel = {
        id: provider.models[0].id,
        providerName: provider.name,
        baseUrl: provider.baseUrl
      };
      set({ selectedModel: defaultModel });
    }
  },

  toggleProvider: (id) => set((state) => {
    const updatedProviders = state.providers.map((p) =>
      p.id === id ? { ...p, enabled: !p.enabled } : p
    );
    return {
      providers: updatedProviders,
      selectedProvider: state.selectedProvider?.id === id
        ? updatedProviders.find(p => p.id === id) || state.selectedProvider
        : state.selectedProvider
    };
  }),

  // 更新提供商，并同步到数据库
  updateProvider: async (id, updates: Partial<Provider>) => {
    const state = get();
    const provider = state.providers.find(p => p.id === id);
    const defaultProvider = state.defaultModels.find(p => p.id === id);
  
    if (!provider && !defaultProvider) {
      // console.error('未找到要更新的提供商:', id);
      return;
    }

    // 检查是否为默认模型，默认模型不允许修改
    if (defaultProvider || (provider && provider.isDefault)) {
      // console.error('默认模型不允许修改');
      set({ 
        error: '系统默认模型不允许修改'
      });
      return;
    }

    try {
      set({ loading: true, error: null });

      // Get the authentication token
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('未找到认证令牌，请先登录');
      }

      // 准备发送到数据库的数据
      const payload = {
        name: updates.name || provider.name,
        model_type: updates.type || provider.type,
        model_name: updates.models?.[0]?.id || provider.models[0]?.id,
        provider: provider.type, // 提供商类型
        credential: updates.apiKey || provider.apiKey,
        base_url: updates.baseUrl || provider.baseUrl
      };

      const response = await fetch(API.MODELS.DETAIL(id), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('更新模型失败');
      }

      // 更新本地状态
      set({
        providers: state.providers.map((p) =>
          p.id === id ? { ...p, ...updates } : p
        ),
        loading: false
      });

      // 如果更新的是当前选中的提供商，也更新selectedProvider
      if (state.selectedProvider?.id === id) {
        set({
          selectedProvider: {
            ...state.selectedProvider,
            ...updates
          }
        });
      }
    } catch (error) {
      // console.error('更新模型失败:', error);
      set({
        loading: false,
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  },

  // 添加新提供商，并同步到数据库
  addProvider: async (provider: Partial<Provider>) => {
    try {
      set({ loading: true, error: null });

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('未找到认证令牌，请先登录');
      }

      // 获取现有模型列表
      const response = await fetch(API.MODELS.LIST, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      const existingModels = await response.json();

      // 按提供商名称和配置过滤相关模型 - 使用provider字段匹配
      const relatedModels = existingModels.filter((model: DBModel) =>
        model.provider === provider.name &&
        model.base_url === provider.baseUrl &&
        model.credential === provider.apiKey
      );

      // 使用过滤后的模型创建新的 Provider
      const newProvider: Provider = {
        id: relatedModels[0]?.id || provider.id || '',
        name: provider.name || '',
        type: provider.type || 'openai',
        enabled: true,
        baseUrl: provider.baseUrl || '',
        apiKey: provider.apiKey || '', // 添加缺失的 apiKey 属性
        models: relatedModels.map((model: DBModel) => ({
          id: model.model_name,
          name: model.model_name
        })),
        color: getColorForProvider(provider.type || 'default'),
        icon: (provider.name || '').charAt(0).toUpperCase()
      };

      // 更新本地状态 - 检查是否已存在相同提供商
      set((state) => {
        // 检查是否已存在相同名称和配置的提供商
        const existingProviderIndex = state.providers.findIndex(p =>
          p.name === newProvider.name &&
          p.baseUrl === newProvider.baseUrl &&
          p.apiKey === newProvider.apiKey
        );

        let updatedProviders;
        if (existingProviderIndex >= 0) {
          // 如果已存在，则替换
          // console.log(`提供商 ${newProvider.name} 已存在，更新现有记录`);
          updatedProviders = [...state.providers];
          updatedProviders[existingProviderIndex] = newProvider;
        } else {
          // 如果不存在，则添加
          // console.log(`添加新提供商: ${newProvider.name}`);
          updatedProviders = [...state.providers, newProvider];
        }

        return {
          providers: updatedProviders,
          loading: false
        };
      });
    } catch (error) {
      // console.error('添加模型失败:', error);
      set({
        loading: false,
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  },

  setSelectedModel: (modelId: string | null) => {
    set((state) => {
      if (!modelId) {
        // console.log('用户取消选择模型');
        return { selectedModel: null };
      }

      // 首先在默认模型中查找
      const defaultProvider = state.defaultModels.find(p =>
        p.enabled && p.models.some(m => m.id === modelId)
      );

      if (defaultProvider) {
        // console.log('选择默认模型:', {
        //   modelId,
        //   providerName: defaultProvider.name,
        //   baseUrl: defaultProvider.baseUrl,
        //   isDefault: true
        // });
      
        return {
          selectedModel: {
            id: modelId,
            providerName: defaultProvider.name,
            baseUrl: defaultProvider.baseUrl,
            isDefault: true
          }
        };
      }
  
      // 然后在用户模型中查找
      const userProvider = state.providers.find(p =>
        p.enabled && p.models.some(m => m.id === modelId)
      );
  
      if (!userProvider) {
        // console.warn('未找到选中模型的提供商信息:', modelId);
        return state;
      }
  
      return {
        selectedModel: {
          id: modelId,
          providerName: userProvider.name,
          baseUrl: userProvider.baseUrl,
          isDefault: false
        }
      };
    });
  },
}));

export interface LLMModel {
  id: string;
  name: string;
  contextWindow?: number;
  maxTokens?: number;
}

export interface Provider {
  id: string;
  name: string;
  icon: string;
  color: string;
  type: 'openai' | 'deepseek' | 'qwen' | 'default';
  apiKey: string;
  baseUrl: string;
  models: LLMModel[];
  enabled: boolean;
  isDefault?: boolean; // 标识是否为默认提供商
  readonly?: boolean;  // 标识是否只读
}