import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface NewsPanelState {
  isOpen: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
}

export const useNewsPanelStore = create<NewsPanelState>()(
  persist(
    (set) => ({
      isOpen: false,
      open: () => set({ isOpen: true }),
      close: () => set({ isOpen: false }),
      toggle: () => set((state) => ({ isOpen: !state.isOpen })),
    }),
    {
      name: 'news-panel-state',
    }
  )
);