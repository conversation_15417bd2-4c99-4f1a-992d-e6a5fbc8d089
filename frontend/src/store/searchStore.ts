import { create } from 'zustand';

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
}

interface SearchStore {
  results: SearchResult[];
  selectedResults: Set<number>;
  isSearchMode: boolean;
  setResults: (results: SearchResult[]) => void;
  toggleSelected: (index: number) => void;
  clearSelected: () => void;
  setSearchMode: (mode: boolean) => void;
}

export const useSearchStore = create<SearchStore>((set) => ({
  results: [],
  selectedResults: new Set(),
  isSearchMode: false,
  setResults: (results) => set({ 
    results,
    selectedResults: new Set()
  }),
  toggleSelected: (index) => set((state) => {
    const newSelected = new Set(state.selectedResults);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    // 移除自动修改搜索模式的逻辑，保持当前状态
    return { selectedResults: newSelected };
  }),
  clearSelected: () => set({
    selectedResults: new Set()
    // 移除自动设置搜索模式，保持当前状态
  }),
  setSearchMode: (mode) => set({ isSearchMode: mode }),
}));