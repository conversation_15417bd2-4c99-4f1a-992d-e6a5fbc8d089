import { create } from 'zustand';
import API from '@/config/api';

export interface Discussion {
  id: string;
  content: string;
  created_at: string;
  creator: {
    id: string;
    username: string;
    display_name?: string;
    avatar_url?: string;
  };
}

export interface OpenSourceProject {
  id: string;
  name: string;
  description: string;
  github_url: string;
  image_url?: string;
  rate?: number;
  is_private?: boolean;
  creator_id?: string;
  created_at?: string;
  updated_at?: string | null;
  discussions: Discussion[];
}

interface OpenSourceStore {
  projects: OpenSourceProject[];
  loading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  fetchProjects: (page?: number, limit?: number) => Promise<void>;
  addProject: (project: Partial<OpenSourceProject>) => Promise<void>;
  addComment: (projectId: string, content: string) => Promise<void>;
  fetchDiscussions: (projectId: string) => Promise<void>;
  updateProjectRate: (projectId: string, rate: number) => Promise<OpenSourceProject>;
  setPage: (page: number) => void;
}

export const useOpenSourceStore = create<OpenSourceStore>((set, get) => ({
  projects: [],
  loading: false,
  error: null,
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  pageSize: 6,
  fetchProjects: async (page = 1, limit = 6) => {
    set({ loading: true, error: null });
    try {
      const token = localStorage.getItem('token');
      const skip = (page - 1) * limit;
      const response = await fetch(`${API.OPEN_SOURCE.LIST}?skip=${skip}&limit=${limit}`, {
        headers: {
          'accept': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        cache: 'no-cache',
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.detail || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      // Handle both array response and paginated response
      if (Array.isArray(data)) {
        set({ 
          projects: data, 
          error: null,
          currentPage: page,
          totalItems: data.length,
          totalPages: 1,
          pageSize: limit
        });
      } else {
        set({ 
          projects: data.items || data.data || [], 
          error: null,
          currentPage: page,
          totalItems: data.total || 0,
          totalPages: Math.ceil((data.total || 0) / limit),
          pageSize: limit
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取开源项目失败';
      set({ error: errorMessage, projects: [] });
    } finally {
      set({ loading: false });
    }
  },
  setPage: (page: number) => {
    const { fetchProjects, pageSize } = get();
    fetchProjects(page, pageSize);
  },

  addProject: async (project: Partial<OpenSourceProject>) => {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('未登录或会话已过期，请重新登录');
    }
    const response = await fetch(API.OPEN_SOURCE.CREATE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(project),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.detail || '添加项目失败');
    }
    // 自动刷新项目列表以显示新项目
    get().fetchProjects();
  },

  addComment: async (projectId: string, content: string) => {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('请先登录再评论');
    }
    const response = await fetch(API.OPEN_SOURCE.ADD_COMMENT(projectId), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ content }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.detail || '发表评论失败');
    }
    
    // 评论成功后，只刷新该项目的讨论列表
    await get().fetchDiscussions(projectId);
  },

  fetchDiscussions: async (projectId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(API.OPEN_SOURCE.GET_DISCUSSIONS(projectId), {
        headers: {
          'accept': 'application/json',
          ...(token ? { 'Authorization': `Bearer ${token}` } : {})
        },
        cache: 'no-cache',
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.detail || `获取评论失败`);
      }
      const discussions = await response.json();
      set((state) => ({
        projects: state.projects.map((p) =>
          p.id === projectId ? { ...p, discussions } : p
        ),
      }));
    } catch (error) {
      console.error('获取评论失败:', error);
    }
  },

  updateProjectRate: async (projectId: string, rate: number) => {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('未登录或会话已过期，请重新登录');
    }
    
    const response = await fetch(API.OPEN_SOURCE.UPDATE_RATE(projectId), {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ rate }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.detail || '更新评分失败');
    }

    const updatedProject = await response.json();
    
    // 更新本地状态
    set((state) => ({
      projects: state.projects.map((p) =>
        p.id === projectId ? { ...p, rate: updatedProject.rate } : p
      ),
    }));

    return updatedProject;
  },
})); 