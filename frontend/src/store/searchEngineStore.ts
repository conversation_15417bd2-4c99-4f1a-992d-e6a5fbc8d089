import { create } from 'zustand';
import API from '@/config/api';

interface SearchEngine {
  id: string;
  name: string;
  provider: string;
  url: string;
  token: string;
  available_credit: number;
  note: string | null;
  is_active: boolean;
  user_id: string;
  created_at: string;
  updated_at: string;
}

// 定义搜索引擎提供商接口
interface SearchEngineProvider {
  value: string;
  label: string;
  apiUrl: string; // 添加默认 API URL
}

interface SearchEngineStore {
  engines: SearchEngine[];
  selectedEngine: string;
  providers: SearchEngineProvider[]; // 添加提供商列表
  lastFetchTime: number; // 添加上次获取数据的时间戳
  setSelectedEngine: (id: string) => void;
  fetchEngines: () => Promise<void>;
}

export const useSearchEngineStore = create<SearchEngineStore>((set, get) => ({
  engines: [],
  selectedEngine: '',
  lastFetchTime: 0,
  // 添加提供商列表及其默认 API URL
  providers: [
    { value: "", label: "请选择搜索引擎提供商", apiUrl: "" },
    { value: "bing", label: "Bing", apiUrl: "https://api.bing.microsoft.com/v7.0/search" },
    { value: "tavily", label: "Tavily", apiUrl: "https://api.tavily.com/search" },
    { value: "exa", label: "EXA", apiUrl: "https://api.exa.ai/search" },
    // { value: "google", label: "Google", apiUrl: "https://serpapi.com/search" }
  ],
  setSelectedEngine: (id) => set({ selectedEngine: id }),
  fetchEngines: async () => {
    // 检查是否已有数据且数据是否在5分钟内获取的
    const state = get();
    const now = Date.now();
    const fiveMinutesInMs = 5 * 60 * 1000;

    if (state.engines.length > 0 && now - state.lastFetchTime < fiveMinutesInMs) {
      // console.log('使用缓存的搜索引擎数据，上次获取时间:', new Date(state.lastFetchTime).toLocaleTimeString());
      return;
    }

    try {
      // 获取 token
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('搜索引擎获取失败: 用户未登录或会话已过期');
        throw new Error('用户未登录或会话已过期，请重新登录');
      }

      // console.log('正在获取搜索引擎列表...');
      // console.log('API 端点:', API.SEARCH.ENGINES);

      const response = await fetch(API.SEARCH.ENGINES, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`搜索引擎获取失败: HTTP ${response.status}`, errorText);
        throw new Error(`Failed to fetch search engines: ${response.status}`);
      }

      const data = await response.json();
      // console.log('搜索引擎获取成功:', data);

      set({
        engines: data,
        // 如果有引擎且没有选择过，则默认选择第一个
        selectedEngine: get().selectedEngine || (data.length > 0 ? data[0].id : ''),
        lastFetchTime: Date.now() // 更新获取时间
      });
    } catch (error) {
      console.error('搜索引擎获取错误:', error);
    }
  }
}));