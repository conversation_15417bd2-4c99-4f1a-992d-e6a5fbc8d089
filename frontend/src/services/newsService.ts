import axios from 'axios';
import { parseWithJinaReader } from '@/lib/fetchNews';

// 定义新闻项的接口
export interface NewsItem {
  id: string;
  title: string;
  summary: string;
  url: string;
  source: string;
  publishedAt: string;
  translatedTitle?: string;
  translatedSummary?: string;
}

const translate_system_prompt = `
你是一位专业AI新闻翻译官（母语为简体中文），具备人工智能领域专业知识。请将输入的英文科学新闻RSS内容按以下规范处理：
1. 根据提供的网页内容（Markdown格式）条目进行筛选
2. 所有专业术语必须符合标准
3. 专用缩略词需保留
5. 输出格式严格遵循JSON Schema：
{
  "news_list": [
    {
      "title": "string（标题）",
      "summary": "string（包含3个核心发现点的摘要）",
      “source”: "string(网页地址)"
    }
  ]
}

`

// 从TLDR Tech获取新闻
export async function fetchTLDRNews(date: string = getTodayDateString()): Promise<NewsItem[]> {
  try {
    console.log('开始获取TLDR Tech新闻...');
    const tldrUrl = `https://tldr.tech/ai/${date}`;

    // 使用Jina Reader获取网页内容
    console.log('使用Jina Reader获取TLDR Tech网页内容...');
    const parsedData = await parseWithJinaReader(tldrUrl);

    // 直接使用内容，不进行解析
    if (parsedData && parsedData.content) {
      // 使用大模型翻译新闻
      console.log('开始翻译新闻...');
      const translatedNews = await translateNews(parsedData.content);
      console.log('新闻翻译完成');

      return translatedNews;
    }
    console.warn('无法从解析数据中获取内容，返回空数组');
    return [];
  } catch (error) {
    console.error('获取新闻失败:', error);
    return getMockNews(); // 出错时返回模拟数据
  }
}

// 使用本地大模型翻译新闻内容
export async function translateNews(content: string): Promise<NewsItem[]> {
  try {
    // 调用本地OpenAI兼容API进行翻译
    const response = await axios.post('http://47.117.124.84:4035/v1/chat/completions', {
      model: "qwen3-30B-A3B", // 使用本地模型名称
      messages: [
        {
          role: "system",
          content: translate_system_prompt
        },
        {
          role: "user",
          content: content
        }
      ],
      temperature: 0.3
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-X9gH2mLp3rT5yU7jK4nB8vF1zR6' // 将API密钥放在Authorization头中
      }
    });

    if (response.data && response.data.choices && response.data.choices[0]?.message?.content) {
      const translatedContent = response.data.choices[0].message.content;

      try {
        // 尝试解析JSON响应
        const jsonResponse = JSON.parse(translatedContent);

        if (jsonResponse.news_list && jsonResponse.news_list.length > 0) {
          // 将翻译后的新闻列表转换为NewsItem数组
          return jsonResponse.news_list.map((newsItem: any, index: number) => ({
            id: `news-${index + 1}`,
            title: newsItem.title,
            summary: newsItem.summary,
            url: newsItem.source || `https://example.com/news-${index + 1}`,
            source: 'TLDR Tech',
            publishedAt: new Date().toISOString(),
            translatedTitle: newsItem.title,
            translatedSummary: newsItem.summary
          }));
        }
      } catch (parseError) {
        console.error('解析翻译JSON响应失败:', parseError);
        // 如果JSON解析失败，返回空数组
        return [];
      }
    }
    return [];
  } catch (error) {
    console.error('Error in translation process:', error);
    return []; // 出错时返回空数组
  }
}


// 获取今天的日期字符串，格式为YYYY-MM-DD
function getTodayDateString(): string {
  // 获取当前日期
  const today = new Date();
  // 减去一天，因为时差问题
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const year = yesterday.getFullYear();
  const month = String(yesterday.getMonth() + 1).padStart(2, '0');
  const day = String(yesterday.getDate()).padStart(2, '0');

  console.log(`使用日期: ${year}-${month}-${day} (昨天的日期)`);
  return `${year}-${month}-${day}`;
}

// 提供模拟数据，以防API调用失败
export function getMockNews(): NewsItem[] {
  return [
    {
      id: 'news-1',
      title: 'GitHub Introduces New AI Features',
      summary: 'GitHub has announced several new AI-powered features to help developers write better code and improve productivity.',
      url: 'https://github.blog/new-ai-features',
      source: 'TLDR Tech',
      publishedAt: new Date().toISOString(),
      translatedTitle: 'GitHub 推出新的 AI 功能',
      translatedSummary: 'GitHub 宣布推出多项由 AI 驱动的新功能，帮助开发者编写更好的代码并提高生产力。'
    },
    {
      id: 'news-2',
      title: 'TypeScript 5.5 Released with Performance Improvements',
      summary: 'Microsoft has released TypeScript 5.5 with significant performance improvements and new language features.',
      url: 'https://devblogs.microsoft.com/typescript/typescript-5-5',
      source: 'TLDR Tech',
      publishedAt: new Date().toISOString(),
      translatedTitle: 'TypeScript 5.5 发布，性能得到提升',
      translatedSummary: '微软发布了 TypeScript 5.5，带来了显著的性能改进和新的语言特性。'
    },
    {
      id: 'news-3',
      title: 'React 19 Beta Now Available',
      summary: 'The React team has announced the beta release of React 19, featuring improved server components and a new hooks system.',
      url: 'https://react.dev/blog/react-19-beta',
      source: 'TLDR Tech',
      publishedAt: new Date().toISOString(),
      translatedTitle: 'React 19 测试版现已推出',
      translatedSummary: 'React 团队宣布了 React 19 的测试版发布，其特点是改进了服务器组件和新的钩子系统。'
    },
    {
      id: 'news-4',
      title: 'AWS Launches New AI Service for Developers',
      summary: 'Amazon Web Services has launched a new AI service designed to help developers build and deploy machine learning models more easily.',
      url: 'https://aws.amazon.com/new-ai-service',
      source: 'TLDR Tech',
      publishedAt: new Date().toISOString(),
      translatedTitle: 'AWS 推出面向开发者的新 AI 服务',
      translatedSummary: '亚马逊网络服务推出了一项新的 AI 服务，旨在帮助开发者更轻松地构建和部署机器学习模型。'
    },
    {
      id: 'news-5',
      title: 'Next.js 14 Introduces New Data Fetching API',
      summary: 'Vercel has released Next.js 14 with a completely redesigned data fetching API and improved build performance.',
      url: 'https://nextjs.org/blog/next-14',
      source: 'TLDR Tech',
      publishedAt: new Date().toISOString(),
      translatedTitle: 'Next.js 14 引入新的数据获取 API',
      translatedSummary: 'Vercel 发布了 Next.js 14，其中包含全新设计的数据获取 API 和改进的构建性能。'
    }
  ];
}