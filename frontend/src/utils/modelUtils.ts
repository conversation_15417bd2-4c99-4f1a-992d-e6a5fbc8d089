import { useLLMStore } from '@/store/llmStore';
import API from '@/config/api';

export interface ModelInfo {
  baseUrl: string;
  model: string;
  apiKey?: string;
  providerName?: string;
  isDefault?: boolean;
}

export class ModelManager {
  /**
   * 获取当前选中模型的完整信息
   * 统一处理默认模型和用户模型的查找逻辑
   */
  static getSelectedModelInfo(): ModelInfo | null {
    const { selectedModel, providers, defaultModels } = useLLMStore.getState();
    
    if (!selectedModel) {
      // 返回环境变量配置的默认值
      return {
        baseUrl: import.meta.env.VITE_LLM_HOST || "",
        model: import.meta.env.VITE_LLM_MODEL || "",
        apiKey: import.meta.env.VITE_LLM_API_KEY || "",
        providerName: "Default",
        isDefault: true
      };
    }

    const modelId = typeof selectedModel === 'string' ? selectedModel : selectedModel.id;
    
    // 首先在默认模型中查找
    const defaultProvider = defaultModels.find(p => 
      p.enabled && p.models.some(m => m.id === modelId)
    );
    
    if (defaultProvider) {
      return {
        baseUrl: defaultProvider.baseUrl || "",
        model: modelId,
        apiKey: defaultProvider.apiKey || import.meta.env.VITE_LLM_API_KEY || "",
        providerName: defaultProvider.name,
        isDefault: true
      };
    }
    
    // 然后在用户提供商中查找
    const userProvider = providers.find(p => 
      p.enabled && p.models.some(m => m.id === modelId)
    );
    
    if (userProvider) {
      return {
        baseUrl: userProvider.baseUrl || "",
        model: modelId,
        apiKey: userProvider.apiKey || import.meta.env.VITE_LLM_API_KEY || "",
        providerName: userProvider.name,
        isDefault: false
      };
    }
    
    // console.warn('未找到对应的提供商配置:', modelId);
    return null;
  }

  /**
   * 构建标准化的聊天完成API URL
   * 统一处理不同格式的baseUrl
   */
  static buildChatCompletionsUrl(baseUrl: string): string {
    if (!baseUrl) {
      return `${import.meta.env.VITE_LLM_HOST || ""}/v1/chat/completions`;
    }
    
    // 移除末尾的斜杠（如果有）
    const cleanBaseUrl = baseUrl.replace(/\/$/, '');
    
    // 检查是否已经包含完整路径
    if (cleanBaseUrl.includes('/chat/completions')) {
      return cleanBaseUrl;
    }
    
    // 检查是否已经包含v1路径
    if (cleanBaseUrl.endsWith('/v1')) {
      return `${cleanBaseUrl}/chat/completions`;
    }
    
    // 默认添加完整路径
    return `${cleanBaseUrl}/v1/chat/completions`;
  }

  /**
   * 获取完整的API配置信息
   * 包含URL、模型ID、API密钥等
   */
  static getApiConfig(): {
    url: string;
    model: string;
    apiKey: string;
    providerName?: string;
    isDefault?: boolean;
  } | null {
    const modelInfo = this.getSelectedModelInfo();
    
    if (!modelInfo) {
      return null;
    }
    
    return {
      url: this.buildChatCompletionsUrl(modelInfo.baseUrl),
      model: modelInfo.model,
      apiKey: modelInfo.apiKey || "",
      providerName: modelInfo.providerName,
      isDefault: modelInfo.isDefault
    };
  }

  /**
   * 创建标准化的请求配置
   */
  static createRequestConfig(messages: any[], options: {
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
  } = {}): {
    url: string;
    requestBody: any;
    headers: Record<string, string>;
  } | null {
    const apiConfig = this.getApiConfig();
    
    if (!apiConfig) {
      return null;
    }
    
    const requestBody = {
      model: apiConfig.model,
      messages,
      temperature: options.temperature ?? 0.6,
      max_tokens: options.maxTokens,
      stream: options.stream ?? false
    };
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiConfig.apiKey}`
    };
    
    return {
      url: apiConfig.url,
      requestBody,
      headers
    };
  }
}