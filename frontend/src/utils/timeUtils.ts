/**
 * 时间工具函数
 * 处理时区转换和时间格式化
 */

/**
 * 确保时间戳被正确解析为UTC时间
 * @param timestamp 时间戳字符串
 * @returns 正确的UTC时间戳字符串
 */
export const ensureUtcTimestamp = (timestamp: string): string => {
  // 如果时间戳已经包含时区信息，直接返回
  if (timestamp.includes('Z') || timestamp.includes('+') || timestamp.includes('-')) {
    return timestamp;
  }
  // 否则添加'Z'后缀表示UTC时间
  return timestamp + 'Z';
};

/**
 * 格式化时间戳为相对时间（如：刚刚、5分钟前、2小时前等）
 * @param timestamp 时间戳字符串
 * @returns 格式化后的时间字符串
 */
export const formatTimestamp = (timestamp: string): string => {
  const utcTimestamp = ensureUtcTimestamp(timestamp);
  const date = new Date(utcTimestamp);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return '刚刚';
  } else if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}分钟前`;
  } else if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}小时前`;
  } else if (diffInSeconds < 2592000) {
    return `${Math.floor(diffInSeconds / 86400)}天前`;
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
};

/**
 * 格式化时间戳为本地日期字符串
 * @param timestamp 时间戳字符串
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export const formatToLocalDate = (
  timestamp: string, 
  options?: Intl.DateTimeFormatOptions
): string => {
  const utcTimestamp = ensureUtcTimestamp(timestamp);
  const date = new Date(utcTimestamp);
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  };
  
  return date.toLocaleDateString('zh-CN', options || defaultOptions);
};

/**
 * 格式化时间戳为本地日期时间字符串
 * @param timestamp 时间戳字符串
 * @param options 格式化选项
 * @returns 格式化后的日期时间字符串
 */
export const formatToLocalDateTime = (
  timestamp: string,
  options?: Intl.DateTimeFormatOptions
): string => {
  const utcTimestamp = ensureUtcTimestamp(timestamp);
  const date = new Date(utcTimestamp);
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  };
  
  return date.toLocaleString('zh-CN', options || defaultOptions);
};
