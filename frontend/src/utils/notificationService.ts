import API from '@/config/api';
import { NotificationType } from '@/types/notification';

export class NotificationService {
  private static getAuthHeaders() {
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  // 创建点赞通知
  static async createLikeNotification(params: {
    postId: string;
    postOwnerId: string;
    senderId: string;
  }) {
    try {
      const response = await fetch(`${API.API_PATH}/notifications/create`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          type: NotificationType.LIKE,
          user_id: params.postOwnerId,
          sender_id: params.senderId,
          target_post_id: params.postId,
          source_post_id: params.postId,
          content: '点赞了你的帖子'
        })
      });

      if (!response.ok) {
        // console.error('创建点赞通知失败:', response.statusText);
      }
    } catch (error) {
      // console.error('创建点赞通知失败:', error);
    }
  }

  // 创建关注通知
  static async createFollowNotification(params: {
    followedUserId: string;
    followerId: string;
  }) {
    try {
      const response = await fetch(`${API.API_PATH}/notifications/create`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          type: NotificationType.FOLLOW,
          user_id: params.followedUserId,
          sender_id: params.followerId,
          content: '关注了你'
        })
      });

      if (!response.ok) {
        // console.error('创建关注通知失败:', response.statusText);
      }
    } catch (error) {
      // console.error('创建关注通知失败:', error);
    }
  }

  // 创建频道新帖通知
  static async createChannelPostNotification(params: {
    channelId: string;
    postId: string;
    authorId: string;
    subscriberIds: string[];
  }) {
    try {
      // 为每个订阅者创建通知
      const promises = params.subscriberIds.map(subscriberId => 
        fetch(`${API.API_PATH}/notifications/create`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            type: NotificationType.CHANNEL_POST,
            user_id: subscriberId,
            sender_id: params.authorId,
            target_post_id: params.postId,
            channel_id: params.channelId,
            content: '在你关注的频道发布了新帖子'
          })
        })
      );

      await Promise.all(promises);
    } catch (error) {
      // console.error('创建频道新帖通知失败:', error);
    }
  }

  // 获取用户的未读通知数量
  static async getUnreadCount(): Promise<number> {
    try {
      const response = await fetch(`${API.API_PATH}/notifications/unread/count`, {
        headers: this.getAuthHeaders()
      });

      if (response.ok) {
        const data = await response.json();
        return data.count || 0;
      }
    } catch (error) {
      // console.error('获取未读通知数量失败:', error);
    }
    return 0;
  }

  // 获取用户的通知列表
  static async getNotifications(params: {
    skip?: number;
    limit?: number;
    unreadOnly?: boolean;
  } = {}) {
    try {
      const { skip = 0, limit = 20, unreadOnly = false } = params;
      const endpoint = unreadOnly ? 'unread' : '';
      const queryParams = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString()
      });

      const response = await fetch(
        `${API.API_PATH}/notifications/${endpoint}?${queryParams}`,
        {
          headers: this.getAuthHeaders()
        }
      );

      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      // console.error('获取通知列表失败:', error);
    }
    return [];
  }

  // 标记通知为已读
  static async markAsRead(notificationId: string): Promise<boolean> {
    try {
      const response = await fetch(
        `${API.API_PATH}/notifications/${notificationId}/read`,
        {
          method: 'PATCH',
          headers: this.getAuthHeaders()
        }
      );

      return response.ok;
    } catch (error) {
      // console.error('标记通知已读失败:', error);
      return false;
    }
  }

  // 标记所有通知为已读
  static async markAllAsRead(): Promise<boolean> {
    try {
      const response = await fetch(`${API.API_PATH}/notifications/read-all`, {
        method: 'PATCH',
        headers: this.getAuthHeaders()
      });

      return response.ok;
    } catch (error) {
      // console.error('标记所有通知已读失败:', error);
      return false;
    }
  }
}