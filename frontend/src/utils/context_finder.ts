// TODO: 根据用户的上下文，提取关键词，并去Context list中搜索最相关的内容列表

// 类型定义
export interface ContextItem {
  id: string;
  title: string;
  content: string;
  type: 'bookmark' | 'weibo' | 'paper';
  url?: string;
  author?: string;
  date?: string;
  relevanceScore?: number;
}

export interface KeywordExtractionResult {
  keywords: string[];
  researchDirection: string;
  confidence: number;
}

export interface ContextSearchResult {
  items: ContextItem[];
  totalFound: number;
  searchQuery: string;
}

// Prompt：
const keywords_prompt = `
你是一位学术分析专家。请仔细阅读用户提供的上下文内容，从中提取出核心的学术关键词，并基于这些关键词推测用户当前可能关注的学术研究领域或探索方向。要求：
- 关键词提取需准确反映上下文的核心主题
- 推测方向应具有学术前沿性
- 返回JSON格式：{"keywords": ["关键词1", "关键词2"], "researchDirection": "研究方向描述", "confidence": 0.8}

请按照以下格式输出学术关键词列表和研究方向：
`;

// 提取关键词的函数
export async function extractKeywords(
  context: string, 
  modelConfig?: { baseUrl: string; model: string; apiKey?: string }
): Promise<KeywordExtractionResult> {
  try {
    const baseUrl = modelConfig?.baseUrl || import.meta.env.VITE_LLM_HOST || "";
    const model = modelConfig?.model || import.meta.env.VITE_LLM_MODEL || "";
    const apiKey = modelConfig?.apiKey || 'sk-YyiBg6DSn1Fc2KBNU6ZYtw';

    const endpoint = baseUrl.endsWith('/') 
      ? `${baseUrl}chat/completions` 
      : `${baseUrl}/v1/chat/completions`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'system', content: keywords_prompt },
          { role: 'user', content: context }
        ],
        temperature: 0.3,
        max_tokens: 500
      })
    });

    if (!response.ok) {
      throw new Error('关键词提取请求失败');
    }

    const data = await response.json();
    const content = data.choices[0].message.content;
    
    // 尝试解析JSON响应
    try {
      const parsed = JSON.parse(content);
      return {
        keywords: parsed.keywords || [],
        researchDirection: parsed.researchDirection || '',
        confidence: parsed.confidence || 0.5
      };
    } catch {
      // 如果不是JSON格式，尝试简单解析
      const keywords = content.match(/关键词[：:]\s*(.+)/)?.[1]?.split(/[,，、]/) || [];
      return {
        keywords: keywords.map((k: string) => k.trim()).filter(Boolean),
        researchDirection: content,
        confidence: 0.6
      };
    }
  } catch (error) {
    console.error('关键词提取失败:', error);
    // 返回基于简单文本分析的后备结果
    return extractKeywordsFallback(context);
  }
}

// 后备关键词提取方法
function extractKeywordsFallback(context: string): KeywordExtractionResult {
  // 简单的关键词提取逻辑
  const commonWords = ['的', '是', '在', '有', '和', '与', '或', '但', '而', '了', '着', '过'];
  const words = context
    .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 1 && !commonWords.includes(word))
    .slice(0, 10);

  return {
    keywords: words,
    researchDirection: '基于文本内容的研究方向分析',
    confidence: 0.4
  };
}

// 搜索相关上下文的主函数
export async function searchRelatedContext(
  userContext: string,
  modelConfig?: { baseUrl: string; model: string; apiKey?: string }
): Promise<ContextSearchResult> {
  try {
    // 1. 提取关键词
    const keywordResult = await extractKeywords(userContext, modelConfig);
    
    // 2. 基于关键词搜索上下文
    const contextItems = await searchContextItems(keywordResult.keywords);
    
    // 3. 计算相关性并排序
    const rankedItems = rankContextItems(contextItems, keywordResult.keywords, userContext);
    
    // 4. 返回前3-5个最相关的结果
    return {
      items: rankedItems.slice(0, 5),
      totalFound: contextItems.length,
      searchQuery: keywordResult.keywords.join(', ')
    };
  } catch (error) {
    console.error('搜索相关上下文失败:', error);
    return {
      items: [],
      totalFound: 0,
      searchQuery: ''
    };
  }
}

// 搜索上下文项目（模拟实现）
async function searchContextItems(keywords: string[]): Promise<ContextItem[]> {
  // TODO: 实际实现中需要连接到真实的数据源
  // 这里提供模拟数据作为示例
  const mockContextItems: ContextItem[] = [
    {
      id: '1',
      title: '深度学习在自然语言处理中的应用',
      content: '本文探讨了深度学习技术在自然语言处理领域的最新进展...',
      type: 'paper',
      author: '张三',
      date: '2024-01-15'
    },
    {
      id: '2', 
      title: '机器学习算法优化研究',
      content: '介绍了几种新的机器学习算法优化方法...',
      type: 'bookmark',
      url: 'https://example.com/ml-optimization'
    },
    {
      id: '3',
      title: '人工智能发展趋势分析',
      content: '分析了当前人工智能技术的发展趋势和未来方向...',
      type: 'weibo',
      date: '2024-02-01'
    }
  ];

  // 简单的关键词匹配逻辑
  return mockContextItems.filter(item => 
    keywords.some(keyword => 
      item.title.includes(keyword) || 
      item.content.includes(keyword)
    )
  );
}

// 对上下文项目进行相关性排序
function rankContextItems(
  items: ContextItem[], 
  keywords: string[], 
  originalContext: string
): ContextItem[] {
  return items.map(item => {
    let score = 0;
    
    // 基于关键词匹配计算分数
    keywords.forEach(keyword => {
      if (item.title.includes(keyword)) score += 3;
      if (item.content.includes(keyword)) score += 2;
    });
    
    // 基于类型权重
    const typeWeights = { paper: 3, bookmark: 2, weibo: 1 };
    score += typeWeights[item.type];
    
    // 基于时间新鲜度（如果有日期）
    if (item.date) {
      const itemDate = new Date(item.date);
      const now = new Date();
      const daysDiff = (now.getTime() - itemDate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff < 30) score += 2;
      else if (daysDiff < 90) score += 1;
    }
    
    return { ...item, relevanceScore: score };
  }).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
}





