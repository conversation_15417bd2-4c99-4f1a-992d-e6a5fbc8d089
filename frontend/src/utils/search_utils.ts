import { useSearchEngineStore } from '@/store/searchEngineStore';

// TODO: 多个KEY的缓冲问题
// 定义搜索结果接口
export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  datePublished?: string;
  language?: string;
  siteName?: string;
}

/**
 * 执行搜索查询
 * @param query 搜索查询字符串
 * @returns 处理后的搜索结果数组
 */
export const performSearch = async (query: string): Promise<{
  results: SearchResult[];
  error?: Error;
}> => {
  try {
    // 从 store 获取当前选中的搜索引擎完整信息
    const { engines, selectedEngine, providers } = useSearchEngineStore.getState();
    // console.log('可用的搜索引擎:', engines);
    // console.log('当前选择的搜索引擎ID:', selectedEngine);
    
    const currentEngine = engines.find(engine => engine.id === selectedEngine);
    
    if (!currentEngine) {
      // console.error('未找到选中的搜索引擎配置');
      throw new Error('未找到选中的搜索引擎配置');
    }
    
    // console.log('当前使用的搜索引擎:', currentEngine);
    
    // 获取搜索引擎的 token
    const token = currentEngine.token;
    
    let response;
    let data;
    
    // 根据不同的搜索引擎提供商使用不同的 API 调用方式
    const provider = currentEngine.provider.toLowerCase();
    // console.log('搜索引擎提供商:', provider);
    
    // 从 store 中获取对应提供商的默认 API URL
    const providerConfig = providers.find(p => p.value.toLowerCase() === provider);
    const defaultApiUrl = providerConfig?.apiUrl || '';
    
    // 优先使用引擎配置中的 URL，如果没有则使用默认 URL
    const apiUrl = currentEngine.url || defaultApiUrl;
    
    if (provider === 'bing') {
      // Bing 搜索使用官方推荐的 API 调用方式
      const params = new URLSearchParams({ q: query });
      response = await fetch(`${apiUrl}?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Ocp-Apim-Subscription-Key': token,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Bing 搜索请求失败: ${response.status} ${response.statusText}`);
      }
      
      data = await response.json();
    } else if (provider === 'tavily') {
      // Tavily 搜索使用官方推荐的 API 调用方式
      response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          query: query,
          search_depth: 'basic',
          include_domains: [],
          exclude_domains: [],
          max_results: 10
        })
      });
      
      if (!response.ok) {
        throw new Error(`Tavily 搜索请求失败: ${response.status} ${response.statusText}`);
      }
      
      data = await response.json();
    } else if (provider === 'exa') {
      // EXA搜索使用官方API
      response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': token
        },
        body: JSON.stringify({
          query: query,
          type: "auto",
          contents: {
            text: true
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`EXA搜索请求失败: ${response.status} ${response.statusText}`);
      }
      
      data = await response.json();
    } else {
      // console.warn(`不支持的搜索引擎提供商: ${provider}，将使用通用方法`);
      
      // 使用引擎配置中的 URL
      const searchUrl = apiUrl;
      const method = currentEngine.provider === 'google' ? 'POST' : 'GET';
      const url = method === 'GET' 
        ? `${searchUrl}?q=${encodeURIComponent(query)}` 
        : searchUrl;
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      // 如果有 token，添加到请求头
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      // 构建请求体
      const body = method === 'POST' ? JSON.stringify({
        q: query,
        // 如果是 Google 搜索，添加语言参数
        ...(currentEngine.provider === 'google' ? { hl: 'zh-cn' } : {})
      }) : undefined;
      
      // console.log('发送通用搜索请求:', {
      //   url,
      //   method,
      //   headers: { ...headers, Authorization: token ? '已设置' : '未设置' },
      //   body: body ? '已设置' : '未设置'
      // });
      
      try {
        // 发送请求
        response = await fetch(url, {
          method,
          headers,
          ...(body ? { body } : {})
        });
        
        if (!response.ok) {
          throw new Error(`搜索请求失败: ${response.status} ${response.statusText}`);
        }
        
        data = await response.json();
      } catch (fetchError) {
        // console.error('通用搜索请求失败:', fetchError);
        data = [];
      }
    }
    
    // console.log('搜索结果:', data);
    
    // 根据不同搜索引擎处理返回的数据结构
    let processedResults: SearchResult[] = [];
    if (currentEngine.provider.toLowerCase() === 'bing') {
      if (data.webPages?.value) {
        processedResults = data.webPages.value.map((result: any) => ({
          title: result.name,
          url: result.url,
          snippet: result.snippet,
          // 添加更多 Bing 特有的字段
          datePublished: result.datePublished,
          language: result.language,
          siteName: result.siteName
        }));
      }
    } else if (currentEngine.provider.toLowerCase() === 'tavily') {
      if (data.results) {
        processedResults = data.results.map((result: any) => ({
          title: result.title,
          url: result.url,
          snippet: result.content  // Tavily 使用 content 字段作为摘要
        }));
      }
    } else if (currentEngine.provider.toLowerCase() === 'exa') {
      if (data.results) {
        processedResults = data.results.map((result: any) => ({
          title: result.title || result.url,
          url: result.url,
          snippet: result.text || result.extract || ''  // EXA返回的是text字段
        }));
      }
    } else {
      if (data.organic_results) {
        processedResults = data.organic_results.map((result: any) => ({
          title: result.title,
          url: result.link,
          snippet: result.snippet
        }));
      }
    }
    
    return { results: processedResults };
  } catch (error) {
    // console.error('搜索错误:', error);
    return { results: [], error: error instanceof Error ? error : new Error('未知错误') };
  }
};