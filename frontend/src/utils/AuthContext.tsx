// # AuthContext.tsx

import { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'sonner'; // 导入 sonner 的 toast
import API from '@/config/api';
import { startSessionManager, stopSessionManager } from './session-manager';

// 不再使用axios实例，改用fetch API

// 用户信息接口
interface User {
  id: string;
  email: string;
  username?: string;
  display_name?: string;
  is_admin?: boolean;
  is_active?: boolean;
  role?: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
  last_login?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastAuthCheck, setLastAuthCheck] = useState(0);

  useEffect(() => {
    // 每次组件挂载或路由变化时，重置加载状态
    setLoading(true);

    // 检查是否需要重新登录
    const needRelogin = localStorage.getItem('needRelogin');
    if (needRelogin === 'true') {
      // 清除标志
      localStorage.removeItem('needRelogin');

      // 清除所有认证相关的本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('username');
      localStorage.removeItem('user');
      localStorage.removeItem('lastAuthCheck');

      // 更新认证状态
      setIsAuthenticated(false);
      setUser(null);
      setLoading(false);

      // 显示提示
      toast.error('登录已过期', {
        description: '请重新登录',
        position: 'top-center',
        duration: 4000,
      });

      return;
    }

    // 检查本地存储中是否有用户信息和token
    const token = localStorage.getItem('token');
    const userJson = localStorage.getItem('user');
    const storedLastAuthCheck = localStorage.getItem('lastAuthCheck');
    const lastCheckTime = storedLastAuthCheck ? parseInt(storedLastAuthCheck) : 0;
    const now = Date.now();
    const fiveMinutesInMs = 5 * 60 * 1000;

    // 如果本地有用户信息，先使用它来避免闪烁
    if (userJson) {
      try {
        const userData = JSON.parse(userJson);
        setUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('解析用户数据失败:', error);
      }
    }

    // 如果有token，验证其有效性
    // 但如果最近5分钟内已经验证过，则跳过验证
    if (token) {
      if (now - lastCheckTime < fiveMinutesInMs) {
        // console.log('使用缓存的认证状态，上次验证时间:', new Date(lastCheckTime).toLocaleTimeString());
        setLoading(false);
        return;
      }

      // 验证 token 有效性
      const validateToken = async () => {
        try {
          // console.log('验证token有效性...');
          const response = await fetch(API.AUTH.ME, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error('Token验证失败');
          }

          const data = await response.json();

          // 如果请求成功，说明 token 有效
          setUser(data);
          setIsAuthenticated(true);

          // 更新最后验证时间
          const currentTime = Date.now();
          setLastAuthCheck(currentTime);
          localStorage.setItem('lastAuthCheck', currentTime.toString());

          // 启动会话管理器
          startSessionManager();
        } catch (error) {
          // token 无效或过期
          console.error('Token 已过期或无效:', error);

          // 停止会话管理器
          stopSessionManager();

          // 清除所有认证相关的本地存储
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('username');
          localStorage.removeItem('user');
          localStorage.removeItem('lastAuthCheck');

          // 更新认证状态
          setIsAuthenticated(false);
          setUser(null);

          // 使用 toast 替代 alert，但只在真正需要时显示（避免每次路由变化都显示）
          if (userJson) {
            toast.error('登录已过期', {
              description: '请重新登录',
              position: 'top-center',
              duration: 4000,
            });
          }
        } finally {
          // 无论成功还是失败，都设置加载完成
          setLoading(false);
        }
      };

      validateToken();
    } else {
      // 如果没有token，直接设置为未认证状态
      setIsAuthenticated(false);
      setUser(null);
      setLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    try {
      // 添加更多调试信息
      // console.log('登录请求URL:', API.AUTH.SIGN_IN);
      // console.log('API配置:', API);
      // console.log('环境变量:', {
      //   VITE_API_BASE: import.meta.env.VITE_API_BASE,
      //   VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
      //   VITE_API_PREFIX: import.meta.env.VITE_API_PREFIX,
      //   VITE_API_VERSION: import.meta.env.VITE_API_VERSION,
      //   MODE: import.meta.env.MODE
      // });

      // 使用 toast 替代 alert
      // toast.info('登录请求', {
      //   description: `正在发送登录请求到: ${API.AUTH.SIGN_IN}`,
      //   position: 'top-center',
      //   duration: 4000,
      // });

      // 显示一个弹窗，显示登录请求的URL和参数
      // alert(`正在尝试登录\n邮箱: ${email}\n密码长度: ${password.length}`);

      // console.log('开始发送登录请求...');
      // console.log('请求参数:', { email, passwordLength: password.length });

      // 使用正确的 API 路径，确保不使用硬编码的 URL
      const loginUrl = API.AUTH.SIGN_IN;
      // console.log('使用 API 路径:', loginUrl);

      // 检查 URL 是否包含硬编码的 localhost
      // if (loginUrl.includes('localhost:8000')) {
      //   console.error('警告: 登录 URL 包含硬编码的 localhost:8000，这可能会导致问题');
      //   console.log('尝试使用相对路径替代');
      // }

      // 使用新的登录接口
      const response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          password
        })
      });

      // console.log('登录请求响应状态:', response.status);

      if (!response.ok) {
        console.error('登录失败，状态码:', response.status);

        // 尝试读取响应内容
        const errorText = await response.text();
        console.error('错误响应内容:', errorText);

        // 显示一个弹窗，显示登录失败的原因
        alert(`登录失败，状态码: ${response.status}\n错误信息: ${errorText}`);
        throw new Error(`登录失败: ${errorText}`);
      }

      // console.log('登录成功，正在解析响应数据...');
      const data = await response.json();
      // console.log('登录响应数据:', { token: data.token ? '已接收' : '未接收', refresh_token: data.refresh_token ? '已接收' : '未接收', user: data.user ? '已接收' : '未接收' });

      // 显示一个弹窗，显示登录成功的信息
      // alert('登录成功，已接收响应数据');

      const { token, refresh_token, user: userData } = data;

      // 保存认证信息
      localStorage.setItem('token', token);
      // 保存刷新令牌，用于会话刷新
      if (refresh_token) {
        localStorage.setItem('refreshToken', refresh_token);
      }
      localStorage.setItem('username', email); // 存储用户名
      setIsAuthenticated(true);

      // 保存用户信息
      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));

      // 更新最后验证时间
      const currentTime = Date.now();
      setLastAuthCheck(currentTime);
      localStorage.setItem('lastAuthCheck', currentTime.toString());

      // 启动会话管理器
      startSessionManager();

      // 如果用户未激活，显示提示信息
      if (userData && userData.is_active === false) {
        toast.info('账户等待激活', {
          description: '您的账户正在等待管理员激活',
          duration: 5000,
        });
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw new Error('登录失败，请检查邮箱和密码');
    }
  };

  const logout = async () => {
    // 停止会话管理器
    stopSessionManager();

    // 清除所有认证相关的本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('username'); // 移除用户名
    localStorage.removeItem('user'); // 如果存储了用户信息，也需要移除
    localStorage.removeItem('lastAuthCheck'); // 移除最后验证时间

    // 更新认证状态
    setIsAuthenticated(false);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};