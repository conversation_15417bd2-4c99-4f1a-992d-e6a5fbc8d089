import { useLLMStore } from '@/store/llmStore';

interface InspirationResponse {
  inspiration: string;
  error?: string;
}

/**
 * 移除文本中的think标签及其内容
 * @param text 原始文本
 * @returns 移除think标签后的文本
 */
function removeThinkTags(text: string): string {
  return text.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
}

/**
 * 提取最后一节总结内容
 * @param text 原始文本
 * @returns 总结内容
 */
function extractLastSummary(text: string): string {
  // 匹配最后一个总结部分
  const summaryMatch = text.match(/###\s*\*\*总结\*\*([\s\S]*?)(?=###|$)/);
  if (summaryMatch) {
    return summaryMatch[1].trim();
  }
  return text; // 如果没有找到总结部分，返回原文
}

/**
 * 随机选择一个fallback灵感
 * @returns 随机选择的灵感
 */
function getRandomFallback(): string {
  return fallbackInspirations[Math.floor(Math.random() * fallbackInspirations.length)];
}

/**
 * 根据最后一段响应内容生成灵感提示
 * @param lastResponse 最后一段响应内容
 * @returns 生成的灵感提示
 */
export async function generateInspiration(lastResponse: string): Promise<InspirationResponse> {
  try {
    // console.log('原始响应内容:', lastResponse);

    // 移除think标签内容
    const cleanResponse = removeThinkTags(lastResponse);
    // console.log('清理后的响应内容:', cleanResponse);

    // 提取最后一节总结内容
    const summaryContent = extractLastSummary(cleanResponse);
    // console.log('提取的总结内容:', summaryContent);

    // 获取一个随机的fallback灵感
    const fallbackInspiration = getRandomFallback();
    // console.log('随机选择的fallback灵感:', fallbackInspiration);
    
    // 构建提示词
    const prompt = `基于以下内容，生成一个简短的、富有启发性的灵感提示（不超过20个字）：

内容：${summaryContent}

要求：
1. 从总结中提取最具学术价值的关键点
2. 生成一个具体的、可操作的灵感
3. 不要过于抽象或笼统
4. 不要使用标点符号
5. 直接输出灵感，不要加引号或其他修饰`;

    // console.log('发送给模型的提示词:', prompt);

    // 获取当前选中的模型
    const { selectedModel } = useLLMStore.getState();
    if (!selectedModel) {
      throw new Error('No model selected');
    }

    // console.log('使用的模型:', selectedModel.id);

    // 准备请求体
    const requestBody = {
      model: selectedModel.id,
      messages: [
        {
          role: "system",
          content: "你是一个学术灵感生成助手，擅长从学术总结中提取关键点并生成具体的、可操作的灵感。请直接输出灵感，不要使用任何标签。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 1.0
    };

    // console.log('请求体:', JSON.stringify(requestBody, null, 2));

    // 处理 baseUrl 的格式
    let endpoint;
    if (selectedModel.baseUrl.endsWith('/')) {
      endpoint = `${selectedModel.baseUrl}chat/completions`;
    } else {
      endpoint = `${selectedModel.baseUrl}/v1/chat/completions`;
    }

    // console.log('API端点:', endpoint);

    // 获取当前选中模型的提供商信息
    const { providers } = useLLMStore.getState();
    const provider = providers.find((p: any) =>
      p.models.some((m: any) => m.id === selectedModel.id)
    );

    // 获取 API 密钥
    let apiKey = 'sk-YyiBg6DSn1Fc2KBNU6ZYtw'; // 默认密钥
    if (provider && provider.apiKey) {
      apiKey = provider.apiKey;
    }

    // 发送请求
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error('Failed to generate inspiration');
    }

    const data = await response.json();
    // console.log('模型原始响应:', JSON.stringify(data, null, 2));

    // 移除生成的灵感中可能存在的think标签
    const rawInspiration = data.choices[0].message.content.trim();
    // console.log('原始生成的灵感:', rawInspiration);

    const inspiration = removeThinkTags(rawInspiration);
    // console.log('最终清理后的灵感:', inspiration);

    // 将fallback灵感和生成的灵感融合在一起
    const combinedInspiration = `${fallbackInspiration}，${inspiration}`;
    // console.log('融合后的灵感:', combinedInspiration);

    return { inspiration: combinedInspiration };
  } catch (error) {
    // console.error('生成灵感时发生错误:', error);
    return {
      inspiration: getRandomFallback(),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// 预设的灵感提示列表，作为备用
export const fallbackInspirations = [
  "换个角度思考",
  "深入探索本质",
  "建立新的联系",
  "突破思维定式",
  "寻找隐藏关联",
  "尝试逆向思维",
  "跳出框架思考",
  "寻找共性规律",
  "探索未知领域",
  "建立知识桥梁"
]; 