import { Message } from '@/store/academicStore';

// Memory service API endpoint
const MEMORY_SERVICE_URL = 'http://localhost:8011';

// Interface for memory input
interface MemoryInput {
  messages: Message[];
  user_id: string;
  metadata?: {
    type?: 'chat' | 'search';
    query?: string;
    results?: any[];
    [key: string]: any;
  };
}

/**
 * Add a memory to the memory service
 */
export async function addMemory(input: MemoryInput): Promise<void> {
  try {
    const response = await fetch(`${MEMORY_SERVICE_URL}/memories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(input),
    });

    if (!response.ok) {
      throw new Error(`Memory service error: ${response.status}`);
    }

    const result = await response.json();
    // console.log('Memory added successfully:', result);
  } catch (error) {
    // console.error('Failed to add memory:', error);
    // Don't throw the error to prevent disrupting the main flow
  }
}

/**
 * Search memories from the memory service
 */
export async function searchMemories(userId: string, query: string, limit: number = 5): Promise<any> {
  try {
    const response = await fetch(`${MEMORY_SERVICE_URL}/memories/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        user_id: userId,
        limit,
      }),
    });

    if (!response.ok) {
      throw new Error(`Memory service error: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    // console.error('Failed to search memories:', error);
    return { memories: [], count: 0 };
  }
} 