// 通知类型定义，与后端保持一致
export enum NotificationType {
  MENTION = 'mention',      // @ 提醒
  LIKE = 'like',           // 点赞通知
  COMMENT = 'comment',     // 评论通知
  FOLLOW = 'follow',       // 关注通知
  CHANNEL_POST = 'channel_post' // 关注的频道有新帖子
}

export interface NotificationData {
  id: string;
  type: NotificationType;
  content: string;
  sender_name: string;
  sender_avatar?: string;
  sender_username?: string;
  target_post_id?: string;
  source_post_id?: string;
  source_comment_id?: string;
  channel_id?: string;
  is_read: boolean;
  created_at: string;
}

export interface NotificationNavigationParams {
  postId?: string;
  commentId?: string;
  channelId?: string;
  userId?: string;
}