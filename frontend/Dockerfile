# Build stage
FROM node:20-alpine as build

WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm

# 复制 package.json 和 lock 文件
COPY package.json pnpm-lock.yaml ./

# 使用 pnpm 安装依赖
RUN pnpm install

# 复制应用代码
COPY . .

# 复制 Docker 环境变量文件
COPY .env.docker .env.production

# 设置生产环境 API 基础路径
ENV VITE_API_BASE=http://localhost:8000/v1/api

# 构建应用 (跳过TypeScript检查)
RUN pnpm vite build

# Production stage - use serve
FROM node:20-alpine as production
WORKDIR /app
RUN npm install -g serve
COPY --from=build /app/dist ./dist
EXPOSE 8080
CMD ["serve", "-s", "dist", "-l", "8080"]

# Development stage (unchanged)
FROM node:20-alpine as development
WORKDIR /app
RUN npm install -g pnpm
COPY package.json pnpm-lock.yaml ./
RUN pnpm install
COPY .env.docker .env.development
EXPOSE 5173
CMD ["pnpm", "dev", "--host", "0.0.0.0", "--port", "5173"]
