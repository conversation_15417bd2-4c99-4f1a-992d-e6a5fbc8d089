{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@langchain/core": "^0.3.55", "@langchain/langgraph-sdk": "^0.0.105", "@lightenna/react-mermaid-diagram": "^1.0.17", "@mendable/firecrawl-js": "^1.24.0", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@tailwindcss/vite": "^4.1.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.7.4", "katex": "^0.16.22", "lucide-react": "^0.492.0", "markdown-docx": "^1.1.2", "next-themes": "^0.4.6", "pdfjs-dist": "^5.1.91", "plantuml-encoder": "^1.4.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.5.1", "react-syntax-highlighter": "^15.6.1", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.3", "styled-components": "^6.1.17", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "vaul": "^1.1.2", "wouter": "^3.7.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}