version: '3.8'

services:
  # 后端 API 服务
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: stooges-backend
    restart: always
    networks:
      - app-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # PDF 服务
  pdf_service:
    build:
      context: ../pdf_service
      dockerfile: Dockerfile
    container_name: stooges-pdf-service
    restart: always
    networks:
      - app-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Deep Search 服务
  deep_search:
    build:
      context: ../deep_search
      dockerfile: Dockerfile
    container_name: stooges-deep-search
    restart: always
    environment:
      - BACKEND_API_BASE=http://backend:8000/v1/api
      - MODEL_CONFIG_REFRESH_INTERVAL=300
      - PYTHONUNBUFFERED=1
    networks:
      - app-network
    depends_on:
      - backend
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # 前端服务 - 只负责构建
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: build
    container_name: stooges-frontend-builder
    volumes:
      - frontend-build:/app/dist

  # Nginx 服务 - 作为统一入口
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: stooges-nginx
    restart: always
    ports:
      - "8080:80"
    depends_on:
      - backend
      - pdf_service
      - deep_search
      - frontend
    volumes:
      - frontend-build:/usr/share/nginx/html
    environment:
      # 支持局域网访问的Supabase主机配置
      # 默认使用host.docker.internal，可通过环境变量覆盖
      - SUPABASE_HOST=${SUPABASE_HOST:-host.docker.internal}
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  frontend-build:
